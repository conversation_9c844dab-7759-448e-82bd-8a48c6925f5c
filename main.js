const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
    // Create the browser window
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            webSecurity: true
        },
        show: false,
        titleBarStyle: 'default',
        frame: true,
        resizable: true,
        maximizable: true,
        minimizable: true,
        closable: true
    });

    // Load the HTML file
    const htmlPath = path.join(__dirname, '🛡️-CYBER-SENTINEL-PROFESSIONAL.html');
    
    if (fs.existsSync(htmlPath)) {
        mainWindow.loadFile(htmlPath);
    } else {
        // Fallback: create a simple error page
        const errorHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>خطأ - Cyber Sentinel Pro</title>
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    background: #1a1a2e; 
                    color: white; 
                    text-align: center; 
                    padding: 50px; 
                }
                .error { 
                    background: #ff4444; 
                    padding: 20px; 
                    border-radius: 10px; 
                    margin: 20px auto; 
                    max-width: 500px; 
                }
            </style>
        </head>
        <body>
            <h1>🛡️ Cyber Sentinel Pro</h1>
            <div class="error">
                <h2>❌ خطأ في التحميل</h2>
                <p>لم يتم العثور على ملف التطبيق الرئيسي</p>
                <p>يرجى التأكد من وجود الملف: 🛡️-CYBER-SENTINEL-PROFESSIONAL.html</p>
            </div>
        </body>
        </html>`;
        
        mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(errorHtml));
    }

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // Show splash screen effect
        setTimeout(() => {
            if (mainWindow) {
                mainWindow.focus();
            }
        }, 500);
    });

    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Prevent navigation to external sites
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });

    // Set window title
    mainWindow.setTitle('🛡️ Cyber Sentinel Pro - Professional Edition');
}

// Create application menu
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                {
                    label: 'تكبير الشاشة',
                    accelerator: 'F11',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.setFullScreen(!mainWindow.isFullScreen());
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        if (mainWindow) {
                            const currentZoom = mainWindow.webContents.getZoomLevel();
                            mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
                        }
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        if (mainWindow) {
                            const currentZoom = mainWindow.webContents.getZoomLevel();
                            mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
                        }
                    }
                },
                {
                    label: 'حجم طبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.setZoomLevel(0);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'أدوات المطور',
                    accelerator: 'F12',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.toggleDevTools();
                        }
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'دليل الاستخدام',
                    click: () => {
                        const guidePath = path.join(__dirname, '📖-PROFESSIONAL-EDITION-GUIDE.txt');
                        if (fs.existsSync(guidePath)) {
                            shell.openPath(guidePath);
                        } else {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'دليل الاستخدام',
                                message: 'لم يتم العثور على دليل الاستخدام',
                                detail: 'يرجى التأكد من وجود الملف: 📖-PROFESSIONAL-EDITION-GUIDE.txt'
                            });
                        }
                    }
                },
                {
                    label: 'تقرير الأمان',
                    click: () => {
                        const reportPath = path.join(__dirname, '🔒-SECURITY-REPORT.txt');
                        if (fs.existsSync(reportPath)) {
                            shell.openPath(reportPath);
                        } else {
                            dialog.showMessageBox(mainWindow, {
                                type: 'info',
                                title: 'تقرير الأمان',
                                message: 'لم يتم العثور على تقرير الأمان',
                                detail: 'يرجى التأكد من وجود الملف: 🔒-SECURITY-REPORT.txt'
                            });
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'حول البرنامج',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول Cyber Sentinel Pro',
                            message: '🛡️ Cyber Sentinel Pro - Professional Edition',
                            detail: `النسخة: 1.0.0
المطور: CyberSentinel Team
الموقع: https://cybersentinel.pro
البريد: <EMAIL>

أداة أمان سيبراني متقدمة مع مراقبة حقيقية للنظام والشبكة.

© 2024 CyberSentinel Team. جميع الحقوق محفوظة.`
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
    createWindow();
    createMenu();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
    // For development, you might want to ignore certificate errors
    // In production, you should handle this properly
    event.preventDefault();
    callback(true);
});

// Prevent navigation to external protocols
app.on('web-contents-created', (event, contents) => {
    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });
});

// Handle app updates (if using electron-updater)
const { autoUpdater } = require('electron-updater');

autoUpdater.checkForUpdatesAndNotify();

autoUpdater.on('update-available', () => {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'تحديث متاح',
        message: 'يتوفر تحديث جديد للبرنامج',
        detail: 'سيتم تنزيل التحديث في الخلفية وتثبيته عند إعادة تشغيل البرنامج.'
    });
});

autoUpdater.on('update-downloaded', () => {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'تحديث جاهز',
        message: 'تم تنزيل التحديث بنجاح',
        detail: 'سيتم تطبيق التحديث عند إعادة تشغيل البرنامج.',
        buttons: ['إعادة تشغيل الآن', 'لاحقاً']
    }).then((result) => {
        if (result.response === 0) {
            autoUpdater.quitAndInstall();
        }
    });
});
