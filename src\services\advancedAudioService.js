// نظام صوتي متقدم مع تأثيرات تفاعلية
class AdvancedAudioService {
  constructor() {
    this.audioContext = null;
    this.masterGain = null;
    this.backgroundMusic = null;
    this.soundEffects = new Map();
    this.isInitialized = false;
    this.currentTheme = 'cyber';
    this.volume = 0.3;
    this.effectsVolume = 0.5;
    this.musicVolume = 0.2;
    
    // مولدات الصوت
    this.oscillators = new Map();
    this.filters = new Map();
    this.delays = new Map();
    this.reverbs = new Map();
    
    // أنماط الموسيقى
    this.musicPatterns = {
      cyber: {
        tempo: 120,
        scale: [220, 246.94, 261.63, 293.66, 329.63, 369.99, 415.30], // A minor
        bassline: [110, 123.47, 130.81, 146.83],
        rhythm: [1, 0, 1, 0, 1, 0, 1, 0]
      },
      alert: {
        tempo: 140,
        scale: [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88],
        bassline: [130.81, 146.83, 164.81, 174.61],
        rhythm: [1, 1, 0, 1, 1, 0, 1, 0]
      },
      stealth: {
        tempo: 80,
        scale: [196.00, 220.00, 246.94, 261.63, 293.66, 329.63, 369.99],
        bassline: [98.00, 110.00, 123.47, 130.81],
        rhythm: [1, 0, 0, 1, 0, 0, 1, 0]
      }
    };

    this.initializeAudio();
  }

  // تهيئة النظام الصوتي
  async initializeAudio() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // إنشاء العقد الرئيسية
      this.masterGain = this.audioContext.createGain();
      this.masterGain.connect(this.audioContext.destination);
      this.masterGain.gain.value = this.volume;

      // إنشاء مولدات التأثيرات
      await this.createAudioEffects();
      
      // إنشاء الأصوات المحددة مسبقاً
      this.createPredefinedSounds();
      
      this.isInitialized = true;
      console.log('🎵 تم تهيئة النظام الصوتي المتقدم');
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة النظام الصوتي:', error);
    }
  }

  // إنشاء تأثيرات صوتية
  async createAudioEffects() {
    // Reverb (صدى)
    const reverbBuffer = await this.createReverbBuffer(2, 2, false);
    const reverb = this.audioContext.createConvolver();
    reverb.buffer = reverbBuffer;
    this.reverbs.set('main', reverb);

    // Delay (تأخير)
    const delay = this.audioContext.createDelay(1.0);
    delay.delayTime.value = 0.3;
    const delayFeedback = this.audioContext.createGain();
    delayFeedback.gain.value = 0.4;
    delay.connect(delayFeedback);
    delayFeedback.connect(delay);
    this.delays.set('main', { delay, feedback: delayFeedback });

    // Filter (مرشح)
    const filter = this.audioContext.createBiquadFilter();
    filter.type = 'lowpass';
    filter.frequency.value = 800;
    filter.Q.value = 10;
    this.filters.set('main', filter);
  }

  // إنشاء buffer للصدى
  async createReverbBuffer(duration, decay, reverse) {
    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const impulse = this.audioContext.createBuffer(2, length, sampleRate);
    
    for (let channel = 0; channel < 2; channel++) {
      const channelData = impulse.getChannelData(channel);
      for (let i = 0; i < length; i++) {
        const n = reverse ? length - i : i;
        channelData[i] = (Math.random() * 2 - 1) * Math.pow(1 - n / length, decay);
      }
    }
    
    return impulse;
  }

  // إنشاء الأصوات المحددة مسبقاً
  createPredefinedSounds() {
    // صوت تنبيه الأمان
    this.soundEffects.set('security_alert', {
      type: 'synthesized',
      generator: () => this.createSecurityAlert()
    });

    // صوت فحص الشبكة
    this.soundEffects.set('network_scan', {
      type: 'synthesized',
      generator: () => this.createNetworkScanSound()
    });

    // صوت اكتشاف تهديد
    this.soundEffects.set('threat_detected', {
      type: 'synthesized',
      generator: () => this.createThreatSound()
    });

    // صوت تسجيل دخول ناجح
    this.soundEffects.set('login_success', {
      type: 'synthesized',
      generator: () => this.createSuccessSound()
    });

    // صوت خطأ
    this.soundEffects.set('error', {
      type: 'synthesized',
      generator: () => this.createErrorSound()
    });

    // صوت كتابة
    this.soundEffects.set('typing', {
      type: 'synthesized',
      generator: () => this.createTypingSound()
    });

    // صوت إشعار
    this.soundEffects.set('notification', {
      type: 'synthesized',
      generator: () => this.createNotificationSound()
    });
  }

  // تشغيل الموسيقى الخلفية
  startBackgroundMusic(theme = 'cyber') {
    if (!this.isInitialized) return;

    this.stopBackgroundMusic();
    this.currentTheme = theme;
    
    const pattern = this.musicPatterns[theme];
    if (!pattern) return;

    // إنشاء مولدات الموسيقى
    this.createMusicGenerators(pattern);
    
    console.log(`🎵 بدء تشغيل موسيقى ${theme}`);
  }

  // إنشاء مولدات الموسيقى
  createMusicGenerators(pattern) {
    const { tempo, scale, bassline, rhythm } = pattern;
    const beatDuration = 60 / tempo; // مدة النبضة بالثواني

    // مولد اللحن الرئيسي
    this.createMelodyGenerator(scale, rhythm, beatDuration);
    
    // مولد الباس
    this.createBassGenerator(bassline, beatDuration);
    
    // مولد الإيقاع
    this.createRhythmGenerator(beatDuration);
  }

  // مولد اللحن الرئيسي
  createMelodyGenerator(scale, rhythm, beatDuration) {
    let beatIndex = 0;
    let noteIndex = 0;

    const playNextNote = () => {
      if (!this.backgroundMusic) return;

      if (rhythm[beatIndex % rhythm.length]) {
        const frequency = scale[noteIndex % scale.length];
        this.playTone(frequency, beatDuration * 0.8, 'sawtooth', 0.1);
        noteIndex++;
      }

      beatIndex++;
      setTimeout(playNextNote, beatDuration * 1000);
    };

    this.backgroundMusic = { active: true };
    playNextNote();
  }

  // مولد الباس
  createBassGenerator(bassline, beatDuration) {
    let beatIndex = 0;

    const playNextBass = () => {
      if (!this.backgroundMusic) return;

      if (beatIndex % 4 === 0) { // كل 4 نبضات
        const frequency = bassline[Math.floor(beatIndex / 4) % bassline.length];
        this.playTone(frequency, beatDuration * 2, 'triangle', 0.15);
      }

      beatIndex++;
      setTimeout(playNextBass, beatDuration * 1000);
    };

    setTimeout(playNextBass, beatDuration * 1000 / 2); // بدء متأخر قليلاً
  }

  // مولد الإيقاع
  createRhythmGenerator(beatDuration) {
    let beatIndex = 0;

    const playNextBeat = () => {
      if (!this.backgroundMusic) return;

      // طبلة قوية كل 4 نبضات
      if (beatIndex % 4 === 0) {
        this.playNoise(beatDuration * 0.1, 0.2, 'kick');
      }
      
      // طبلة خفيفة في النبضات الفردية
      if (beatIndex % 2 === 1) {
        this.playNoise(beatDuration * 0.05, 0.1, 'snare');
      }

      beatIndex++;
      setTimeout(playNextBeat, beatDuration * 1000);
    };

    setTimeout(playNextBeat, beatDuration * 1000 / 4); // بدء متأخر
  }

  // تشغيل نغمة
  playTone(frequency, duration, waveType = 'sine', volume = 0.1) {
    if (!this.isInitialized) return;

    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();
    
    oscillator.type = waveType;
    oscillator.frequency.value = frequency;
    
    gainNode.gain.value = volume * this.musicVolume;
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
    
    oscillator.connect(gainNode);
    gainNode.connect(this.filters.get('main'));
    this.filters.get('main').connect(this.masterGain);
    
    oscillator.start();
    oscillator.stop(this.audioContext.currentTime + duration);
  }

  // تشغيل ضوضاء (للإيقاع)
  playNoise(duration, volume, type = 'white') {
    if (!this.isInitialized) return;

    const bufferSize = this.audioContext.sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
    const data = buffer.getChannelData(0);

    // توليد الضوضاء
    for (let i = 0; i < bufferSize; i++) {
      if (type === 'kick') {
        // صوت طبلة قوية
        data[i] = (Math.random() * 2 - 1) * Math.exp(-i / (bufferSize * 0.1));
      } else if (type === 'snare') {
        // صوت طبلة خفيفة
        data[i] = (Math.random() * 2 - 1) * Math.exp(-i / (bufferSize * 0.3));
      } else {
        // ضوضاء بيضاء
        data[i] = Math.random() * 2 - 1;
      }
    }

    const source = this.audioContext.createBufferSource();
    const gainNode = this.audioContext.createGain();
    
    source.buffer = buffer;
    gainNode.gain.value = volume * this.musicVolume;
    
    source.connect(gainNode);
    gainNode.connect(this.masterGain);
    
    source.start();
  }

  // إيقاف الموسيقى الخلفية
  stopBackgroundMusic() {
    if (this.backgroundMusic) {
      this.backgroundMusic = null;
      console.log('🔇 تم إيقاف الموسيقى الخلفية');
    }
  }

  // تشغيل تأثير صوتي
  playEffect(effectName, options = {}) {
    if (!this.isInitialized) return;

    const effect = this.soundEffects.get(effectName);
    if (!effect) {
      console.warn(`تأثير صوتي غير موجود: ${effectName}`);
      return;
    }

    if (effect.type === 'synthesized') {
      effect.generator(options);
    }
  }

  // إنشاء صوت تنبيه الأمان
  createSecurityAlert() {
    const frequencies = [800, 1000, 800, 1000];
    frequencies.forEach((freq, index) => {
      setTimeout(() => {
        this.playTone(freq, 0.2, 'square', 0.3);
      }, index * 200);
    });
  }

  // إنشاء صوت فحص الشبكة
  createNetworkScanSound() {
    // صوت متصاعد يحاكي فحص الشبكة
    for (let i = 0; i < 10; i++) {
      setTimeout(() => {
        const frequency = 200 + (i * 50);
        this.playTone(frequency, 0.1, 'sawtooth', 0.1);
      }, i * 50);
    }
  }

  // إنشاء صوت اكتشاف تهديد
  createThreatSound() {
    // صوت تحذيري منخفض ومخيف
    this.playTone(150, 1, 'triangle', 0.2);
    setTimeout(() => {
      this.playTone(100, 0.5, 'square', 0.3);
    }, 200);
  }

  // إنشاء صوت النجاح
  createSuccessSound() {
    // نغمة صاعدة إيجابية
    const notes = [261.63, 329.63, 392.00]; // C, E, G
    notes.forEach((note, index) => {
      setTimeout(() => {
        this.playTone(note, 0.3, 'sine', 0.2);
      }, index * 100);
    });
  }

  // إنشاء صوت الخطأ
  createErrorSound() {
    // نغمة هابطة سلبية
    const notes = [400, 350, 300];
    notes.forEach((note, index) => {
      setTimeout(() => {
        this.playTone(note, 0.2, 'square', 0.2);
      }, index * 100);
    });
  }

  // إنشاء صوت الكتابة
  createTypingSound() {
    // نقرة قصيرة تحاكي الكتابة
    this.playNoise(0.05, 0.1, 'snare');
  }

  // إنشاء صوت الإشعار
  createNotificationSound() {
    // نغمة لطيفة للإشعارات
    this.playTone(523.25, 0.2, 'sine', 0.15); // C5
    setTimeout(() => {
      this.playTone(659.25, 0.3, 'sine', 0.15); // E5
    }, 150);
  }

  // تغيير مستوى الصوت الرئيسي
  setMasterVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    if (this.masterGain) {
      this.masterGain.gain.value = this.volume;
    }
  }

  // تغيير مستوى صوت الموسيقى
  setMusicVolume(volume) {
    this.musicVolume = Math.max(0, Math.min(1, volume));
  }

  // تغيير مستوى صوت التأثيرات
  setEffectsVolume(volume) {
    this.effectsVolume = Math.max(0, Math.min(1, volume));
  }

  // تغيير نمط الموسيقى حسب حالة الأمان
  adaptMusicToThreatLevel(threatLevel) {
    switch (threatLevel) {
      case 'low':
        this.startBackgroundMusic('cyber');
        break;
      case 'medium':
        this.startBackgroundMusic('alert');
        break;
      case 'high':
      case 'critical':
        this.startBackgroundMusic('alert');
        // إضافة تأثيرات تحذيرية
        setInterval(() => {
          if (threatLevel === 'critical') {
            this.playEffect('security_alert');
          }
        }, 5000);
        break;
      default:
        this.startBackgroundMusic('stealth');
    }
  }

  // تشغيل تأثير صوتي للأحداث
  playEventSound(eventType, data = {}) {
    switch (eventType) {
      case 'login_success':
        this.playEffect('login_success');
        break;
      case 'login_failed':
        this.playEffect('error');
        break;
      case 'scan_started':
        this.playEffect('network_scan');
        break;
      case 'threat_detected':
        this.playEffect('threat_detected');
        break;
      case 'notification':
        this.playEffect('notification');
        break;
      case 'typing':
        this.playEffect('typing');
        break;
      case 'scan_complete':
        this.playEffect('success');
        break;
      default:
        console.log(`حدث صوتي غير معروف: ${eventType}`);
    }
  }

  // الحصول على حالة النظام الصوتي
  getAudioStatus() {
    return {
      initialized: this.isInitialized,
      masterVolume: this.volume,
      musicVolume: this.musicVolume,
      effectsVolume: this.effectsVolume,
      currentTheme: this.currentTheme,
      backgroundMusicActive: !!this.backgroundMusic,
      availableEffects: Array.from(this.soundEffects.keys()),
      audioContextState: this.audioContext?.state
    };
  }

  // تنظيف الموارد
  dispose() {
    this.stopBackgroundMusic();
    
    if (this.audioContext) {
      this.audioContext.close();
    }
    
    this.soundEffects.clear();
    this.oscillators.clear();
    this.filters.clear();
    this.delays.clear();
    this.reverbs.clear();
    
    console.log('🔇 تم تنظيف النظام الصوتي');
  }
}

// إنشاء instance واحد من الخدمة
export const advancedAudioService = new AdvancedAudioService();
export default AdvancedAudioService;
