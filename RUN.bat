@echo off
title Cyber Sentinel Pro
color 0A

echo.
echo 🛡️ CYBER SENTINEL PRO - SecOps Edition
echo =====================================
echo.

REM Simple check and install
if not exist "node_modules" (
    echo [INSTALL] Installing dependencies...
    call npm install
    if errorlevel 1 (
        echo [ERROR] Installation failed!
        pause
        exit /b 1
    )
)

echo [INFO] Starting Cyber Sentinel Pro...
echo [INFO] Login: admin / JaMaL@123
echo [INFO] URL: http://localhost:3000
echo.

REM Open browser
start "" timeout /t 8 /nobreak >nul && start http://localhost:3000

REM Start app
call npm start

pause
