@echo off
title Cyber Sentinel Pro - SecOps Edition
color 0A

echo.
echo  ██████╗██╗   ██╗██████╗ ███████╗██████╗     ███████╗███████╗███╗   ██╗████████╗██╗███╗   ██╗███████╗██╗     
echo ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗    ██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║████╗  ██║██╔════╝██║     
echo ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝    ███████╗█████╗  ██╔██╗ ██║   ██║   ██║██╔██╗ ██║█████╗  ██║     
echo ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗    ╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██║╚██╗██║██╔══╝  ██║     
echo ╚██████╗   ██║   ██████╔╝███████╗██║  ██║    ███████║███████╗██║ ╚████║   ██║   ██║██║ ╚████║███████╗███████╗
echo  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝╚══════╝╚══════╝
echo.
echo                                    SecOps Edition v1.0.0
echo                              Advanced Cybersecurity Testing Platform
echo.
echo ===============================================================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from: https://nodejs.org/
    echo [INFO] Recommended version: 18.x or higher
    pause
    exit /b 1
)

REM Check if Python is installed
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo [INFO] Please install Python from: https://python.org/
    echo [INFO] Recommended version: 3.8 or higher
    pause
    exit /b 1
)

REM Check if npm is available
echo [INFO] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available
    echo [INFO] npm should be installed with Node.js
    pause
    exit /b 1
)

echo [SUCCESS] All prerequisites are installed!
echo.

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating .env file from template...
    copy ".env.example" ".env" >nul
    echo [SUCCESS] .env file created. Please review and update the configuration.
    echo.
)

REM Install Node.js dependencies
echo [INFO] Installing Node.js dependencies...
echo [INFO] This may take a few minutes on first run...
npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Install Python dependencies
echo [INFO] Installing Python dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [WARNING] Some Python dependencies may have failed to install
    echo [INFO] You can install them manually later if needed
)

REM Create necessary directories
echo [INFO] Creating application directories...
if not exist "uploads" mkdir uploads
if not exist "reports" mkdir reports
if not exist "logs" mkdir logs
if not exist "temp" mkdir temp
if not exist "backups" mkdir backups
if not exist "ssl" mkdir ssl
if not exist "tools" mkdir tools

echo [SUCCESS] Directory structure created!
echo.

REM Setup database (optional)
echo [INFO] Do you want to setup the database now? (y/n)
set /p setup_db="Enter your choice: "
if /i "%setup_db%"=="y" (
    echo [INFO] Setting up database...
    node scripts/setup-database.js
    if %errorlevel% neq 0 (
        echo [WARNING] Database setup failed. You can run it manually later.
    )
)

echo.
echo ===============================================================================
echo                              STARTUP OPTIONS
echo ===============================================================================
echo.
echo 1. Start Development Server (Frontend + Backend)
echo 2. Start Production Server
echo 3. Start Electron Desktop App
echo 4. Start Backend Only
echo 5. Start Frontend Only
echo 6. Run Tests
echo 7. Build for Production
echo 8. Setup Database
echo 9. Exit
echo.

:menu
set /p choice="Enter your choice (1-9): "

if "%choice%"=="1" (
    echo [INFO] Starting development server...
    echo [INFO] Frontend will be available at: http://localhost:3000
    echo [INFO] Backend will be available at: http://localhost:5000
    echo [INFO] Press Ctrl+C to stop the server
    echo.
    npm run dev
    goto end
)

if "%choice%"=="2" (
    echo [INFO] Building and starting production server...
    npm run build
    npm run start:prod
    goto end
)

if "%choice%"=="3" (
    echo [INFO] Starting Electron desktop application...
    npm run electron
    goto end
)

if "%choice%"=="4" (
    echo [INFO] Starting backend server only...
    echo [INFO] Server will be available at: http://localhost:5000
    npm run server
    goto end
)

if "%choice%"=="5" (
    echo [INFO] Starting frontend only...
    echo [INFO] Frontend will be available at: http://localhost:3000
    npm start
    goto end
)

if "%choice%"=="6" (
    echo [INFO] Running tests...
    npm test
    goto menu
)

if "%choice%"=="7" (
    echo [INFO] Building for production...
    npm run build
    echo [SUCCESS] Build completed! Files are in the 'build' directory.
    goto menu
)

if "%choice%"=="8" (
    echo [INFO] Setting up database...
    node scripts/setup-database.js
    goto menu
)

if "%choice%"=="9" (
    goto end
)

echo [ERROR] Invalid choice. Please enter a number between 1-9.
goto menu

:end
echo.
echo ===============================================================================
echo                              IMPORTANT INFORMATION
echo ===============================================================================
echo.
echo Default Admin Credentials:
echo   Username: admin
echo   Password: JaMaL@123
echo.
echo Security Notes:
echo   - Change the default password after first login
echo   - Setup two-factor authentication
echo   - Review the .env file for security settings
echo   - This tool is for authorized security testing only
echo.
echo Documentation:
echo   - README.md - Complete setup and usage guide
echo   - /docs - Additional documentation
echo   - GitHub: https://github.com/cybersentinel/cyber-sentinel-pro
echo.
echo Support:
echo   - Email: <EMAIL>
echo   - Telegram: @CyberSentinelSupport
echo.
echo ===============================================================================
echo                                  WARNING
echo ===============================================================================
echo.
echo This software is intended for authorized security testing and educational
echo purposes only. Unauthorized use against systems you do not own or have
echo explicit permission to test is illegal and unethical.
echo.
echo The developers are not responsible for any misuse of this software.
echo Use responsibly and in accordance with all applicable laws.
echo.
echo ===============================================================================
echo.
pause
