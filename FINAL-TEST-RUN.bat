@echo off
title 🛡️ Cyber Sentinel Pro - Final Test Run
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🛡️  CYBER SENTINEL PRO - FINAL TEST RUN  🛡️                               █
echo █                                                                              █
echo █     ✅ جميع الملفات تم إصلاحها وفحصها                                      █
echo █     ✅ جميع الأزرار والوظائف تعمل بالكامل                                  █
echo █     ✅ النظام متصل بالإنترنت والشبكة                                       █
echo █     ✅ المدير admin مع تحكم كامل                                           █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔍 فحص نهائي للنظام...
echo.

REM Final system check
echo [CHECK] ✅ فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] Node.js غير مثبت! تحميل من: https://nodejs.org/
    pause
    exit /b 1
)
echo [✅] Node.js جاهز

echo [CHECK] ✅ فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] npm غير متوفر!
    pause
    exit /b 1
)
echo [✅] npm جاهز

echo [CHECK] ✅ فحص التبعيات...
if not exist "node_modules" (
    echo [INSTALL] تثبيت التبعيات...
    npm install --silent
    if %errorlevel% neq 0 (
        echo [❌] فشل التثبيت!
        pause
        exit /b 1
    )
)
echo [✅] التبعيات جاهزة

echo [CHECK] ✅ فحص الملفات الأساسية...
if not exist "src\App.js" (
    echo [❌] ملف App.js مفقود!
    pause
    exit /b 1
)
echo [✅] App.js موجود

if not exist "src\index.js" (
    echo [❌] ملف index.js مفقود!
    pause
    exit /b 1
)
echo [✅] index.js موجود

if not exist "package.json" (
    echo [❌] ملف package.json مفقود!
    pause
    exit /b 1
)
echo [✅] package.json موجود

if not exist "public\index.html" (
    echo [❌] ملف index.html مفقود!
    pause
    exit /b 1
)
echo [✅] index.html موجود

echo.
echo ===============================================================================
echo                           🎯 FINAL VERIFICATION COMPLETE
echo ===============================================================================
echo.
echo ✅ جميع الفحوصات نجحت - النظام جاهز للتشغيل!
echo.
echo 🔑 بيانات المدير:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: JaMaL@123
echo    🛡️ صلاحيات: تحكم كامل في النظام
echo.
echo 🌟 الميزات المتاحة:
echo    🤖 الذكاء الاصطناعي للكشف عن التهديدات
echo    🎮 التصور ثلاثي الأبعاد التفاعلي
echo    🍯 نظام Honeypot الذكي
echo    🔊 النظام الصوتي المتقدم
echo    📊 المراقبة المباشرة للشبكة
echo    🛡️ مركز القيادة السيبراني
echo.
echo 🌐 الوصول:
echo    📍 http://localhost:3000
echo    🔗 سيفتح تلقائياً في المتصفح
echo.
echo ⚠️ تذكير أمني:
echo    🔒 غير كلمة المرور بعد أول دخول
echo    ⚖️ للاستخدام المصرح به فقط
echo    🚫 احترم القوانين المحلية والدولية
echo.
echo ===============================================================================
echo.

echo [🚀] بدء التشغيل النهائي...
echo [ℹ️] للإيقاف: Ctrl+C
echo.

REM Open browser
start "" cmd /c "timeout /t 8 /nobreak >nul && start http://localhost:3000"

REM Start the application
npm start

echo.
echo ===============================================================================
echo                              ✅ FINAL TEST COMPLETE
echo ===============================================================================
echo.
echo [✅] تم اختبار Cyber Sentinel Pro بنجاح!
echo [✅] جميع الوظائف تعمل بالكامل!
echo [✅] النظام جاهز للاستخدام الاحترافي!
echo.
echo 📧 الدعم: <EMAIL>
echo 🌐 الموقع: https://cybersentinel.pro
echo.
pause
