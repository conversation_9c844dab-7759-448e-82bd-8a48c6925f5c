@echo off
title Terminal Test - Cyber Sentinel Pro
color 0A

REM Test 1: Basic echo and pause
echo Test 1: Basic terminal test
echo This terminal should NOT close automatically
echo.
echo Press any key to continue to Test 2...
pause >nul

cls
echo Test 2: System information
echo.
echo Operating System: %OS%
echo Computer Name: %COMPUTERNAME%
echo Current Directory: %CD%
echo.
echo Press any key to continue to Test 3...
pause >nul

cls
echo Test 3: Command testing
echo.
echo Testing basic commands...
echo.

echo Checking date:
date /t

echo Checking time:
time /t

echo Checking directory:
dir . /b | findstr /i ".bat"

echo.
echo Press any key to continue to Test 4...
pause >nul

cls
echo Test 4: Node.js detection
echo.

echo Checking if Node.js is available...
where node >nul 2>&1
if errorlevel 1 (
    echo Node.js: NOT FOUND
    echo This is likely why the build scripts fail
    echo.
    echo SOLUTION: Install Node.js from https://nodejs.org
) else (
    echo Node.js: FOUND
    echo Location:
    where node
    echo Version:
    node --version 2>nul
)

echo.
echo Checking if npm is available...
where npm >nul 2>&1
if errorlevel 1 (
    echo npm: NOT FOUND
    echo This is likely why the build scripts fail
) else (
    echo npm: FOUND
    echo Location:
    where npm
    echo Version:
    npm --version 2>nul
)

echo.
echo Press any key to continue to Test 5...
pause >nul

cls
echo Test 5: File system test
echo.

echo Checking project files in current directory:
echo.

if exist "package.json" (
    echo [OK] package.json found
) else (
    echo [MISSING] package.json not found
)

if exist "main.js" (
    echo [OK] main.js found
) else (
    echo [MISSING] main.js not found
)

if exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [OK] Main application file found
) else (
    echo [MISSING] Main application file not found
)

echo.
echo All build script files:
dir *.bat /b 2>nul

echo.
echo Press any key for final test...
pause >nul

cls
echo ========================================
echo           TERMINAL TEST RESULTS
echo ========================================
echo.

echo [SUCCESS] Terminal is working correctly!
echo [SUCCESS] No auto-close issues detected!
echo.

echo If you can see this message, your terminal is working fine.
echo The build script issues are likely due to:
echo.

where node >nul 2>&1
if errorlevel 1 (
    echo [ISSUE] Node.js is not installed
    echo [SOLUTION] Install Node.js from https://nodejs.org
    echo.
) else (
    echo [OK] Node.js is available
)

where npm >nul 2>&1
if errorlevel 1 (
    echo [ISSUE] npm is not available
    echo [SOLUTION] Reinstall Node.js (npm comes with it)
    echo.
) else (
    echo [OK] npm is available
)

if not exist "package.json" (
    echo [ISSUE] package.json is missing
    echo [SOLUTION] Make sure you have all project files
    echo.
)

if not exist "main.js" (
    echo [ISSUE] main.js is missing
    echo [SOLUTION] Make sure you have all project files
    echo.
)

echo ========================================
echo.

echo RECOMMENDED NEXT STEPS:
echo.
echo 1. If Node.js is missing: Install it from https://nodejs.org
echo 2. If files are missing: Download complete project
echo 3. Try the simple build: SIMPLE-BUILD.bat
echo 4. Try the fixed build: 🔧-FIXED-BUILD-EXE.bat
echo 5. Run diagnosis: 🔍-SIMPLE-DIAGNOSIS.bat
echo.

echo ========================================
echo.

echo This terminal will stay open until you close it.
echo Press any key to exit...
pause >nul

echo.
echo Final test: This message should appear before exit
echo Goodbye!
echo.

REM Extra pause to be absolutely sure
timeout /t 2 /nobreak >nul
exit /b 0
