@echo off
title 🛡️ Cyber Sentinel Pro - SecOps Edition v1.0.0
color 0A
chcp 65001 >nul

echo.
echo  ██████╗██╗   ██╗██████╗ ███████╗██████╗     ███████╗███████╗███╗   ██╗████████╗██╗███╗   ██╗███████╗██╗     
echo ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗    ██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║████╗  ██║██╔════╝██║     
echo ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝    ███████╗█████╗  ██╔██╗ ██║   ██║   ██║██╔██╗ ██║█████╗  ██║     
echo ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗    ╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██║╚██╗██║██╔══╝  ██║     
echo ╚██████╗   ██║   ██████╔╝███████╗██║  ██║    ███████║███████╗██║ ╚████║   ██║   ██║██║ ╚████║███████╗███████╗
echo  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝╚══════╝╚══════╝
echo.
echo                                    🛡️ SecOps Edition v1.0.0 🛡️
echo                              Advanced Cybersecurity Testing Platform
echo                          🤖 AI + 🎮 3D Visualization + 🍯 Honeypot + 🔊 Audio
echo.
echo ===============================================================================
echo                                  🚀 QUICK START GUIDE
echo ===============================================================================
echo.

REM System Requirements Check
echo [INFO] 🔍 فحص متطلبات النظام...
echo.

REM Check Node.js
echo [CHECK] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌ ERROR] Node.js غير مثبت!
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo 🌐 https://nodejs.org/
    echo.
    echo ⚡ اختر النسخة LTS (الموصى بها)
    echo 🔄 ثم أعد تشغيل هذا الملف
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo [✅ SUCCESS] Node.js مثبت - الإصدار: %NODE_VERSION%

REM Check npm
echo [CHECK] فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌ ERROR] npm غير متوفر!
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo [✅ SUCCESS] npm متوفر - الإصدار: %NPM_VERSION%
echo.

REM Check and install dependencies
echo [INFO] 📦 فحص وتثبيت التبعيات...
if not exist "node_modules" (
    echo [INFO] 🔄 تثبيت التبعيات للمرة الأولى...
    echo [INFO] ⏳ قد يستغرق هذا عدة دقائق...
    echo.
    
    echo [INSTALL] تثبيت React وMaterial-UI...
    npm install --silent
    
    if %errorlevel% neq 0 (
        echo [❌ ERROR] فشل في تثبيت التبعيات!
        echo.
        echo 🔧 حلول مقترحة:
        echo 1. npm cache clean --force
        echo 2. حذف مجلد node_modules وإعادة التثبيت
        echo 3. تحقق من اتصال الإنترنت
        echo.
        pause
        exit /b 1
    )
    
    echo [✅ SUCCESS] تم تثبيت جميع التبعيات بنجاح!
    echo.
) else (
    echo [✅ SUCCESS] التبعيات مثبتة مسبقاً!
    echo.
)

REM Create required directories and files
echo [INFO] 📁 إعداد الملفات المطلوبة...

if not exist "public" mkdir public

if not exist "public\index.html" (
    echo [CREATE] إنشاء public\index.html...
    (
        echo ^<!DOCTYPE html^>
        echo ^<html lang="ar" dir="rtl"^>
        echo ^<head^>
        echo   ^<meta charset="utf-8" /^>
        echo   ^<meta name="viewport" content="width=device-width, initial-scale=1" /^>
        echo   ^<title^>Cyber Sentinel Pro - SecOps Edition^</title^>
        echo ^</head^>
        echo ^<body^>
        echo   ^<div id="root"^>^</div^>
        echo ^</body^>
        echo ^</html^>
    ) > public\index.html
)

if not exist ".env" (
    echo [CREATE] إنشاء ملف .env...
    (
        echo REACT_APP_NAME=Cyber Sentinel Pro
        echo REACT_APP_VERSION=1.0.0
        echo REACT_APP_DESCRIPTION=Advanced Cybersecurity Testing Platform
        echo GENERATE_SOURCEMAP=false
        echo FAST_REFRESH=true
    ) > .env
)

echo [✅ SUCCESS] تم إعداد جميع الملفات المطلوبة!
echo.

echo ===============================================================================
echo                                🌟 FEATURES OVERVIEW
echo ===============================================================================
echo.
echo 🤖 الذكاء الاصطناعي للكشف عن التهديدات
echo    ├─ شبكة عصبية متقدمة مع TensorFlow.js
echo    ├─ تعلم مستمر من البيانات الجديدة
echo    ├─ كشف الهجمات المتقدمة (APT, DDoS)
echo    └─ دقة عالية في التنبؤ بالتهديدات
echo.
echo 🎮 واجهة ثلاثية الأبعاد تفاعلية
echo    ├─ خريطة شبكة ثلاثية الأبعاد مع WebGL
echo    ├─ تأثيرات بصرية متقدمة
echo    ├─ تفاعل مباشر مع عقد الشبكة
echo    └─ رسوم متحركة سلسة 60 FPS
echo.
echo 🍯 نظام Honeypot ذكي
echo    ├─ فخاخ متعددة (SSH, HTTP, FTP, Database)
echo    ├─ تحليل تكتيكات المهاجمين
echo    ├─ تسجيل تفصيلي للهجمات
echo    └─ تنبيهات فورية للتهديدات
echo.
echo 🔊 نظام صوتي متقدم
echo    ├─ موسيقى ديناميكية تتغير حسب التهديدات
echo    ├─ تأثيرات صوتية تفاعلية
echo    ├─ Web Audio API مع تأثيرات متقدمة
echo    └─ تجربة صوتية غامرة كاملة
echo.
echo 📊 مراقبة الشبكة المباشرة
echo    ├─ رسوم بيانية حية مع Chart.js
echo    ├─ تحليل حركة البيانات في الوقت الفعلي
echo    ├─ إحصائيات النظام المباشرة
echo    └─ تنبيهات تلقائية للأنشطة المشبوهة
echo.

echo ===============================================================================
echo                                🔑 LOGIN CREDENTIALS
echo ===============================================================================
echo.
echo 👤 اسم المستخدم: admin
echo 🔒 كلمة المرور: JaMaL@123
echo.
echo ⚠️  ملاحظات أمنية مهمة:
echo    ✓ غير كلمة المرور الافتراضية بعد أول تسجيل دخول
echo    ✓ فعل التحقق بخطوتين للحماية الإضافية
echo    ✓ هذا البرنامج مخصص لاختبار الأمان المصرح به فقط
echo    ✓ لا تستخدمه ضد أنظمة لا تملكها
echo    ✓ احترم جميع القوانين المحلية والدولية
echo.

echo ===============================================================================
echo                                🚀 LAUNCHING APPLICATION
echo ===============================================================================
echo.
echo [INFO] 🚀 بدء تشغيل Cyber Sentinel Pro...
echo [INFO] 🌐 سيتم فتح التطبيق تلقائياً في المتصفح
echo [INFO] 📍 الرابط المباشر: http://localhost:3000
echo [INFO] ⏳ يرجى الانتظار حتى يتم تحميل التطبيق...
echo.
echo [CONTROLS] للتحكم في التطبيق:
echo    ⏹️  للإيقاف: اضغط Ctrl+C
echo    🔄 للإعادة: أغلق النافذة وشغل الملف مرة أخرى
echo    🌐 فتح في متصفح آخر: http://localhost:3000
echo.

REM Open browser after delay
start "" cmd /c "timeout /t 10 /nobreak >nul && start http://localhost:3000"

REM Start the React development server
echo [STARTING] تشغيل خادم التطوير...
npm start

REM If we reach here, the server has stopped
echo.
echo ===============================================================================
echo                                👋 APPLICATION STOPPED
echo ===============================================================================
echo.
echo [INFO] تم إيقاف Cyber Sentinel Pro بنجاح
echo [INFO] شكراً لاستخدام منصة الأمان السيبراني المتقدمة!
echo.
echo 📧 للدعم: <EMAIL>
echo 🌐 الموقع: https://cybersentinel.pro
echo 📱 تليجرام: @CyberSentinelSupport
echo.
echo ===============================================================================
echo.
pause
