#!/bin/bash

# Cyber Sentinel Pro - SecOps Edition
# Advanced Cybersecurity Testing Platform
# Start Script for Linux/macOS

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Clear screen
clear

echo -e "${CYAN}"
echo "  ██████╗██╗   ██╗██████╗ ███████╗██████╗     ███████╗███████╗███╗   ██╗████████╗██╗███╗   ██╗███████╗██╗     "
echo " ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗    ██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║████╗  ██║██╔════╝██║     "
echo " ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝    ███████╗█████╗  ██╔██╗ ██║   ██║   ██║██╔██╗ ██║█████╗  ██║     "
echo " ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗    ╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██║╚██╗██║██╔══╝  ██║     "
echo " ╚██████╗   ██║   ██████╔╝███████╗██║  ██║    ███████║███████╗██║ ╚████║   ██║   ██║██║ ╚████║███████╗███████╗"
echo "  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝╚══════╝╚══════╝"
echo -e "${NC}"
echo ""
echo -e "${GREEN}                                    🛡️ SecOps Edition v1.0.0 🛡️${NC}"
echo -e "${BLUE}                              Advanced Cybersecurity Testing Platform${NC}"
echo -e "${YELLOW}                          🤖 AI + 🎮 3D Visualization + 🍯 Honeypot + 🔊 Audio${NC}"
echo ""
echo "==============================================================================="
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✅ SUCCESS]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[ℹ️ INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️ WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ ERROR]${NC} $1"
}

# Check if Node.js is installed
print_info "فحص Node.js..."
if ! command -v node &> /dev/null; then
    print_error "Node.js غير مثبت!"
    echo ""
    echo "📥 يرجى تحميل وتثبيت Node.js من:"
    echo "🌐 https://nodejs.org/"
    echo ""
    echo "⚡ اختر النسخة LTS (الموصى بها)"
    echo "🔄 ثم شغل هذا الملف مرة أخرى"
    echo ""
    exit 1
fi

NODE_VERSION=$(node --version)
print_status "Node.js مثبت - الإصدار: $NODE_VERSION"

# Check if npm is available
print_info "فحص npm..."
if ! command -v npm &> /dev/null; then
    print_error "npm غير متوفر!"
    exit 1
fi

NPM_VERSION=$(npm --version)
print_status "npm متوفر - الإصدار: $NPM_VERSION"
echo ""

# Check and install dependencies
print_info "📦 فحص وتثبيت التبعيات..."
if [ ! -d "node_modules" ]; then
    print_info "🔄 تثبيت التبعيات للمرة الأولى..."
    print_info "⏳ قد يستغرق هذا عدة دقائق..."
    echo ""
    
    npm install
    
    if [ $? -ne 0 ]; then
        print_error "فشل في تثبيت التبعيات!"
        echo ""
        echo "🔧 حلول مقترحة:"
        echo "1. npm cache clean --force"
        echo "2. حذف مجلد node_modules وإعادة التثبيت"
        echo "3. تحقق من اتصال الإنترنت"
        echo ""
        exit 1
    fi
    
    print_status "تم تثبيت جميع التبعيات بنجاح!"
    echo ""
else
    print_status "التبعيات مثبتة مسبقاً!"
    echo ""
fi

# Create required directories and files
print_info "📁 إعداد الملفات المطلوبة..."

# Create public directory
if [ ! -d "public" ]; then
    mkdir -p public
fi

# Create index.html if it doesn't exist
if [ ! -f "public/index.html" ]; then
    print_info "إنشاء public/index.html..."
    cat > public/index.html << 'EOF'
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Cyber Sentinel Pro - SecOps Edition</title>
</head>
<body>
  <div id="root"></div>
</body>
</html>
EOF
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_info "إنشاء ملف .env..."
    cat > .env << 'EOF'
REACT_APP_NAME=Cyber Sentinel Pro
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Advanced Cybersecurity Testing Platform
GENERATE_SOURCEMAP=false
FAST_REFRESH=true
EOF
fi

print_status "تم إعداد جميع الملفات المطلوبة!"
echo ""

echo "==============================================================================="
echo -e "${CYAN}                                🌟 FEATURES OVERVIEW${NC}"
echo "==============================================================================="
echo ""
echo -e "${GREEN}🤖 الذكاء الاصطناعي للكشف عن التهديدات${NC}"
echo "   ├─ شبكة عصبية متقدمة مع TensorFlow.js"
echo "   ├─ تعلم مستمر من البيانات الجديدة"
echo "   ├─ كشف الهجمات المتقدمة (APT, DDoS)"
echo "   └─ دقة عالية في التنبؤ بالتهديدات"
echo ""
echo -e "${BLUE}🎮 واجهة ثلاثية الأبعاد تفاعلية${NC}"
echo "   ├─ خريطة شبكة ثلاثية الأبعاد مع WebGL"
echo "   ├─ تأثيرات بصرية متقدمة"
echo "   ├─ تفاعل مباشر مع عقد الشبكة"
echo "   └─ رسوم متحركة سلسة 60 FPS"
echo ""
echo -e "${YELLOW}🍯 نظام Honeypot ذكي${NC}"
echo "   ├─ فخاخ متعددة (SSH, HTTP, FTP, Database)"
echo "   ├─ تحليل تكتيكات المهاجمين"
echo "   ├─ تسجيل تفصيلي للهجمات"
echo "   └─ تنبيهات فورية للتهديدات"
echo ""

echo "==============================================================================="
echo -e "${CYAN}                                🔑 LOGIN CREDENTIALS${NC}"
echo "==============================================================================="
echo ""
echo -e "${GREEN}👤 اسم المستخدم: admin${NC}"
echo -e "${GREEN}🔒 كلمة المرور: JaMaL@123${NC}"
echo ""
echo -e "${YELLOW}⚠️  ملاحظات أمنية مهمة:${NC}"
echo "   ✓ غير كلمة المرور الافتراضية بعد أول تسجيل دخول"
echo "   ✓ فعل التحقق بخطوتين للحماية الإضافية"
echo "   ✓ هذا البرنامج مخصص لاختبار الأمان المصرح به فقط"
echo "   ✓ لا تستخدمه ضد أنظمة لا تملكها"
echo "   ✓ احترم جميع القوانين المحلية والدولية"
echo ""

echo "==============================================================================="
echo -e "${CYAN}                                🚀 LAUNCHING APPLICATION${NC}"
echo "==============================================================================="
echo ""
print_info "🚀 بدء تشغيل Cyber Sentinel Pro..."
print_info "🌐 سيتم فتح التطبيق تلقائياً في المتصفح"
print_info "📍 الرابط المباشر: http://localhost:3000"
print_info "⏳ يرجى الانتظار حتى يتم تحميل التطبيق..."
echo ""
echo -e "${BLUE}[CONTROLS] للتحكم في التطبيق:${NC}"
echo "   ⏹️  للإيقاف: اضغط Ctrl+C"
echo "   🔄 للإعادة: أغلق Terminal وشغل الملف مرة أخرى"
echo "   🌐 فتح في متصفح آخر: http://localhost:3000"
echo ""

# Open browser after delay (works on macOS and most Linux distributions)
if command -v open &> /dev/null; then
    # macOS
    (sleep 10 && open http://localhost:3000) &
elif command -v xdg-open &> /dev/null; then
    # Linux
    (sleep 10 && xdg-open http://localhost:3000) &
fi

# Start the React development server
print_info "تشغيل خادم التطوير..."
npm start

# If we reach here, the server has stopped
echo ""
echo "==============================================================================="
echo -e "${GREEN}                                👋 APPLICATION STOPPED${NC}"
echo "==============================================================================="
echo ""
print_info "تم إيقاف Cyber Sentinel Pro بنجاح"
print_info "شكراً لاستخدام منصة الأمان السيبراني المتقدمة!"
echo ""
echo "📧 للدعم: <EMAIL>"
echo "🌐 الموقع: https://cybersentinel.pro"
echo "📱 تليجرام: @CyberSentinelSupport"
echo ""
echo "==============================================================================="
echo ""
