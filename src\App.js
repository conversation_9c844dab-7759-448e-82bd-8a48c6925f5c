import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box } from '@mui/material';

// Import components
import LoadingScreen from './components/Common/LoadingScreen';
import LoginPage from './components/Auth/LoginPage';
import RegisterPage from './components/Auth/RegisterPage';
import Dashboard from './components/Dashboard/Dashboard';
import CyberCommandCenter from './components/Advanced/CyberCommandCenter';

// Import contexts
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SecurityProvider } from './contexts/SecurityContext';

// Create dark theme with cyber styling
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00ff41',
      light: '#33ff66',
      dark: '#00cc33',
    },
    secondary: {
      main: '#00ccff',
      light: '#33d9ff',
      dark: '#0099cc',
    },
    error: {
      main: '#ff0040',
      light: '#ff3366',
      dark: '#cc0033',
    },
    warning: {
      main: '#ffaa00',
      light: '#ffbb33',
      dark: '#cc8800',
    },
    info: {
      main: '#00ccff',
      light: '#33d9ff',
      dark: '#0099cc',
    },
    success: {
      main: '#00ff41',
      light: '#33ff66',
      dark: '#00cc33',
    },
    background: {
      default: '#0a0a0a',
      paper: 'rgba(26, 26, 46, 0.9)',
    },
    text: {
      primary: '#ffffff',
      secondary: '#cccccc',
    },
  },
  typography: {
    fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.75rem',
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h5: {
      fontWeight: 500,
      fontSize: '1.25rem',
    },
    h6: {
      fontWeight: 500,
      fontSize: '1rem',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
          minHeight: '100vh',
          fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif',
        },
        '*::-webkit-scrollbar': {
          width: '8px',
        },
        '*::-webkit-scrollbar-track': {
          background: 'rgba(0, 0, 0, 0.1)',
        },
        '*::-webkit-scrollbar-thumb': {
          background: 'rgba(0, 255, 65, 0.3)',
          borderRadius: '4px',
        },
        '*::-webkit-scrollbar-thumb:hover': {
          background: 'rgba(0, 255, 65, 0.5)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: '8px',
          fontWeight: 600,
        },
        contained: {
          boxShadow: '0 4px 15px rgba(0, 255, 65, 0.3)',
          '&:hover': {
            boxShadow: '0 6px 20px rgba(0, 255, 65, 0.4)',
            transform: 'translateY(-2px)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(26, 26, 46, 0.9)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 255, 65, 0.2)',
          borderRadius: '12px',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              borderColor: 'rgba(0, 255, 65, 0.3)',
            },
            '&:hover fieldset': {
              borderColor: 'rgba(0, 255, 65, 0.5)',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#00ff41',
            },
          },
        },
      },
    },
  },
});

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <LoadingScreen />;
  }
  
  return user ? children : <Navigate to="/login" replace />;
};

// Public Route Component (redirect if authenticated)
const PublicRoute = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <LoadingScreen />;
  }
  
  return !user ? children : <Navigate to="/dashboard" replace />;
};

// Main App Component
function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Initialize app
    const initializeApp = async () => {
      try {
        console.log('🚀 Initializing Cyber Sentinel Pro...');
        
        // Add cyber grid background
        document.body.classList.add('cyber-body');
        
        // Simulate loading time for dramatic effect
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        setIsLoading(false);
        console.log('✅ Cyber Sentinel Pro initialized successfully');
      } catch (error) {
        console.error('❌ Error initializing app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <AuthProvider>
        <SecurityProvider>
          <Router>
            <Box sx={{ minHeight: '100vh', position: 'relative' }}>
              {/* Cyber Grid Background */}
              <Box
                className="cyber-grid"
                sx={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  opacity: 0.05,
                  pointerEvents: 'none',
                  zIndex: -1,
                  backgroundImage: `
                    linear-gradient(rgba(0, 255, 65, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0, 255, 65, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '50px 50px',
                }}
              />
              
              <Routes>
                {/* Public Routes */}
                <Route 
                  path="/login" 
                  element={
                    <PublicRoute>
                      <LoginPage />
                    </PublicRoute>
                  } 
                />
                <Route 
                  path="/register" 
                  element={
                    <PublicRoute>
                      <RegisterPage />
                    </PublicRoute>
                  } 
                />
                
                {/* Protected Routes */}
                <Route 
                  path="/dashboard/*" 
                  element={
                    <ProtectedRoute>
                      <Dashboard />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/command-center" 
                  element={
                    <ProtectedRoute>
                      <CyberCommandCenter />
                    </ProtectedRoute>
                  } 
                />
                
                {/* Default redirect */}
                <Route path="/" element={<Navigate to="/login" replace />} />
                <Route path="*" element={<Navigate to="/login" replace />} />
              </Routes>
            </Box>
          </Router>
        </SecurityProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
