@echo off
setlocal enabledelayedexpansion
title Cyber Sentinel Pro - EXE Builder
color 0A

REM Force UTF-8 encoding and error handling
chcp 65001 >nul 2>&1

REM Immediate pause to prevent auto-close
echo Starting Cyber Sentinel Pro EXE Builder...
echo Press any key to continue or Ctrl+C to exit...
pause >nul

cls
echo.
echo ================================================================================
echo                    Cyber Sentinel Pro - Professional Edition
echo                              EXE Builder Tool
echo ================================================================================
echo.
echo [INFO] Building EXE file for Cyber Sentinel Pro...
echo [INFO] This process may take several minutes, please wait...
echo.

REM Check current directory
echo [STEP 1] Checking current directory...
echo Current directory: %CD%
echo.

REM List files in current directory
echo [STEP 2] Checking project files...
if exist "package.json" (
    echo [OK] package.json found
) else (
    echo [ERROR] package.json not found
    goto error_exit
)

if exist "main.js" (
    echo [OK] main.js found
) else (
    echo [ERROR] main.js not found
    goto error_exit
)

if exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [OK] Main application file found
) else (
    echo [ERROR] Main application file not found
    goto error_exit
)

echo.
echo [STEP 3] Checking Node.js installation...

REM Check Node.js with error handling
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo.
    echo [SOLUTION] Please install Node.js:
    echo 1. Go to: https://nodejs.org
    echo 2. Download LTS version
    echo 3. Install with default settings
    echo 4. Restart Command Prompt
    echo 5. Run this script again
    echo.
    echo [ACTION] Opening Node.js download page...
    start https://nodejs.org
    goto error_exit
) else (
    echo [OK] Node.js is installed
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set node_ver=%%i
    echo Version: !node_ver!
)

echo.
echo [STEP 4] Checking npm...

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not available
    echo [INFO] npm usually comes with Node.js
    echo [SOLUTION] Try reinstalling Node.js
    goto error_exit
) else (
    echo [OK] npm is available
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set npm_ver=%%i
    echo Version: !npm_ver!
)

echo.
echo [STEP 5] Installing dependencies...
echo [INFO] Installing Electron and Electron Builder...
echo [WARNING] This may take 5-10 minutes depending on your internet connection
echo.

REM Create package.json backup
if exist "package.json" (
    copy "package.json" "package.json.backup" >nul 2>&1
)

REM Install with verbose output and error handling
echo [INFO] Running: npm install electron electron-builder electron-updater --save-dev
echo [INFO] Please wait...
echo.

npm install electron electron-builder electron-updater --save-dev
set install_error=!errorlevel!

if !install_error! neq 0 (
    echo.
    echo [ERROR] Failed to install dependencies
    echo Error code: !install_error!
    echo.
    echo [SOLUTIONS] Try these solutions:
    echo 1. Check internet connection
    echo 2. Run Command Prompt as Administrator
    echo 3. Disable antivirus temporarily
    echo 4. Clear npm cache: npm cache clean --force
    echo 5. Delete node_modules folder and try again
    echo.
    goto error_exit
) else (
    echo.
    echo [OK] Dependencies installed successfully
)

echo.
echo [STEP 6] Building application...
echo [INFO] Creating EXE file...
echo [WARNING] This process may take 5-15 minutes
echo.

REM Try building with npm script first
echo [INFO] Attempting build with npm script...
npm run build-win
set build_error1=!errorlevel!

if !build_error1! neq 0 (
    echo [WARNING] npm script failed, trying alternative method...
    
    REM Try with npx
    echo [INFO] Attempting build with npx electron-builder...
    npx electron-builder --win
    set build_error2=!errorlevel!
    
    if !build_error2! neq 0 (
        echo.
        echo [ERROR] Build failed with both methods
        echo npm error: !build_error1!
        echo npx error: !build_error2!
        echo.
        echo [SOLUTIONS] Try these solutions:
        echo 1. Check available disk space (need 1GB+)
        echo 2. Close other applications
        echo 3. Disable antivirus temporarily
        echo 4. Try: npm run build-win --verbose
        echo 5. Check package.json configuration
        echo.
        goto error_exit
    )
)

echo.
echo [OK] Build completed successfully!

echo.
echo [STEP 7] Checking output files...

if exist "dist" (
    echo [OK] dist folder created
    echo.
    echo [INFO] Contents of dist folder:
    dir dist /b 2>nul
    echo.
    
    REM Look for EXE files
    for /r dist %%i in (*.exe) do (
        echo [FOUND] EXE file: %%~nxi
        echo Path: %%i
    )
    
    echo [ACTION] Opening dist folder...
    start dist
    
) else (
    echo [WARNING] dist folder not found
    echo [INFO] Build may have completed but files are in different location
)

echo.
echo ================================================================================
echo                              BUILD COMPLETED!
echo ================================================================================
echo.
echo [SUCCESS] Cyber Sentinel Pro has been converted to EXE successfully!
echo.
echo [FILES] Check the dist folder for:
echo - EXE file (executable)
echo - Setup file (installer)
echo - Unpacked folder (portable version)
echo.
echo [USAGE] To run the application:
echo 1. Navigate to dist folder
echo 2. Double-click the EXE file
echo 3. Login with: admin / JaMaL@123
echo.
echo [SUPPORT] If you need help:
echo Email: <EMAIL>
echo Website: https://cybersentinel.pro
echo.
goto normal_exit

:error_exit
echo.
echo ================================================================================
echo                                BUILD FAILED
echo ================================================================================
echo.
echo [ERROR] The build process encountered an error and could not complete.
echo.
echo [TROUBLESHOOTING] Common solutions:
echo.
echo 1. Node.js Issues:
echo    - Install Node.js from https://nodejs.org
echo    - Use LTS (Long Term Support) version
echo    - Restart Command Prompt after installation
echo.
echo 2. Permission Issues:
echo    - Run Command Prompt as Administrator
echo    - Check folder permissions
echo    - Disable UAC temporarily
echo.
echo 3. Network Issues:
echo    - Check internet connection
echo    - Disable firewall/antivirus temporarily
echo    - Try using VPN if blocked
echo.
echo 4. Disk Space Issues:
echo    - Free up at least 1GB of disk space
echo    - Clean temporary files
echo    - Move to different drive if needed
echo.
echo 5. Antivirus Issues:
echo    - Add project folder to antivirus exclusions
echo    - Disable real-time protection temporarily
echo    - Try different antivirus software
echo.
echo [ALTERNATIVE] Try the portable version:
echo Run: 🔧-SIMPLE-EXE-CONVERTER.bat
echo.
echo [SUPPORT] For additional help:
echo Email: <EMAIL>
echo Include: Error messages, system info, screenshots
echo.

:normal_exit
echo ================================================================================
echo.
echo [INFO] Build process finished.
echo [INFO] You can now close this window.
echo.
echo Press any key to exit...
pause >nul
exit /b 0
