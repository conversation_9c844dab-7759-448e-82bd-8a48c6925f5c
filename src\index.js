import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
// RTL support will be handled by Material-UI theme
// import { CacheProvider } from '@emotion/react';
// import createCache from '@emotion/cache';
// import rtlPlugin from 'stylis-plugin-rtl';
// import { prefixer } from 'stylis';

import App from './App';
import './index.css';

// RTL support is handled by Material-UI theme direction property

// إعداد الثيم المخصص
const theme = createTheme({
  direction: 'rtl',
  palette: {
    mode: 'dark',
    primary: {
      main: '#00ff41',
      dark: '#00cc33',
      light: '#33ff66',
      contrastText: '#000000'
    },
    secondary: {
      main: '#00ccff',
      dark: '#0099cc',
      light: '#33ddff',
      contrastText: '#000000'
    },
    error: {
      main: '#ff0040',
      dark: '#cc0033',
      light: '#ff3366'
    },
    warning: {
      main: '#ffaa00',
      dark: '#cc8800',
      light: '#ffbb33'
    },
    success: {
      main: '#00ff41',
      dark: '#00cc33',
      light: '#33ff66'
    },
    background: {
      default: '#0a0a0a',
      paper: 'rgba(26, 26, 46, 0.9)'
    },
    text: {
      primary: '#ffffff',
      secondary: '#cccccc'
    }
  },
  typography: {
    fontFamily: [
      'Cairo',
      'Roboto',
      'Arial',
      'sans-serif'
    ].join(','),
    h1: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 700,
      fontSize: '2.5rem',
      textShadow: '0 0 10px #00ff41'
    },
    h2: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
      fontSize: '2rem',
      textShadow: '0 0 8px #00ff41'
    },
    h3: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
      fontSize: '1.5rem'
    },
    h4: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 500,
      fontSize: '1.25rem'
    },
    body1: {
      fontFamily: 'Cairo, sans-serif',
      fontSize: '1rem',
      lineHeight: 1.6
    },
    body2: {
      fontFamily: 'Cairo, sans-serif',
      fontSize: '0.875rem',
      lineHeight: 1.5
    },
    button: {
      fontFamily: 'Cairo, sans-serif',
      fontWeight: 600,
      textTransform: 'none'
    },
    code: {
      fontFamily: 'Roboto Mono, monospace',
      fontSize: '0.875rem',
      backgroundColor: 'rgba(0, 255, 65, 0.1)',
      padding: '2px 4px',
      borderRadius: '4px'
    }
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
          minHeight: '100vh',
          fontFamily: 'Cairo, sans-serif'
        },
        '*::-webkit-scrollbar': {
          width: '8px',
        },
        '*::-webkit-scrollbar-track': {
          background: 'rgba(0, 0, 0, 0.3)',
        },
        '*::-webkit-scrollbar-thumb': {
          background: 'rgba(0, 255, 65, 0.5)',
          borderRadius: '4px',
        },
        '*::-webkit-scrollbar-thumb:hover': {
          background: 'rgba(0, 255, 65, 0.7)',
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          textTransform: 'none',
          fontWeight: 600,
          padding: '10px 20px',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 0 20px rgba(0, 255, 65, 0.4)',
            transform: 'translateY(-2px)'
          }
        },
        contained: {
          background: 'linear-gradient(45deg, #00ff41 30%, #00ccff 90%)',
          color: '#000000',
          '&:hover': {
            background: 'linear-gradient(45deg, #00cc33 30%, #0099cc 90%)',
          }
        },
        outlined: {
          borderColor: '#00ff41',
          color: '#00ff41',
          '&:hover': {
            borderColor: '#00cc33',
            backgroundColor: 'rgba(0, 255, 65, 0.1)'
          }
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              borderColor: 'rgba(0, 255, 65, 0.3)',
            },
            '&:hover fieldset': {
              borderColor: 'rgba(0, 255, 65, 0.6)',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#00ff41',
              boxShadow: '0 0 10px rgba(0, 255, 65, 0.3)'
            },
          },
          '& .MuiInputLabel-root': {
            color: '#cccccc',
            '&.Mui-focused': {
              color: '#00ff41'
            }
          },
          '& .MuiOutlinedInput-input': {
            color: '#ffffff'
          }
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(26, 26, 46, 0.9)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 255, 65, 0.2)',
          borderRadius: '12px'
        }
      }
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(26, 26, 46, 0.9)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 255, 65, 0.2)',
          borderRadius: '12px',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 8px 32px rgba(0, 255, 65, 0.2)',
            transform: 'translateY(-4px)'
          }
        }
      }
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(10, 10, 10, 0.95)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(0, 255, 65, 0.3)'
        }
      }
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: 'rgba(10, 10, 10, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRight: '1px solid rgba(0, 255, 65, 0.3)'
        }
      }
    }
  }
});

// إعداد الصوت
const audioContext = new (window.AudioContext || window.webkitAudioContext)();

// تشغيل الموسيقى الخلفية
const playBackgroundMusic = () => {
  // سيتم إضافة ملف الموسيقى لاحقاً
  console.log('🎵 تم تحضير نظام الصوت');
};

// تهيئة التطبيق
const initializeApp = () => {
  // تحقق من دعم المتصفح
  if (!window.crypto || !window.crypto.subtle) {
    console.error('❌ المتصفح لا يدعم Web Crypto API');
    alert('هذا المتصفح غير مدعوم. يرجى استخدام متصفح حديث.');
    return;
  }

  // تحقق من دعم WebGL
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  if (!gl) {
    console.warn('⚠️ WebGL غير مدعوم، قد تكون الأداء أبطأ');
  }

  console.log('✅ تم تهيئة التطبيق بنجاح');
  playBackgroundMusic();
};

// إنشاء التطبيق
const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <React.StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <App />
    </ThemeProvider>
  </React.StrictMode>
);

// تهيئة التطبيق
initializeApp();

// تسجيل Service Worker للأداء
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('✅ Service Worker مسجل:', registration.scope);
      })
      .catch((error) => {
        console.log('❌ فشل تسجيل Service Worker:', error);
      });
  });
}
