@echo off
title 🔍 Cyber Sentinel Pro - Problem Diagnosis
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🔍  CYBER SENTINEL PRO - PROBLEM DIAGNOSIS  🔍                             █
echo █                                                                              █
echo █     تشخيص شامل لمعرفة سبب عدم عمل التطبيق                                 █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔍 بدء التشخيص الشامل...
echo.

REM 1. Check current directory
echo [CHECK 1] فحص المجلد الحالي:
echo المجلد: %CD%
echo.

REM 2. Check if we're in the right place
echo [CHECK 2] فحص وجود ملفات المشروع:
if exist "package.json" (
    echo ✅ package.json موجود
) else (
    echo ❌ package.json غير موجود
)

if exist "src" (
    echo ✅ مجلد src موجود
) else (
    echo ❌ مجلد src غير موجود
)

if exist "public" (
    echo ✅ مجلد public موجود
) else (
    echo ❌ مجلد public غير موجود
)

if exist "node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ❌ مجلد node_modules غير موجود
)
echo.

REM 3. Check Node.js installation
echo [CHECK 3] فحص تثبيت Node.js:
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js موجود في PATH
    for /f "tokens=*" %%i in ('node --version 2^>nul') do (
        echo    الإصدار: %%i
    )
) else (
    echo ❌ Node.js غير موجود في PATH
    echo    تحقق من تثبيت Node.js من https://nodejs.org/
)
echo.

REM 4. Check npm
echo [CHECK 4] فحص npm:
where npm >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm موجود في PATH
    REM Try to get npm version
    npm --version >nul 2>&1
    if %errorlevel% equ 0 (
        for /f "tokens=*" %%i in ('npm --version 2^>nul') do (
            echo    الإصدار: %%i
        )
    ) else (
        echo ⚠️ npm موجود لكن لا يعمل (مشكلة PowerShell)
    )
) else (
    echo ❌ npm غير موجود في PATH
)
echo.

REM 5. Check PowerShell execution policy
echo [CHECK 5] فحص سياسة PowerShell:
powershell -Command "Get-ExecutionPolicy" >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('powershell -Command "Get-ExecutionPolicy" 2^>nul') do (
        echo    سياسة التنفيذ: %%i
        if "%%i"=="Restricted" (
            echo ❌ سياسة التنفيذ مقيدة - هذا سبب المشكلة!
        ) else (
            echo ✅ سياسة التنفيذ مسموحة
        )
    )
) else (
    echo ❌ لا يمكن فحص سياسة PowerShell
)
echo.

REM 6. Check package.json content
echo [CHECK 6] فحص محتوى package.json:
if exist "package.json" (
    findstr "react" package.json >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ package.json يحتوي على React
    ) else (
        echo ❌ package.json لا يحتوي على React
    )
    
    findstr "start" package.json >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ package.json يحتوي على سكريبت start
    ) else (
        echo ❌ package.json لا يحتوي على سكريبت start
    )
) else (
    echo ❌ package.json غير موجود
)
echo.

REM 7. Check internet connection
echo [CHECK 7] فحص الاتصال بالإنترنت:
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الاتصال بالإنترنت يعمل
) else (
    echo ❌ مشكلة في الاتصال بالإنترنت
)
echo.

REM 8. Check Windows version
echo [CHECK 8] فحص إصدار Windows:
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo    إصدار Windows: %VERSION%
echo.

REM 9. Check available disk space
echo [CHECK 9] فحص المساحة المتاحة:
for /f "tokens=3" %%i in ('dir /-c ^| find "bytes free"') do set FREESPACE=%%i
echo    المساحة المتاحة: %FREESPACE% bytes
echo.

REM 10. Try different approaches
echo [CHECK 10] اختبار طرق مختلفة للتشغيل:
echo.

echo [TEST A] اختبار Node.js مباشرة:
echo console.log('Node.js يعمل!'); > test-node.js
node test-node.js 2>nul
if %errorlevel% equ 0 (
    echo ✅ Node.js يعمل بشكل صحيح
) else (
    echo ❌ Node.js لا يعمل
)
del test-node.js 2>nul

echo.
echo [TEST B] اختبار npm باستخدام cmd:
cmd /c "npm --version" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm يعمل مع cmd
) else (
    echo ❌ npm لا يعمل حتى مع cmd
)

echo.
echo [TEST C] اختبار npx:
where npx >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npx متوفر
) else (
    echo ❌ npx غير متوفر
)

echo.
echo ===============================================================================
echo                              📋 DIAGNOSIS SUMMARY
echo ===============================================================================
echo.

REM Summary and recommendations
echo 🎯 ملخص التشخيص:
echo.

if not exist "package.json" (
    echo ❌ المشكلة الرئيسية: ملفات المشروع مفقودة
    echo 💡 الحل: استخدم FIX-AND-RUN.bat لإنشاء المشروع من جديد
    echo.
)

where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ المشكلة الرئيسية: Node.js غير مثبت
    echo 💡 الحل: حمل وثبت Node.js من https://nodejs.org/
    echo.
)

powershell -Command "Get-ExecutionPolicy" 2>nul | findstr "Restricted" >nul
if %errorlevel% equ 0 (
    echo ❌ المشكلة الرئيسية: سياسة PowerShell مقيدة
    echo 💡 الحل: استخدم SIMPLE-START.bat أو FIX-AND-RUN.bat
    echo.
)

if not exist "node_modules" (
    echo ⚠️ مشكلة ثانوية: التبعيات غير مثبتة
    echo 💡 الحل: استخدم FIX-AND-RUN.bat لتثبيت التبعيات
    echo.
)

echo 🚀 الحلول المتاحة (مرتبة حسب الأولوية):
echo.
echo 1. 🌐 للتشغيل الفوري: افتح cyber-sentinel-standalone.html في المتصفح
echo 2. 🔧 لحل جميع المشاكل: شغل FIX-AND-RUN.bat
echo 3. ⚡ للتشغيل البسيط: شغل SIMPLE-START.bat
echo 4. 📦 إذا فشل كل شيء: أعد تثبيت Node.js وشغل FIX-AND-RUN.bat
echo.

echo ===============================================================================
echo.

echo [INFO] انتهى التشخيص. اختر الحل المناسب من القائمة أعلاه.
echo.
pause
