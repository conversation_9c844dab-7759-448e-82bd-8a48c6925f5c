import CryptoJS from 'crypto-js';

// مفاتيح التشفير والأمان
const SECURITY_KEY = 'CyberSentinel-Security-Key-2024';
const INTEGRITY_KEY = 'CyberSentinel-Integrity-Check-2024';

// فئات التهديدات
const THREAT_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// أنواع التهديدات
const THREAT_TYPES = {
  INJECTION: 'injection',
  XSS: 'xss',
  CSRF: 'csrf',
  BRUTE_FORCE: 'brute_force',
  SUSPICIOUS_ACTIVITY: 'suspicious_activity',
  UNAUTHORIZED_ACCESS: 'unauthorized_access'
};

class SecurityService {
  constructor() {
    this.securityLog = [];
    this.threatDetectors = new Map();
    this.securityMetrics = {
      totalThreats: 0,
      blockedAttacks: 0,
      suspiciousActivities: 0,
      lastSecurityCheck: null
    };
    
    this.initializeSecurityMonitoring();
  }

  // تهيئة مراقبة الأمان
  initializeSecurityMonitoring() {
    // مراقبة محاولات الوصول غير المصرح بها
    this.setupAccessMonitoring();
    
    // مراقبة تلاعب DOM
    this.setupDOMIntegrityCheck();
    
    // مراقبة الشبكة
    this.setupNetworkMonitoring();
    
    // مراقبة أدوات المطور
    this.setupDevToolsDetection();
    
    console.log('🛡️ تم تفعيل نظام المراقبة الأمنية');
  }

  // مراقبة محاولات الوصول
  setupAccessMonitoring() {
    let failedAttempts = 0;
    const maxAttempts = 5;
    const timeWindow = 15 * 60 * 1000; // 15 دقيقة
    
    window.addEventListener('storage', (e) => {
      if (e.key === 'cybersentinel_token' && !e.newValue && e.oldValue) {
        this.logSecurityEvent({
          type: THREAT_TYPES.UNAUTHORIZED_ACCESS,
          level: THREAT_LEVELS.HIGH,
          message: 'تم حذف رمز المصادقة بشكل غير متوقع',
          timestamp: new Date()
        });
      }
    });

    // مراقبة محاولات تسجيل الدخول الفاشلة
    this.monitorFailedLogins = (attempt) => {
      failedAttempts++;
      
      if (failedAttempts >= maxAttempts) {
        this.logSecurityEvent({
          type: THREAT_TYPES.BRUTE_FORCE,
          level: THREAT_LEVELS.CRITICAL,
          message: `تم اكتشاف محاولة هجوم brute force - ${failedAttempts} محاولة فاشلة`,
          timestamp: new Date()
        });
        
        // حظر مؤقت
        this.temporaryLockout(timeWindow);
      }
    };
  }

  // مراقبة سلامة DOM
  setupDOMIntegrityCheck() {
    const originalConsole = { ...console };
    const criticalElements = ['script', 'iframe', 'object', 'embed'];
    
    // مراقبة إضافة عناصر مشبوهة
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const tagName = node.tagName?.toLowerCase();
              
              if (criticalElements.includes(tagName)) {
                this.logSecurityEvent({
                  type: THREAT_TYPES.XSS,
                  level: THREAT_LEVELS.HIGH,
                  message: `تم اكتشاف إضافة عنصر مشبوه: ${tagName}`,
                  element: node.outerHTML.substring(0, 100),
                  timestamp: new Date()
                });
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // حماية console من التلاعب
    Object.keys(console).forEach(key => {
      if (typeof console[key] === 'function') {
        console[key] = function(...args) {
          // تسجيل محاولات استخدام console في الإنتاج
          if (process.env.NODE_ENV === 'production') {
            securityService.logSecurityEvent({
              type: THREAT_TYPES.SUSPICIOUS_ACTIVITY,
              level: THREAT_LEVELS.MEDIUM,
              message: `محاولة استخدام console.${key}`,
              timestamp: new Date()
            });
          }
          return originalConsole[key].apply(console, args);
        };
      }
    });
  }

  // مراقبة الشبكة
  setupNetworkMonitoring() {
    const originalFetch = window.fetch;
    const originalXHR = window.XMLHttpRequest;

    // مراقبة طلبات fetch
    window.fetch = async function(...args) {
      const url = args[0];
      
      // فحص الطلبات المشبوهة
      if (typeof url === 'string') {
        if (securityService.isSuspiciousURL(url)) {
          securityService.logSecurityEvent({
            type: THREAT_TYPES.SUSPICIOUS_ACTIVITY,
            level: THREAT_LEVELS.HIGH,
            message: `طلب مشبوه إلى: ${url}`,
            timestamp: new Date()
          });
        }
      }
      
      return originalFetch.apply(this, args);
    };

    // مراقبة XMLHttpRequest
    const originalOpen = originalXHR.prototype.open;
    originalXHR.prototype.open = function(method, url, ...args) {
      if (securityService.isSuspiciousURL(url)) {
        securityService.logSecurityEvent({
          type: THREAT_TYPES.SUSPICIOUS_ACTIVITY,
          level: THREAT_LEVELS.HIGH,
          message: `XHR مشبوه إلى: ${url}`,
          timestamp: new Date()
        });
      }
      
      return originalOpen.call(this, method, url, ...args);
    };
  }

  // كشف أدوات المطور
  setupDevToolsDetection() {
    let devtools = { open: false, orientation: null };
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true;
          this.logSecurityEvent({
            type: THREAT_TYPES.SUSPICIOUS_ACTIVITY,
            level: THREAT_LEVELS.MEDIUM,
            message: 'تم اكتشاف فتح أدوات المطور',
            timestamp: new Date()
          });
        }
      } else {
        devtools.open = false;
      }
    }, 500);

    // كشف استخدام debugger
    const originalDebugger = window.debugger;
    Object.defineProperty(window, 'debugger', {
      get: function() {
        securityService.logSecurityEvent({
          type: THREAT_TYPES.SUSPICIOUS_ACTIVITY,
          level: THREAT_LEVELS.HIGH,
          message: 'محاولة استخدام debugger',
          timestamp: new Date()
        });
        return originalDebugger;
      }
    });
  }

  // فحص URL مشبوه
  isSuspiciousURL(url) {
    const suspiciousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /file:/i,
      /\.\.\//, // Path traversal
      /<script/i,
      /eval\(/i,
      /document\.cookie/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(url));
  }

  // تسجيل حدث أمني
  logSecurityEvent(event) {
    const securityEvent = {
      id: this.generateEventId(),
      ...event,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: event.timestamp || new Date()
    };

    this.securityLog.push(securityEvent);
    this.securityMetrics.totalThreats++;

    // إرسال تنبيه للخادم في حالة التهديدات الحرجة
    if (event.level === THREAT_LEVELS.CRITICAL) {
      this.sendSecurityAlert(securityEvent);
    }

    console.warn('🚨 حدث أمني:', securityEvent);
    
    // الاحتفاظ بآخر 1000 حدث فقط
    if (this.securityLog.length > 1000) {
      this.securityLog = this.securityLog.slice(-1000);
    }
  }

  // إرسال تنبيه أمني
  async sendSecurityAlert(event) {
    try {
      const encryptedEvent = this.encryptData(event);
      
      await fetch('/api/security/alert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('cybersentinel_token')}`
        },
        body: JSON.stringify({ event: encryptedEvent })
      });
    } catch (error) {
      console.error('خطأ في إرسال التنبيه الأمني:', error);
    }
  }

  // حظر مؤقت
  temporaryLockout(duration) {
    const lockoutEnd = Date.now() + duration;
    localStorage.setItem('cybersentinel_lockout', lockoutEnd.toString());
    
    this.logSecurityEvent({
      type: THREAT_TYPES.BRUTE_FORCE,
      level: THREAT_LEVELS.CRITICAL,
      message: `تم تفعيل الحظر المؤقت لمدة ${duration / 60000} دقيقة`,
      timestamp: new Date()
    });
  }

  // التحقق من الحظر المؤقت
  isLockedOut() {
    const lockoutEnd = localStorage.getItem('cybersentinel_lockout');
    if (lockoutEnd) {
      const now = Date.now();
      if (now < parseInt(lockoutEnd)) {
        return {
          locked: true,
          remainingTime: parseInt(lockoutEnd) - now
        };
      } else {
        localStorage.removeItem('cybersentinel_lockout');
      }
    }
    return { locked: false };
  }

  // فحص أمني شامل
  async performSecurityCheck() {
    const checks = {
      integrity: this.checkIntegrity(),
      storage: this.checkStorageSecurity(),
      network: this.checkNetworkSecurity(),
      environment: this.checkEnvironmentSecurity()
    };

    const overallStatus = Object.values(checks).every(check => check.status === 'secure') 
      ? 'secure' 
      : 'warning';

    this.securityMetrics.lastSecurityCheck = new Date();

    return {
      status: overallStatus,
      checks,
      metrics: this.securityMetrics,
      timestamp: new Date()
    };
  }

  // فحص سلامة البيانات
  checkIntegrity() {
    try {
      const criticalData = localStorage.getItem('cybersentinel_token');
      if (criticalData) {
        const hash = CryptoJS.SHA256(criticalData + INTEGRITY_KEY).toString();
        const storedHash = localStorage.getItem('cybersentinel_integrity');
        
        if (storedHash && hash !== storedHash) {
          return {
            status: 'compromised',
            message: 'تم اكتشاف تلاعب في البيانات'
          };
        }
      }
      
      return { status: 'secure', message: 'سلامة البيانات محققة' };
    } catch (error) {
      return { status: 'error', message: 'خطأ في فحص السلامة' };
    }
  }

  // فحص أمان التخزين
  checkStorageSecurity() {
    try {
      // فحص وجود بيانات حساسة غير مشفرة
      const sensitiveKeys = ['password', 'secret', 'key', 'token'];
      const storageKeys = Object.keys(localStorage);
      
      for (const key of storageKeys) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          const value = localStorage.getItem(key);
          if (value && !this.isEncrypted(value)) {
            return {
              status: 'warning',
              message: 'تم العثور على بيانات حساسة غير مشفرة'
            };
          }
        }
      }
      
      return { status: 'secure', message: 'التخزين آمن' };
    } catch (error) {
      return { status: 'error', message: 'خطأ في فحص التخزين' };
    }
  }

  // فحص أمان الشبكة
  checkNetworkSecurity() {
    const isHTTPS = window.location.protocol === 'https:';
    const hasCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    
    if (!isHTTPS && window.location.hostname !== 'localhost') {
      return {
        status: 'warning',
        message: 'الاتصال غير مشفر (HTTP)'
      };
    }
    
    return { status: 'secure', message: 'الشبكة آمنة' };
  }

  // فحص أمان البيئة
  checkEnvironmentSecurity() {
    const checks = {
      webCrypto: !!window.crypto?.subtle,
      secureContext: window.isSecureContext,
      localStorage: !!window.localStorage,
      sessionStorage: !!window.sessionStorage
    };
    
    const failedChecks = Object.entries(checks)
      .filter(([key, value]) => !value)
      .map(([key]) => key);
    
    if (failedChecks.length > 0) {
      return {
        status: 'warning',
        message: `فشل في فحص: ${failedChecks.join(', ')}`
      };
    }
    
    return { status: 'secure', message: 'البيئة آمنة' };
  }

  // تشفير البيانات
  encryptData(data) {
    return CryptoJS.AES.encrypt(JSON.stringify(data), SECURITY_KEY).toString();
  }

  // فك تشفير البيانات
  decryptData(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, SECURITY_KEY);
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
      return null;
    }
  }

  // التحقق من كون البيانات مشفرة
  isEncrypted(data) {
    try {
      // محاولة فك التشفير
      this.decryptData(data);
      return true;
    } catch (error) {
      return false;
    }
  }

  // توليد معرف الحدث
  generateEventId() {
    return CryptoJS.SHA256(Date.now() + Math.random().toString()).toString().substring(0, 16);
  }

  // الحصول على سجل الأمان
  getSecurityLog(limit = 100) {
    return this.securityLog.slice(-limit);
  }

  // مسح سجل الأمان
  clearSecurityLog() {
    this.securityLog = [];
    this.securityMetrics.totalThreats = 0;
    this.securityMetrics.blockedAttacks = 0;
    this.securityMetrics.suspiciousActivities = 0;
  }

  // الحصول على إحصائيات الأمان
  getSecurityMetrics() {
    return { ...this.securityMetrics };
  }
}

// إنشاء instance واحد من الخدمة
export const securityService = new SecurityService();
export { THREAT_LEVELS, THREAT_TYPES };
