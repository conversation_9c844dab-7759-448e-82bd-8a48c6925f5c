<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Cyber Sentinel Pro - SecOps Edition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Matrix Background */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            padding: 2rem 0;
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            margin-bottom: 2rem;
            border: 1px solid rgba(0, 255, 65, 0.3);
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff41, #00ccff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 2rem;
        }

        .login-container {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            max-width: 400px;
            margin: 0 auto 2rem;
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #00ff41;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 8px;
            color: #ffffff;
            font-size: 1rem;
        }

        .form-group input:focus {
            outline: none;
            border-color: #00ff41;
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #00ff41, #00ccff);
            color: #000000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
        }

        .dashboard {
            display: none;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
            border-color: rgba(0, 255, 65, 0.6);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #00ff41;
        }

        .feature-description {
            color: #cccccc;
            line-height: 1.6;
        }

        .status-bar {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            margin: 2rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff41;
            animation: pulse 2s infinite;
        }

        .terminal {
            background: #000000;
            border: 1px solid #00ff41;
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 2rem 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .terminal-line {
            margin: 0.5rem 0;
            animation: typewriter 2s steps(40) infinite;
        }

        .terminal-prompt {
            color: #00ff41;
        }

        .terminal-output {
            color: #00ccff;
        }

        .logout-btn {
            background: linear-gradient(45deg, #ff0040, #ff6600);
            margin-top: 2rem;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        @keyframes typewriter {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        .alert {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid rgba(255, 170, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 2rem 0;
            text-align: center;
        }

        .alert h3 {
            color: #ffaa00;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .logo {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Matrix Background -->
    <canvas class="matrix-bg" id="matrix-canvas"></canvas>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛡️ CYBER SENTINEL PRO</div>
            <div class="subtitle">SecOps Edition - Advanced Cybersecurity Testing Platform</div>
            <div class="subtitle">منصة اختبار الأمان السيبراني المتقدمة</div>
        </div>

        <!-- Login Section -->
        <div id="login-section" class="login-container">
            <h2 style="text-align: center; margin-bottom: 2rem; color: #00ff41;">تسجيل الدخول</h2>
            
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <input type="text" id="username" value="admin" placeholder="أدخل اسم المستخدم">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" value="JaMaL@123" placeholder="أدخل كلمة المرور">
            </div>
            
            <button class="btn" onclick="login()">دخول</button>
            
            <div style="text-align: center; margin-top: 1rem; font-size: 0.9rem; color: #cccccc;">
                <p>البيانات الافتراضية:</p>
                <p>👤 admin | 🔒 JaMaL@123</p>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="dashboard">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>النظام نشط</span>
                </div>
                <div class="status-item">
                    <span>مستوى التهديد: </span>
                    <span style="color: #00ff41;">منخفض</span>
                </div>
                <div class="status-item">
                    <span>الاتصالات النشطة: </span>
                    <span style="color: #00ccff;">15</span>
                </div>
                <div class="status-item">
                    <span>آخر فحص: </span>
                    <span id="last-scan">الآن</span>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="features-grid">
                <div class="feature-card" onclick="showFeature('ai')">
                    <span class="feature-icon">🤖</span>
                    <div class="feature-title">الذكاء الاصطناعي</div>
                    <div class="feature-description">
                        نظام متقدم للكشف عن التهديدات باستخدام الذكاء الاصطناعي والتعلم الآلي
                    </div>
                </div>

                <div class="feature-card" onclick="showFeature('3d')">
                    <span class="feature-icon">🎮</span>
                    <div class="feature-title">التصور ثلاثي الأبعاد</div>
                    <div class="feature-description">
                        خريطة شبكة تفاعلية ثلاثية الأبعاد مع تأثيرات بصرية متقدمة
                    </div>
                </div>

                <div class="feature-card" onclick="showFeature('honeypot')">
                    <span class="feature-icon">🍯</span>
                    <div class="feature-title">نظام Honeypot</div>
                    <div class="feature-description">
                        فخاخ ذكية لجذب المهاجمين وتحليل تكتيكاتهم
                    </div>
                </div>

                <div class="feature-card" onclick="showFeature('monitoring')">
                    <span class="feature-icon">📊</span>
                    <div class="feature-title">المراقبة المباشرة</div>
                    <div class="feature-description">
                        مراقبة الشبكة والنظام في الوقت الفعلي مع تنبيهات ذكية
                    </div>
                </div>

                <div class="feature-card" onclick="showFeature('audio')">
                    <span class="feature-icon">🔊</span>
                    <div class="feature-title">النظام الصوتي</div>
                    <div class="feature-description">
                        موسيقى ديناميكية وتأثيرات صوتية تفاعلية
                    </div>
                </div>

                <div class="feature-card" onclick="showFeature('tools')">
                    <span class="feature-icon">🛠️</span>
                    <div class="feature-title">أدوات الفحص</div>
                    <div class="feature-description">
                        مجموعة شاملة من أدوات فحص الأمان واختبار الاختراق
                    </div>
                </div>
            </div>

            <!-- Terminal -->
            <div class="terminal">
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">نظام الحماية السيبرانية نشط</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">تم تحميل نموذج الذكاء الاصطناعي</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">جاري مراقبة الشبكة...</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">تم تفعيل أنظمة Honeypot</span>
                </div>
            </div>

            <button class="btn logout-btn" onclick="logout()">تسجيل خروج</button>
        </div>

        <!-- Warning -->
        <div class="alert">
            <h3>⚠️ تحذير قانوني مهم</h3>
            <p>
                هذا البرنامج مخصص لاختبار الأمان المصرح به والأغراض التعليمية فقط.
                الاستخدام غير المصرح به ضد أنظمة لا تملكها غير قانوني.
            </p>
        </div>
    </div>

    <script>
        // Matrix Background Effect
        function initMatrix() {
            const canvas = document.getElementById('matrix-canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
            const matrixArray = matrix.split("");
            
            const fontSize = 10;
            const columns = canvas.width / fontSize;
            const drops = [];
            
            for(let x = 0; x < columns; x++) {
                drops[x] = 1;
            }
            
            function draw() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#00ff41';
                ctx.font = fontSize + 'px monospace';
                
                for(let i = 0; i < drops.length; i++) {
                    const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if(drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        drops[i] = 0;
                    }
                    drops[i]++;
                }
            }
            
            setInterval(draw, 35);
        }

        // Login function
        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'JaMaL@123') {
                document.getElementById('login-section').style.display = 'none';
                document.getElementById('dashboard-section').style.display = 'block';
                
                // Start updating last scan time
                updateLastScan();
                setInterval(updateLastScan, 5000);
            } else {
                alert('❌ اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        }

        // Logout function
        function logout() {
            document.getElementById('login-section').style.display = 'block';
            document.getElementById('dashboard-section').style.display = 'none';
        }

        // Show feature
        function showFeature(feature) {
            const features = {
                'ai': 'الذكاء الاصطناعي للكشف عن التهديدات',
                '3d': 'التصور ثلاثي الأبعاد للشبكة',
                'honeypot': 'نظام Honeypot الذكي',
                'monitoring': 'المراقبة المباشرة للشبكة',
                'audio': 'النظام الصوتي التفاعلي',
                'tools': 'أدوات فحص الأمان'
            };
            
            alert(`🚀 تم تفعيل: ${features[feature]}\n\nهذه الميزة متاحة في النسخة الكاملة من التطبيق.`);
        }

        // Update last scan time
        function updateLastScan() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.getElementById('last-scan').textContent = timeString;
        }

        // Initialize matrix effect
        window.onload = function() {
            initMatrix();
        };

        // Handle window resize
        window.onresize = function() {
            const canvas = document.getElementById('matrix-canvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Handle Enter key in login form
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && document.getElementById('login-section').style.display !== 'none') {
                login();
            }
        });
    </script>
</body>
</html>
