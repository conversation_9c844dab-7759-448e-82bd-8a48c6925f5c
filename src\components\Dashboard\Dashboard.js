import React, { useState, useEffect } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  Divider,
  Chip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Security,
  NetworkCheck,
  BugReport,
  Assessment,
  Settings,
  ExitToApp,
  Notifications,
  Shield,
  Warning,
  Person,
  AdminPanelSettings
} from '@mui/icons-material';

// استيراد المكونات
import DashboardHome from './DashboardHome';
import NetworkScanner from '../Tools/NetworkScanner';
import VulnerabilityScanner from '../Tools/VulnerabilityScanner';
import PenetrationTesting from '../Tools/PenetrationTesting';
import Reports from '../Reports/Reports';
import SettingsPage from '../Settings/SettingsPage';
import { useSecurity } from '../../contexts/SecurityContext';

const Dashboard = ({ user, onLogout }) => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationAnchor, setNotificationAnchor] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { alerts, threatLevel, getThreatLevelColor, isSecure } = useSecurity();

  // عناصر القائمة الجانبية
  const menuItems = [
    {
      text: 'لوحة التحكم',
      icon: <DashboardIcon />,
      path: '/dashboard',
      color: '#00ff41'
    },
    {
      text: 'فحص الشبكة',
      icon: <NetworkCheck />,
      path: '/dashboard/network-scanner',
      color: '#00ccff'
    },
    {
      text: 'فحص الثغرات',
      icon: <BugReport />,
      path: '/dashboard/vulnerability-scanner',
      color: '#ff6b35'
    },
    {
      text: 'اختبار الاختراق',
      icon: <Security />,
      path: '/dashboard/penetration-testing',
      color: '#ff0040'
    },
    {
      text: 'التقارير',
      icon: <Assessment />,
      path: '/dashboard/reports',
      color: '#ffaa00'
    },
    {
      text: 'الإعدادات',
      icon: <Settings />,
      path: '/dashboard/settings',
      color: '#9c27b0'
    }
  ];

  // الحصول على العنوان الحالي
  const getCurrentTitle = () => {
    const currentItem = menuItems.find(item => item.path === location.pathname);
    return currentItem ? currentItem.text : 'Cyber Sentinel Pro';
  };

  // معالج فتح/إغلاق القائمة الجانبية
  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  // معالج فتح قائمة المستخدم
  const handleUserMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  // معالج إغلاق قائمة المستخدم
  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  // معالج فتح قائمة الإشعارات
  const handleNotificationOpen = (event) => {
    setNotificationAnchor(event.currentTarget);
  };

  // معالج إغلاق قائمة الإشعارات
  const handleNotificationClose = () => {
    setNotificationAnchor(null);
  };

  // معالج تسجيل الخروج
  const handleLogout = () => {
    handleUserMenuClose();
    onLogout();
  };

  // معالج التنقل
  const handleNavigation = (path) => {
    navigate(path);
    setDrawerOpen(false);
  };

  // تأثيرات جانبية
  useEffect(() => {
    // إغلاق القائمة الجانبية عند تغيير المسار
    setDrawerOpen(false);
  }, [location.pathname]);

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* شريط التطبيق العلوي */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(10, 10, 10, 0.95)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(0, 255, 65, 0.3)'
        }}
      >
        <Toolbar>
          {/* زر القائمة */}
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={toggleDrawer}
            edge="start"
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          {/* العنوان */}
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{ flexGrow: 1, fontWeight: 'bold' }}
          >
            {getCurrentTitle()}
          </Typography>

          {/* مؤشر حالة الأمان */}
          <Tooltip title={`مستوى التهديد: ${threatLevel}`}>
            <Chip
              icon={<Shield />}
              label={isSecure() ? 'آمن' : 'تحذير'}
              size="small"
              sx={{
                backgroundColor: getThreatLevelColor(),
                color: '#000000',
                fontWeight: 'bold',
                mr: 2
              }}
            />
          </Tooltip>

          {/* زر الإشعارات */}
          <Tooltip title="الإشعارات">
            <IconButton
              color="inherit"
              onClick={handleNotificationOpen}
              sx={{ mr: 1 }}
            >
              <Badge badgeContent={alerts.length} color="error">
                <Notifications />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* معلومات المستخدم */}
          <Tooltip title="حساب المستخدم">
            <IconButton
              onClick={handleUserMenuOpen}
              sx={{ p: 0 }}
            >
              <Avatar
                sx={{
                  bgcolor: user?.isAdmin ? '#ff0040' : '#00ff41',
                  color: '#000000',
                  fontWeight: 'bold'
                }}
              >
                {user?.isAdmin ? <AdminPanelSettings /> : <Person />}
              </Avatar>
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية */}
      <Drawer
        variant="temporary"
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{
          width: 280,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            backgroundColor: 'rgba(10, 10, 10, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRight: '1px solid rgba(0, 255, 65, 0.3)'
          }
        }}
      >
        <Toolbar />
        
        {/* معلومات المستخدم في القائمة الجانبية */}
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Avatar
            sx={{
              width: 60,
              height: 60,
              bgcolor: user?.isAdmin ? '#ff0040' : '#00ff41',
              color: '#000000',
              margin: '0 auto 1rem',
              fontSize: '1.5rem'
            }}
          >
            {user?.username?.charAt(0).toUpperCase()}
          </Avatar>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 0.5 }}>
            {user?.username}
          </Typography>
          <Chip
            label={user?.isAdmin ? 'مدير' : 'مستخدم'}
            size="small"
            color={user?.isAdmin ? 'error' : 'primary'}
          />
        </Box>

        <Divider sx={{ borderColor: 'rgba(0, 255, 65, 0.2)' }} />

        {/* عناصر القائمة */}
        <List sx={{ pt: 2 }}>
          {menuItems.map((item) => (
            <ListItem
              button
              key={item.text}
              onClick={() => handleNavigation(item.path)}
              sx={{
                mx: 1,
                mb: 1,
                borderRadius: 2,
                backgroundColor: location.pathname === item.path 
                  ? 'rgba(0, 255, 65, 0.1)' 
                  : 'transparent',
                border: location.pathname === item.path 
                  ? '1px solid rgba(0, 255, 65, 0.3)' 
                  : '1px solid transparent',
                '&:hover': {
                  backgroundColor: 'rgba(0, 255, 65, 0.05)',
                  border: '1px solid rgba(0, 255, 65, 0.2)'
                }
              }}
            >
              <ListItemIcon
                sx={{
                  color: location.pathname === item.path ? item.color : '#cccccc',
                  minWidth: 40
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.text}
                sx={{
                  '& .MuiListItemText-primary': {
                    color: location.pathname === item.path ? '#ffffff' : '#cccccc',
                    fontWeight: location.pathname === item.path ? 'bold' : 'normal'
                  }
                }}
              />
            </ListItem>
          ))}
        </List>
      </Drawer>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: 8,
          backgroundColor: 'transparent',
          minHeight: 'calc(100vh - 64px)'
        }}
      >
        <Routes>
          <Route path="/" element={<DashboardHome user={user} />} />
          <Route path="/network-scanner" element={<NetworkScanner />} />
          <Route path="/vulnerability-scanner" element={<VulnerabilityScanner />} />
          <Route path="/penetration-testing" element={<PenetrationTesting />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/settings" element={<SettingsPage user={user} />} />
        </Routes>
      </Box>

      {/* قائمة المستخدم */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleUserMenuClose}
        PaperProps={{
          sx: {
            backgroundColor: 'rgba(26, 26, 46, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(0, 255, 65, 0.3)',
            borderRadius: 2
          }
        }}
      >
        <MenuItem onClick={() => { handleUserMenuClose(); navigate('/dashboard/settings'); }}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          الإعدادات
        </MenuItem>
        <Divider sx={{ borderColor: 'rgba(0, 255, 65, 0.2)' }} />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <ExitToApp fontSize="small" />
          </ListItemIcon>
          تسجيل الخروج
        </MenuItem>
      </Menu>

      {/* قائمة الإشعارات */}
      <Menu
        anchorEl={notificationAnchor}
        open={Boolean(notificationAnchor)}
        onClose={handleNotificationClose}
        PaperProps={{
          sx: {
            backgroundColor: 'rgba(26, 26, 46, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(0, 255, 65, 0.3)',
            borderRadius: 2,
            maxWidth: 350,
            maxHeight: 400
          }
        }}
      >
        {alerts.length === 0 ? (
          <MenuItem>
            <Typography variant="body2" color="text.secondary">
              لا توجد إشعارات جديدة
            </Typography>
          </MenuItem>
        ) : (
          alerts.slice(0, 5).map((alert, index) => (
            <MenuItem key={alert.id || index} onClick={handleNotificationClose}>
              <ListItemIcon>
                <Warning 
                  fontSize="small" 
                  sx={{ 
                    color: alert.level === 'critical' ? '#ff0040' : 
                           alert.level === 'high' ? '#ff6600' : 
                           alert.level === 'medium' ? '#ffaa00' : '#00ff41'
                  }} 
                />
              </ListItemIcon>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                  {alert.message}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {new Date(alert.timestamp).toLocaleString('ar-SA')}
                </Typography>
              </Box>
            </MenuItem>
          ))
        )}
        {alerts.length > 5 && (
          <MenuItem onClick={handleNotificationClose}>
            <Typography variant="caption" color="primary.main">
              عرض جميع الإشعارات ({alerts.length})
            </Typography>
          </MenuItem>
        )}
      </Menu>
    </Box>
  );
};

export default Dashboard;
