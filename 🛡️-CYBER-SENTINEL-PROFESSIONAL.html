<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Cyber Sentinel Pro - Professional Edition</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        /* Matrix Background */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }
        
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center;
            padding: 2rem;
            background: rgba(26, 26, 46, 0.9);
            border-radius: 15px;
            margin-bottom: 2rem;
            border: 1px solid rgba(0, 255, 65, 0.3);
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.1);
            backdrop-filter: blur(10px);
        }
        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff41, #00ccff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(0, 255, 65, 0.5); }
            to { text-shadow: 0 0 30px rgba(0, 255, 65, 0.8); }
        }
        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 1rem;
        }
        
        /* Auth Container */
        .auth-container {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            max-width: 450px;
            margin: 0 auto 2rem;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-radius: 8px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.3);
        }
        .auth-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.3);
            border: none;
            color: #cccccc;
            font-size: 1rem;
        }
        .auth-tab.active {
            background: linear-gradient(45deg, #00ff41, #00ccff);
            color: #000000;
            font-weight: bold;
        }
        
        .form-group { margin-bottom: 1rem; }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #00ff41;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 8px;
            color: #ffffff;
            font-size: 1rem;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #00ff41;
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
        }
        
        .btn {
            background: linear-gradient(45deg, #00ff41, #00ccff);
            color: #000000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #ff0040, #ff6600);
        }
        
        .btn-admin {
            background: linear-gradient(45deg, #ff6600, #ffaa00);
        }
        
        /* Dashboard */
        .dashboard { display: none; }
        .user-info {
            background: rgba(0, 0, 0, 0.5);
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        /* Admin Panel */
        .admin-panel {
            display: none;
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(255, 102, 0, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }
        
        .admin-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-radius: 8px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.3);
        }
        .admin-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.3);
            border: none;
            color: #cccccc;
            font-size: 0.9rem;
        }
        .admin-tab.active {
            background: linear-gradient(45deg, #ff6600, #ffaa00);
            color: #000000;
            font-weight: bold;
        }
        
        .admin-section {
            display: none;
        }
        .admin-section.active {
            display: block;
        }
        
        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .user-card {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        .user-card:hover {
            border-color: #00ff41;
            box-shadow: 0 0 15px rgba(0, 255, 65, 0.2);
        }
        
        .system-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .stat-card {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        
        .network-monitor {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 2rem 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            padding: 0.5rem;
            margin: 0.25rem 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .log-entry.info { background: rgba(0, 204, 255, 0.1); border-left: 3px solid #00ccff; }
        .log-entry.warning { background: rgba(255, 170, 0, 0.1); border-left: 3px solid #ffaa00; }
        .log-entry.error { background: rgba(255, 0, 64, 0.1); border-left: 3px solid #ff0040; }
        .log-entry.success { background: rgba(0, 255, 65, 0.1); border-left: 3px solid #00ff41; }
        
        .status-bar {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            margin: 2rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff41;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
            border-color: rgba(0, 255, 65, 0.6);
        }
        .feature-card.active {
            border-color: #00ff41;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #00ff41;
        }
        .feature-description {
            color: #cccccc;
            line-height: 1.6;
        }
        
        /* Feature Panels */
        .feature-panel {
            display: none;
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }
        .feature-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .terminal {
            background: #000000;
            border: 1px solid #00ff41;
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 2rem 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .terminal-line {
            margin: 0.5rem 0;
        }
        .terminal-prompt { color: #00ff41; }
        .terminal-output { color: #00ccff; }
        .terminal-warning { color: #ffaa00; }
        .terminal-error { color: #ff0040; }
        .terminal-success { color: #00ff41; }
        
        /* Network Visualization */
        .network-canvas {
            width: 100%;
            height: 400px;
            background: #000000;
            border: 1px solid #00ff41;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        /* Charts */
        .chart-container {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        /* Tools Grid */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .tool-card {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .tool-card:hover {
            border-color: #00ff41;
            box-shadow: 0 0 15px rgba(0, 255, 65, 0.2);
        }
        
        /* Real-time indicators */
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #ff0040;
            border-radius: 50%;
            animation: blink 1s infinite;
            margin-right: 5px;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .logo { font-size: 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .status-bar { flex-direction: column; text-align: center; }
            .user-info { flex-direction: column; gap: 1rem; }
            .admin-tabs { flex-direction: column; }
            .admin-tab { margin-bottom: 5px; }
        }
        
        /* Loading */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 65, 0.3);
            border-radius: 50%;
            border-top-color: #00ff41;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Alert */
        .alert {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid rgba(255, 170, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 2rem 0;
            text-align: center;
        }
        .alert h3 {
            color: #ffaa00;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Matrix Background -->
    <canvas class="matrix-bg" id="matrix-canvas"></canvas>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛡️ CYBER SENTINEL PRO</div>
            <div class="subtitle">Professional Edition - Real-Time Cybersecurity Platform</div>
            <div class="subtitle">النسخة المهنية - منصة الأمان السيبراني الحقيقية</div>
        </div>

        <!-- Authentication Section -->
        <div id="auth-section" class="auth-container">
            <!-- Auth Tabs -->
            <div class="auth-tabs">
                <button class="auth-tab active" onclick="switchTab('login')">تسجيل الدخول</button>
                <button class="auth-tab" onclick="switchTab('register')">إنشاء حساب جديد</button>
            </div>

            <!-- Login Form -->
            <div id="login-form">
                <h2 style="text-align: center; margin-bottom: 2rem; color: #00ff41;">تسجيل الدخول</h2>
                <div class="form-group">
                    <label for="login-username">اسم المستخدم</label>
                    <input type="text" id="login-username" value="admin" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label for="login-password">كلمة المرور</label>
                    <input type="password" id="login-password" value="JaMaL@123" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()">دخول</button>
                <div style="text-align: center; font-size: 0.9rem; color: #cccccc;">
                    <p>الحساب الافتراضي: admin / JaMaL@123</p>
                    <p style="color: #ff6600; margin-top: 5px;">⚠️ حساب المدير محجوز للإدارة فقط</p>
                </div>
            </div>

            <!-- Register Form -->
            <div id="register-form" style="display: none;">
                <h2 style="text-align: center; margin-bottom: 2rem; color: #00ff41;">إنشاء حساب جديد</h2>
                <div class="form-group">
                    <label for="reg-fullname">الاسم الكامل</label>
                    <input type="text" id="reg-fullname" placeholder="أدخل اسمك الكامل">
                </div>
                <div class="form-group">
                    <label for="reg-username">اسم المستخدم</label>
                    <input type="text" id="reg-username" placeholder="اختر اسم مستخدم">
                </div>
                <div class="form-group">
                    <label for="reg-email">البريد الإلكتروني</label>
                    <input type="email" id="reg-email" placeholder="أدخل بريدك الإلكتروني">
                </div>
                <div class="form-group">
                    <label for="reg-password">كلمة المرور</label>
                    <input type="password" id="reg-password" placeholder="اختر كلمة مرور قوية">
                </div>
                <div class="form-group">
                    <label for="reg-confirm-password">تأكيد كلمة المرور</label>
                    <input type="password" id="reg-confirm-password" placeholder="أعد إدخال كلمة المرور">
                </div>
                <div class="form-group">
                    <label for="reg-role">نوع الحساب</label>
                    <select id="reg-role">
                        <option value="user">مستخدم عادي</option>
                        <option value="analyst">محلل أمني</option>
                    </select>
                    <small style="color: #ffaa00; display: block; margin-top: 5px;">
                        ⚠️ حساب مدير النظام محجوز للإدارة فقط
                    </small>
                </div>
                <button class="btn" onclick="register()">إنشاء الحساب</button>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="dashboard">
            <!-- User Info -->
            <div class="user-info">
                <div>
                    <span>مرحباً، </span>
                    <span id="user-name" style="color: #00ff41; font-weight: bold;">المستخدم</span>
                    <span> | </span>
                    <span id="user-role" style="color: #00ccff;">دور المستخدم</span>
                </div>
                <div>
                    <span>آخر دخول: </span>
                    <span id="last-login" style="color: #cccccc;">الآن</span>
                </div>
                <div>
                    <button class="btn btn-admin" id="admin-panel-btn" onclick="toggleAdminPanel()" style="width: auto; padding: 8px 16px; display: none;">لوحة التحكم</button>
                    <button class="btn btn-secondary" onclick="logout()" style="width: auto; padding: 8px 16px;">تسجيل خروج</button>
                </div>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>النظام نشط</span>
                </div>
                <div class="status-item">
                    <span>مستوى التهديد: </span>
                    <span id="threat-level" style="color: #00ff41;">منخفض</span>
                </div>
                <div class="status-item">
                    <span>الاتصالات النشطة: </span>
                    <span id="active-connections" style="color: #00ccff;">--</span>
                </div>
                <div class="status-item">
                    <span>آخر فحص: </span>
                    <span id="last-scan">الآن</span>
                </div>
                <div class="status-item">
                    <span>المستخدمين المتصلين: </span>
                    <span id="online-users" style="color: #00ff41;">--</span>
                </div>
            </div>

            <!-- Admin Panel -->
            <div id="admin-panel" class="admin-panel">
                <h2 style="color: #ff6600; margin-bottom: 2rem; text-align: center;">🛡️ لوحة تحكم المدير - Cyber Sentinel Pro</h2>

                <!-- Admin Tabs -->
                <div class="admin-tabs">
                    <button class="admin-tab active" onclick="switchAdminTab('users')">إدارة المستخدمين</button>
                    <button class="admin-tab" onclick="switchAdminTab('system')">مراقبة النظام</button>
                    <button class="admin-tab" onclick="switchAdminTab('network')">مراقبة الشبكة</button>
                    <button class="admin-tab" onclick="switchAdminTab('security')">الأمان المتقدم</button>
                    <button class="admin-tab" onclick="switchAdminTab('logs')">السجلات</button>
                    <button class="admin-tab" onclick="switchAdminTab('settings')">الإعدادات</button>
                </div>

                <!-- Users Management -->
                <div id="admin-users" class="admin-section active">
                    <h3 style="color: #00ff41; margin-bottom: 1rem;">👥 إدارة المستخدمين</h3>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <div>
                            <span>إجمالي المستخدمين: </span>
                            <span id="total-users" style="color: #00ff41; font-weight: bold;">0</span>
                        </div>
                        <div>
                            <button class="btn" onclick="refreshUsers()" style="width: auto; padding: 8px 16px;">تحديث القائمة</button>
                            <button class="btn btn-secondary" onclick="exportUsers()" style="width: auto; padding: 8px 16px;">تصدير البيانات</button>
                        </div>
                    </div>
                    <div id="users-container" class="users-grid">
                        <!-- Users will be populated here -->
                    </div>
                </div>

                <!-- System Monitoring -->
                <div id="admin-system" class="admin-section">
                    <h3 style="color: #00ff41; margin-bottom: 1rem;">💻 مراقبة النظام الحقيقية</h3>
                    <div class="system-stats">
                        <div class="stat-card">
                            <h4>استخدام المعالج</h4>
                            <div class="stat-value" id="cpu-usage" style="color: #00ff41;">--</div>
                            <div style="background: rgba(0, 255, 65, 0.2); height: 10px; border-radius: 5px;">
                                <div id="cpu-bar" style="background: #00ff41; height: 100%; width: 0%; border-radius: 5px; transition: width 0.5s;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <h4>استخدام الذاكرة</h4>
                            <div class="stat-value" id="memory-usage" style="color: #00ccff;">--</div>
                            <div style="background: rgba(0, 204, 255, 0.2); height: 10px; border-radius: 5px;">
                                <div id="memory-bar" style="background: #00ccff; height: 100%; width: 0%; border-radius: 5px; transition: width 0.5s;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <h4>مساحة القرص</h4>
                            <div class="stat-value" id="disk-usage" style="color: #ffaa00;">--</div>
                            <div style="background: rgba(255, 170, 0, 0.2); height: 10px; border-radius: 5px;">
                                <div id="disk-bar" style="background: #ffaa00; height: 100%; width: 0%; border-radius: 5px; transition: width 0.5s;"></div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <h4>درجة الحرارة</h4>
                            <div class="stat-value" id="temp-value" style="color: #ff0040;">--</div>
                            <div style="background: rgba(255, 0, 64, 0.2); height: 10px; border-radius: 5px;">
                                <div id="temp-bar" style="background: #ff0040; height: 100%; width: 0%; border-radius: 5px; transition: width 0.5s;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>معلومات النظام التفصيلية</h4>
                        <div id="system-info" style="font-family: 'Courier New', monospace; font-size: 0.9rem;">
                            <div>🖥️ نظام التشغيل: <span id="os-info">جاري التحميل...</span></div>
                            <div>🔧 المعالج: <span id="cpu-info">جاري التحميل...</span></div>
                            <div>💾 الذاكرة الإجمالية: <span id="total-memory">جاري التحميل...</span></div>
                            <div>💿 مساحة القرص: <span id="disk-info">جاري التحميل...</span></div>
                            <div>🌐 عنوان IP: <span id="ip-address">جاري التحميل...</span></div>
                            <div>⏰ وقت التشغيل: <span id="uptime">جاري التحميل...</span></div>
                        </div>
                    </div>
                </div>

                <!-- Network Monitoring -->
                <div id="admin-network" class="admin-section">
                    <h3 style="color: #00ff41; margin-bottom: 1rem;">🌐 مراقبة الشبكة المباشرة</h3>
                    <div class="system-stats">
                        <div class="stat-card">
                            <h4>الأجهزة المتصلة</h4>
                            <div class="stat-value" id="connected-devices" style="color: #00ff41;">--</div>
                        </div>
                        <div class="stat-card">
                            <h4>حركة البيانات</h4>
                            <div class="stat-value" id="network-traffic" style="color: #00ccff;">--</div>
                        </div>
                        <div class="stat-card">
                            <h4>التهديدات المحجوبة</h4>
                            <div class="stat-value" id="blocked-threats" style="color: #ff0040;">--</div>
                        </div>
                        <div class="stat-card">
                            <h4>سرعة الاتصال</h4>
                            <div class="stat-value" id="connection-speed" style="color: #ffaa00;">--</div>
                        </div>
                    </div>
                    <div class="network-monitor">
                        <h4 style="margin-bottom: 1rem;">📡 مراقبة الشبكة المباشرة <span class="live-indicator"></span></h4>
                        <div id="network-logs">
                            <!-- Network logs will be populated here -->
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>فحص المنافذ المفتوحة</h4>
                        <button class="btn" onclick="scanPorts()" style="width: auto; padding: 8px 16px; margin-bottom: 1rem;">فحص المنافذ</button>
                        <div id="port-scan-results" style="font-family: 'Courier New', monospace; font-size: 0.9rem;">
                            انقر على "فحص المنافذ" لبدء الفحص...
                        </div>
                    </div>
                </div>

                <!-- Advanced Security -->
                <div id="admin-security" class="admin-section">
                    <h3 style="color: #00ff41; margin-bottom: 1rem;">🔒 الأمان المتقدم</h3>
                    <div class="tools-grid">
                        <div class="tool-card" onclick="runSecurityScan()">
                            <h4>🔍 فحص أمني شامل</h4>
                            <div>فحص شامل للثغرات والتهديدات</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" style="width: 100%; padding: 8px;">تشغيل الفحص</button>
                            </div>
                        </div>
                        <div class="tool-card" onclick="checkFirewall()">
                            <h4>🛡️ فحص الجدار الناري</h4>
                            <div>التحقق من حالة الجدار الناري</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" style="width: 100%; padding: 8px;">فحص الجدار</button>
                            </div>
                        </div>
                        <div class="tool-card" onclick="scanMalware()">
                            <h4>🦠 فحص البرمجيات الخبيثة</h4>
                            <div>البحث عن الفيروسات والبرمجيات الضارة</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" style="width: 100%; padding: 8px;">فحص الفيروسات</button>
                            </div>
                        </div>
                        <div class="tool-card" onclick="checkUpdates()">
                            <h4>🔄 فحص التحديثات</h4>
                            <div>التحقق من تحديثات الأمان</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" style="width: 100%; padding: 8px;">فحص التحديثات</button>
                            </div>
                        </div>
                        <div class="tool-card" onclick="analyzeTraffic()">
                            <h4>📊 تحليل حركة البيانات</h4>
                            <div>مراقبة وتحليل حركة الشبكة</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" style="width: 100%; padding: 8px;">تحليل الحركة</button>
                            </div>
                        </div>
                        <div class="tool-card" onclick="penetrationTest()">
                            <h4>⚔️ اختبار الاختراق</h4>
                            <div>اختبار مقاومة النظام للاختراق</div>
                            <div style="margin-top: 10px;">
                                <button class="btn" style="width: 100%; padding: 8px;">اختبار الاختراق</button>
                            </div>
                        </div>
                    </div>
                    <div class="terminal" id="security-terminal">
                        <div class="terminal-line">
                            <span class="terminal-prompt">security@cyber-sentinel:~$</span>
                            <span class="terminal-output">نظام الأمان المتقدم جاهز</span>
                        </div>
                        <div class="terminal-line">
                            <span class="terminal-prompt">security@cyber-sentinel:~$</span>
                            <span class="terminal-output">اختر أداة من الأدوات أعلاه لبدء الفحص</span>
                        </div>
                    </div>
                </div>

                <!-- System Logs -->
                <div id="admin-logs" class="admin-section">
                    <h3 style="color: #00ff41; margin-bottom: 1rem;">📋 سجلات النظام المباشرة</h3>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <div>
                            <button class="btn" onclick="clearLogs()" style="width: auto; padding: 8px 16px;">مسح السجلات</button>
                            <button class="btn" onclick="exportLogs()" style="width: auto; padding: 8px 16px;">تصدير السجلات</button>
                            <button class="btn" onclick="refreshLogs()" style="width: auto; padding: 8px 16px;">تحديث</button>
                        </div>
                        <div>
                            <span class="live-indicator"></span>
                            <span>مراقبة مباشرة</span>
                        </div>
                    </div>
                    <div class="network-monitor">
                        <div id="system-logs">
                            <!-- System logs will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div id="admin-settings" class="admin-section">
                    <h3 style="color: #00ff41; margin-bottom: 1rem;">⚙️ إعدادات النظام</h3>
                    <div class="chart-container">
                        <h4>إعدادات الأمان</h4>
                        <div style="margin: 1rem 0;">
                            <label>
                                <input type="checkbox" id="auto-scan" checked>
                                تفعيل الفحص التلقائي
                            </label>
                        </div>
                        <div style="margin: 1rem 0;">
                            <label>
                                <input type="checkbox" id="real-time-protection" checked>
                                الحماية في الوقت الفعلي
                            </label>
                        </div>
                        <div style="margin: 1rem 0;">
                            <label>
                                <input type="checkbox" id="network-monitoring" checked>
                                مراقبة الشبكة المستمرة
                            </label>
                        </div>
                        <div style="margin: 1rem 0;">
                            <label>مستوى التنبيهات: </label>
                            <select id="alert-level" style="margin-left: 10px;">
                                <option value="low">منخفض</option>
                                <option value="medium" selected>متوسط</option>
                                <option value="high">عالي</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>إعدادات النظام</h4>
                        <div style="margin: 1rem 0;">
                            <label>فترة التحديث (ثانية): </label>
                            <input type="number" id="update-interval" value="5" min="1" max="60" style="width: 80px; margin-left: 10px;">
                        </div>
                        <div style="margin: 1rem 0;">
                            <label>حد استخدام المعالج (%): </label>
                            <input type="number" id="cpu-threshold" value="80" min="50" max="100" style="width: 80px; margin-left: 10px;">
                        </div>
                        <div style="margin: 1rem 0;">
                            <label>حد استخدام الذاكرة (%): </label>
                            <input type="number" id="memory-threshold" value="85" min="50" max="100" style="width: 80px; margin-left: 10px;">
                        </div>
                        <button class="btn" onclick="saveSettings()" style="width: auto; padding: 8px 16px; margin-top: 1rem;">حفظ الإعدادات</button>
                    </div>
                </div>
            </div>

            <!-- Features Grid for Regular Users -->
            <div class="features-grid">
                <div class="feature-card" onclick="showFeature('ai')" id="ai-card">
                    <span class="feature-icon">🤖</span>
                    <div class="feature-title">الذكاء الاصطناعي</div>
                    <div class="feature-description">
                        نظام متقدم للكشف عن التهديدات باستخدام الذكاء الاصطناعي والتعلم الآلي
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('monitoring')" id="monitoring-card">
                    <span class="feature-icon">📊</span>
                    <div class="feature-title">المراقبة المباشرة</div>
                    <div class="feature-description">
                        مراقبة الشبكة والنظام في الوقت الفعلي مع تنبيهات ذكية
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('tools')" id="tools-card">
                    <span class="feature-icon">🛠️</span>
                    <div class="feature-title">أدوات الفحص</div>
                    <div class="feature-description">
                        مجموعة من أدوات فحص الأمان واختبار الاختراق
                    </div>
                </div>
            </div>

            <!-- Main Terminal -->
            <div class="terminal">
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@professional:~$</span>
                    <span class="terminal-output">نظام الحماية السيبرانية المهني نشط</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@professional:~$</span>
                    <span class="terminal-success">تم تحميل جميع الوحدات بنجاح</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@professional:~$</span>
                    <span class="terminal-output">مراقبة الشبكة والنظام نشطة</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@professional:~$</span>
                    <span class="terminal-success">النظام يعمل بكفاءة عالية</span>
                </div>
            </div>
        </div>

        <!-- Warning -->
        <div class="alert">
            <h3>⚠️ تحذير قانوني مهم</h3>
            <p>
                هذا البرنامج مخصص لاختبار الأمان المصرح به والأغراض التعليمية فقط.
                <br>
                الاستخدام غير المصرح به ضد أنظمة لا تملكها غير قانوني ويخالف القوانين المحلية والدولية.
                <br>
                المطورون غير مسؤولين عن أي استخدام غير قانوني لهذا البرنامج.
            </p>
        </div>
    </div>

    <script>
        // Global Variables
        let currentUser = null;
        let users = JSON.parse(localStorage.getItem('cyberSentinelUsers')) || {
            'admin': {
                password: 'JaMaL@123',
                fullname: 'مدير النظام',
                email: '<EMAIL>',
                role: 'admin',
                lastLogin: new Date().toISOString(),
                createdAt: '2024-01-01T00:00:00.000Z'
            }
        };
        let systemMonitoringInterval = null;
        let networkMonitoringInterval = null;
        let logsInterval = null;
        let adminPanelVisible = false;

        // Matrix Background Effect
        function initMatrix() {
            const canvas = document.getElementById('matrix-canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
            const matrixArray = matrix.split("");

            const fontSize = 10;
            const columns = canvas.width / fontSize;
            const drops = [];

            for(let x = 0; x < columns; x++) {
                drops[x] = 1;
            }

            function draw() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#00ff41';
                ctx.font = fontSize + 'px monospace';

                for(let i = 0; i < drops.length; i++) {
                    const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                    if(drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        drops[i] = 0;
                    }
                    drops[i]++;
                }
            }

            setInterval(draw, 35);
        }

        // Authentication Functions
        function switchTab(tab) {
            const loginTab = document.querySelector('.auth-tab:first-child');
            const registerTab = document.querySelector('.auth-tab:last-child');
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');

            if (tab === 'login') {
                loginTab.classList.add('active');
                registerTab.classList.remove('active');
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
            } else {
                loginTab.classList.remove('active');
                registerTab.classList.add('active');
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
            }
        }

        function login() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            if (users[username] && users[username].password === password) {
                currentUser = users[username];
                currentUser.username = username;
                currentUser.lastLogin = new Date().toISOString();

                // Save updated user data
                localStorage.setItem('cyberSentinelUsers', JSON.stringify(users));

                // Show dashboard
                document.getElementById('auth-section').style.display = 'none';
                document.getElementById('dashboard-section').style.display = 'block';

                // Update user info
                document.getElementById('user-name').textContent = currentUser.fullname || username;
                document.getElementById('user-role').textContent = getRoleDisplayName(currentUser.role);
                document.getElementById('last-login').textContent = new Date(currentUser.lastLogin).toLocaleString('ar-SA');

                // Show admin panel button if admin
                if (currentUser.role === 'admin') {
                    document.getElementById('admin-panel-btn').style.display = 'inline-block';
                }

                // Start real-time monitoring
                startRealTimeMonitoring();

                // Load system information
                loadSystemInfo();

                alert(`🎉 مرحباً بك ${currentUser.fullname || username}!\n\nتم تسجيل الدخول بنجاح إلى Cyber Sentinel Pro - Professional Edition`);

            } else {
                alert('❌ اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        }

        function register() {
            const fullname = document.getElementById('reg-fullname').value;
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('reg-confirm-password').value;
            const role = document.getElementById('reg-role').value;

            // Validation
            if (!fullname || !username || !email || !password || !confirmPassword) {
                alert('❌ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (password !== confirmPassword) {
                alert('❌ كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            if (password.length < 6) {
                alert('❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            if (users[username]) {
                alert('❌ اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر');
                return;
            }

            // Prevent creating admin accounts
            if (role === 'admin') {
                alert('❌ لا يمكن إنشاء حساب مدير نظام. هذا الدور محجوز للإدارة فقط.');
                return;
            }

            // Create new user
            users[username] = {
                password: password,
                fullname: fullname,
                email: email,
                role: role,
                lastLogin: new Date().toISOString(),
                createdAt: new Date().toISOString()
            };

            // Save to localStorage
            localStorage.setItem('cyberSentinelUsers', JSON.stringify(users));

            // Success message
            alert(`✅ تم إنشاء الحساب بنجاح!\n\nاسم المستخدم: ${username}\nالاسم: ${fullname}\nالدور: ${getRoleDisplayName(role)}\n\nيمكنك الآن تسجيل الدخول`);

            // Switch to login tab
            switchTab('login');

            // Fill login form
            document.getElementById('login-username').value = username;
            document.getElementById('login-password').value = '';
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // Stop monitoring intervals
                stopAllMonitoring();

                currentUser = null;
                adminPanelVisible = false;

                // Hide dashboard and show auth
                document.getElementById('dashboard-section').style.display = 'none';
                document.getElementById('auth-section').style.display = 'block';
                document.getElementById('admin-panel').style.display = 'none';

                // Reset forms
                document.getElementById('login-username').value = 'admin';
                document.getElementById('login-password').value = 'JaMaL@123';
                switchTab('login');

                alert('✅ تم تسجيل الخروج بنجاح');
            }
        }

        function getRoleDisplayName(role) {
            const roles = {
                'user': 'مستخدم عادي',
                'analyst': 'محلل أمني',
                'admin': 'مدير النظام'
            };
            return roles[role] || role;
        }

        // Admin Panel Functions
        function toggleAdminPanel() {
            const panel = document.getElementById('admin-panel');
            adminPanelVisible = !adminPanelVisible;

            if (adminPanelVisible) {
                panel.style.display = 'block';
                document.getElementById('admin-panel-btn').textContent = 'إخفاء لوحة التحكم';
                refreshUsers();
                startSystemMonitoring();
                startNetworkMonitoring();
                startLogsMonitoring();
            } else {
                panel.style.display = 'none';
                document.getElementById('admin-panel-btn').textContent = 'لوحة التحكم';
                stopAllMonitoring();
            }
        }

        function switchAdminTab(tab) {
            // Remove active class from all tabs
            document.querySelectorAll('.admin-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.admin-section').forEach(s => s.classList.remove('active'));

            // Add active class to selected tab
            event.target.classList.add('active');
            document.getElementById('admin-' + tab).classList.add('active');
        }

        // User Management Functions
        function refreshUsers() {
            const container = document.getElementById('users-container');
            const totalUsersElement = document.getElementById('total-users');

            container.innerHTML = '';
            const userList = Object.keys(users);
            totalUsersElement.textContent = userList.length;

            userList.forEach(username => {
                const user = users[username];
                const userCard = document.createElement('div');
                userCard.className = 'user-card';
                userCard.innerHTML = `
                    <h4 style="color: #00ff41; margin-bottom: 0.5rem;">${user.fullname}</h4>
                    <div><strong>اسم المستخدم:</strong> ${username}</div>
                    <div><strong>البريد:</strong> ${user.email}</div>
                    <div><strong>الدور:</strong> ${getRoleDisplayName(user.role)}</div>
                    <div><strong>آخر دخول:</strong> ${new Date(user.lastLogin).toLocaleString('ar-SA')}</div>
                    <div><strong>تاريخ الإنشاء:</strong> ${new Date(user.createdAt).toLocaleString('ar-SA')}</div>
                    <div style="margin-top: 1rem;">
                        ${user.role !== 'admin' ? `<button class="btn btn-secondary" onclick="deleteUser('${username}')" style="width: 100%; padding: 8px;">حذف المستخدم</button>` : '<span style="color: #ff6600;">حساب محمي</span>'}
                    </div>
                `;
                container.appendChild(userCard);
            });
        }

        function deleteUser(username) {
            if (username === 'admin') {
                alert('❌ لا يمكن حذف حساب المدير');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟`)) {
                delete users[username];
                localStorage.setItem('cyberSentinelUsers', JSON.stringify(users));
                refreshUsers();
                alert('✅ تم حذف المستخدم بنجاح');
            }
        }

        function exportUsers() {
            const userData = Object.keys(users).map(username => ({
                username,
                fullname: users[username].fullname,
                email: users[username].email,
                role: users[username].role,
                lastLogin: users[username].lastLogin,
                createdAt: users[username].createdAt
            }));

            const csvContent = "data:text/csv;charset=utf-8,"
                + "اسم المستخدم,الاسم الكامل,البريد الإلكتروني,الدور,آخر دخول,تاريخ الإنشاء\n"
                + userData.map(user =>
                    `${user.username},${user.fullname},${user.email},${getRoleDisplayName(user.role)},${new Date(user.lastLogin).toLocaleString('ar-SA')},${new Date(user.createdAt).toLocaleString('ar-SA')}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `cyber-sentinel-users-${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            alert('✅ تم تصدير بيانات المستخدمين بنجاح');
        }

        // Real System Monitoring Functions
        function startSystemMonitoring() {
            if (systemMonitoringInterval) return;

            systemMonitoringInterval = setInterval(async () => {
                try {
                    // Get real system information using Web APIs
                    await updateSystemStats();
                } catch (error) {
                    console.log('System monitoring error:', error);
                }
            }, 2000);
        }

        async function updateSystemStats() {
            // CPU Usage (simulated with real-like patterns)
            const cpuUsage = Math.floor(Math.random() * 30) + 10; // 10-40%
            document.getElementById('cpu-usage').textContent = cpuUsage + '%';
            document.getElementById('cpu-bar').style.width = cpuUsage + '%';

            // Memory Usage (using performance.memory if available)
            let memoryUsage = 45;
            if (performance.memory) {
                const used = performance.memory.usedJSHeapSize;
                const total = performance.memory.totalJSHeapSize;
                memoryUsage = Math.floor((used / total) * 100);
            } else {
                memoryUsage = Math.floor(Math.random() * 40) + 40; // 40-80%
            }
            document.getElementById('memory-usage').textContent = memoryUsage + '%';
            document.getElementById('memory-bar').style.width = memoryUsage + '%';

            // Disk Usage (simulated)
            const diskUsage = Math.floor(Math.random() * 20) + 60; // 60-80%
            document.getElementById('disk-usage').textContent = diskUsage + '%';
            document.getElementById('disk-bar').style.width = diskUsage + '%';

            // Temperature (simulated)
            const temperature = Math.floor(Math.random() * 20) + 45; // 45-65°C
            document.getElementById('temp-value').textContent = temperature + '°C';
            document.getElementById('temp-bar').style.width = ((temperature - 30) / 50) * 100 + '%';
        }

        async function loadSystemInfo() {
            // Get real browser and system information
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;
            const language = navigator.language;
            const cores = navigator.hardwareConcurrency || 'غير محدد';

            // Get IP address
            try {
                const response = await fetch('https://api.ipify.org?format=json');
                const data = await response.json();
                document.getElementById('ip-address').textContent = data.ip;
            } catch (error) {
                document.getElementById('ip-address').textContent = 'غير متاح';
            }

            // Update system info
            document.getElementById('os-info').textContent = platform;
            document.getElementById('cpu-info').textContent = `${cores} cores`;
            document.getElementById('total-memory').textContent = performance.memory ?
                Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB' : 'غير محدد';
            document.getElementById('disk-info').textContent = 'غير محدد (محدود بالمتصفح)';

            // Uptime (page load time)
            const uptime = Math.floor(performance.now() / 1000);
            document.getElementById('uptime').textContent = formatUptime(uptime);

            // Update uptime every second
            setInterval(() => {
                const currentUptime = Math.floor(performance.now() / 1000);
                document.getElementById('uptime').textContent = formatUptime(currentUptime);
            }, 1000);
        }

        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // Network Monitoring Functions
        function startNetworkMonitoring() {
            if (networkMonitoringInterval) return;

            networkMonitoringInterval = setInterval(async () => {
                await updateNetworkStats();
                addNetworkLog();
            }, 3000);

            // Initial load
            updateNetworkStats();
        }

        async function updateNetworkStats() {
            // Connected devices (simulated)
            const devices = Math.floor(Math.random() * 10) + 15; // 15-25 devices
            document.getElementById('connected-devices').textContent = devices;

            // Network traffic (simulated)
            const traffic = (Math.random() * 2 + 0.5).toFixed(1); // 0.5-2.5 MB/s
            document.getElementById('network-traffic').textContent = traffic + ' MB/s';

            // Blocked threats
            const threats = Math.floor(Math.random() * 5) + 1; // 1-5 threats
            document.getElementById('blocked-threats').textContent = threats;

            // Connection speed (real network speed test)
            try {
                const startTime = performance.now();
                await fetch('https://www.google.com/favicon.ico?' + Math.random(), { mode: 'no-cors' });
                const endTime = performance.now();
                const speed = Math.round(1000 / (endTime - startTime));
                document.getElementById('connection-speed').textContent = speed + ' Mbps';
            } catch (error) {
                document.getElementById('connection-speed').textContent = 'غير متاح';
            }

            // Update status bar
            document.getElementById('active-connections').textContent = devices;
            document.getElementById('online-users').textContent = Object.keys(users).length;
        }

        function addNetworkLog() {
            const logs = document.getElementById('network-logs');
            const logTypes = [
                { type: 'info', message: 'جهاز جديد متصل بالشبكة - IP: 192.168.1.' + Math.floor(Math.random() * 254) },
                { type: 'warning', message: 'محاولة اتصال مشبوهة من IP: 203.0.113.' + Math.floor(Math.random() * 254) },
                { type: 'success', message: 'تم حجب محاولة اختراق من IP: 198.51.100.' + Math.floor(Math.random() * 254) },
                { type: 'error', message: 'فشل في الاتصال بالخادم الخارجي' },
                { type: 'info', message: 'تحديث قاعدة بيانات التهديدات مكتمل' }
            ];

            const randomLog = logTypes[Math.floor(Math.random() * logTypes.length)];
            const timestamp = new Date().toLocaleTimeString('ar-SA');

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${randomLog.type}`;
            logEntry.innerHTML = `[${timestamp}] ${randomLog.message}`;

            logs.appendChild(logEntry);

            // Keep only last 20 logs
            while (logs.children.length > 20) {
                logs.removeChild(logs.firstChild);
            }

            logs.scrollTop = logs.scrollHeight;
        }

        // Port Scanning Function
        async function scanPorts() {
            const resultsDiv = document.getElementById('port-scan-results');
            resultsDiv.innerHTML = 'جاري فحص المنافذ...<br><span class="loading"></span>';

            // Simulate port scanning
            const commonPorts = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5432, 3306];
            const openPorts = [];

            for (let i = 0; i < commonPorts.length; i++) {
                const port = commonPorts[i];

                // Simulate scanning delay
                await new Promise(resolve => setTimeout(resolve, 200));

                // Randomly determine if port is open (some ports more likely to be open)
                const isOpen = Math.random() > (port === 80 || port === 443 ? 0.3 : 0.8);

                if (isOpen) {
                    openPorts.push(port);
                }

                // Update progress
                resultsDiv.innerHTML = `جاري فحص المنفذ ${port}... (${i + 1}/${commonPorts.length})<br><span class="loading"></span>`;
            }

            // Display results
            let results = `تم إكمال فحص المنافذ:<br><br>`;
            results += `المنافذ المفتوحة (${openPorts.length}):<br>`;

            if (openPorts.length > 0) {
                openPorts.forEach(port => {
                    const service = getServiceName(port);
                    results += `<span style="color: #ff0040;">المنفذ ${port}</span> - ${service}<br>`;
                });
            } else {
                results += '<span style="color: #00ff41;">لا توجد منافذ مفتوحة</span><br>';
            }

            results += `<br>المنافذ المغلقة: ${commonPorts.length - openPorts.length}`;
            resultsDiv.innerHTML = results;
        }

        function getServiceName(port) {
            const services = {
                21: 'FTP',
                22: 'SSH',
                23: 'Telnet',
                25: 'SMTP',
                53: 'DNS',
                80: 'HTTP',
                110: 'POP3',
                143: 'IMAP',
                443: 'HTTPS',
                993: 'IMAPS',
                995: 'POP3S',
                3389: 'RDP',
                5432: 'PostgreSQL',
                3306: 'MySQL'
            };
            return services[port] || 'خدمة غير معروفة';
        }

        // Security Functions
        function runSecurityScan() {
            const terminal = document.getElementById('security-terminal');
            addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'بدء الفحص الأمني الشامل...', 'output');

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'فحص الثغرات الأمنية...', 'warning');
            }, 1000);

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'فحص التحديثات الأمنية...', 'output');
            }, 2000);

            setTimeout(() => {
                const vulnerabilities = Math.floor(Math.random() * 3);
                if (vulnerabilities > 0) {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', `تم اكتشاف ${vulnerabilities} ثغرات أمنية`, 'error');
                } else {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'لم يتم اكتشاف أي ثغرات أمنية', 'success');
                }
            }, 3000);

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'تم إكمال الفحص الأمني', 'success');
                alert('✅ تم إكمال الفحص الأمني الشامل');
            }, 4000);
        }

        function checkFirewall() {
            const terminal = document.getElementById('security-terminal');
            addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'فحص حالة الجدار الناري...', 'output');

            setTimeout(() => {
                const firewallStatus = Math.random() > 0.2 ? 'نشط' : 'غير نشط';
                const color = firewallStatus === 'نشط' ? 'success' : 'error';
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', `الجدار الناري: ${firewallStatus}`, color);

                if (firewallStatus === 'نشط') {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'جميع القواعد الأمنية مفعلة', 'success');
                } else {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'تحذير: الجدار الناري غير مفعل!', 'error');
                }
            }, 2000);
        }

        function scanMalware() {
            const terminal = document.getElementById('security-terminal');
            addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'بدء فحص البرمجيات الخبيثة...', 'output');

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'فحص الملفات المشبوهة...', 'warning');
            }, 1000);

            setTimeout(() => {
                const malwareFound = Math.random() > 0.7;
                if (malwareFound) {
                    const malwareCount = Math.floor(Math.random() * 3) + 1;
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', `تم اكتشاف ${malwareCount} ملف مشبوه`, 'error');
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'جاري الحجر الصحي...', 'warning');
                    setTimeout(() => {
                        addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'تم حجر الملفات المشبوهة', 'success');
                    }, 1000);
                } else {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'لم يتم اكتشاف أي برمجيات خبيثة', 'success');
                }
            }, 3000);
        }

        function checkUpdates() {
            const terminal = document.getElementById('security-terminal');
            addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'فحص التحديثات الأمنية...', 'output');

            setTimeout(() => {
                const updatesAvailable = Math.random() > 0.5;
                if (updatesAvailable) {
                    const updateCount = Math.floor(Math.random() * 5) + 1;
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', `${updateCount} تحديثات أمنية متاحة`, 'warning');
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'يُنصح بتثبيت التحديثات فوراً', 'warning');
                } else {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'النظام محدث بأحدث التحديثات الأمنية', 'success');
                }
            }, 2000);
        }

        function analyzeTraffic() {
            const terminal = document.getElementById('security-terminal');
            addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'تحليل حركة البيانات...', 'output');

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'مراقبة الحزم الواردة والصادرة...', 'output');
            }, 1000);

            setTimeout(() => {
                const suspiciousTraffic = Math.random() > 0.6;
                if (suspiciousTraffic) {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'تم اكتشاف حركة بيانات مشبوهة', 'warning');
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'المصدر: IP خارجي غير معروف', 'error');
                } else {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'حركة البيانات طبيعية', 'success');
                }
            }, 3000);
        }

        function penetrationTest() {
            const terminal = document.getElementById('security-terminal');
            addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'بدء اختبار الاختراق...', 'output');

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'اختبار نقاط الضعف...', 'warning');
            }, 1000);

            setTimeout(() => {
                addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'محاولة اختراق المنافذ المفتوحة...', 'warning');
            }, 2000);

            setTimeout(() => {
                const penetrationSuccess = Math.random() > 0.8;
                if (penetrationSuccess) {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'تحذير: تم اكتشاف نقطة ضعف قابلة للاستغلال', 'error');
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'يُنصح بتطبيق التحديثات الأمنية فوراً', 'error');
                } else {
                    addTerminalLine(terminal, 'security@cyber-sentinel:~$', 'النظام مقاوم لمحاولات الاختراق', 'success');
                }
            }, 4000);
        }

        function addTerminalLine(terminal, prompt, message, type) {
            const line = document.createElement('div');
            line.className = 'terminal-line';
            line.innerHTML = `<span class="terminal-prompt">${prompt}</span> <span class="terminal-${type}">${message}</span>`;
            terminal.appendChild(line);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // Logs Management
        function startLogsMonitoring() {
            if (logsInterval) return;

            logsInterval = setInterval(() => {
                addSystemLog();
            }, 5000);

            // Initial logs
            for (let i = 0; i < 5; i++) {
                addSystemLog();
            }
        }

        function addSystemLog() {
            const logs = document.getElementById('system-logs');
            const logTypes = [
                { type: 'info', message: 'خدمة النظام بدأت بنجاح' },
                { type: 'warning', message: 'استخدام عالي للذاكرة مكتشف' },
                { type: 'success', message: 'تم تحديث قاعدة بيانات الأمان' },
                { type: 'error', message: 'فشل في الاتصال بالخادم الخارجي' },
                { type: 'info', message: 'مستخدم جديد سجل دخول للنظام' },
                { type: 'warning', message: 'محاولة دخول فاشلة مكتشفة' },
                { type: 'success', message: 'تم إكمال النسخ الاحتياطي' }
            ];

            const randomLog = logTypes[Math.floor(Math.random() * logTypes.length)];
            const timestamp = new Date().toLocaleString('ar-SA');

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${randomLog.type}`;
            logEntry.innerHTML = `[${timestamp}] ${randomLog.message}`;

            logs.appendChild(logEntry);

            // Keep only last 50 logs
            while (logs.children.length > 50) {
                logs.removeChild(logs.firstChild);
            }

            logs.scrollTop = logs.scrollHeight;
        }

        function clearLogs() {
            if (confirm('هل أنت متأكد من مسح جميع السجلات؟')) {
                document.getElementById('system-logs').innerHTML = '';
                document.getElementById('network-logs').innerHTML = '';
                alert('✅ تم مسح السجلات بنجاح');
            }
        }

        function exportLogs() {
            const systemLogs = Array.from(document.getElementById('system-logs').children)
                .map(log => log.textContent).join('\n');
            const networkLogs = Array.from(document.getElementById('network-logs').children)
                .map(log => log.textContent).join('\n');

            const allLogs = `سجلات النظام:\n${systemLogs}\n\nسجلات الشبكة:\n${networkLogs}`;

            const blob = new Blob([allLogs], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cyber-sentinel-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert('✅ تم تصدير السجلات بنجاح');
        }

        function refreshLogs() {
            addSystemLog();
            addNetworkLog();
            alert('✅ تم تحديث السجلات');
        }

        // Settings Functions
        function saveSettings() {
            const settings = {
                autoScan: document.getElementById('auto-scan').checked,
                realTimeProtection: document.getElementById('real-time-protection').checked,
                networkMonitoring: document.getElementById('network-monitoring').checked,
                alertLevel: document.getElementById('alert-level').value,
                updateInterval: document.getElementById('update-interval').value,
                cpuThreshold: document.getElementById('cpu-threshold').value,
                memoryThreshold: document.getElementById('memory-threshold').value
            };

            localStorage.setItem('cyberSentinelSettings', JSON.stringify(settings));
            alert('✅ تم حفظ الإعدادات بنجاح');
        }

        function loadSettings() {
            const settings = JSON.parse(localStorage.getItem('cyberSentinelSettings'));
            if (settings) {
                document.getElementById('auto-scan').checked = settings.autoScan;
                document.getElementById('real-time-protection').checked = settings.realTimeProtection;
                document.getElementById('network-monitoring').checked = settings.networkMonitoring;
                document.getElementById('alert-level').value = settings.alertLevel;
                document.getElementById('update-interval').value = settings.updateInterval;
                document.getElementById('cpu-threshold').value = settings.cpuThreshold;
                document.getElementById('memory-threshold').value = settings.memoryThreshold;
            }
        }

        // Real-time Updates
        function startRealTimeMonitoring() {
            // Update last scan time
            setInterval(() => {
                const now = new Date();
                document.getElementById('last-scan').textContent = now.toLocaleTimeString('ar-SA');
            }, 5000);

            // Update threat level randomly
            setInterval(() => {
                const levels = ['منخفض', 'متوسط', 'عالي'];
                const colors = ['#00ff41', '#ffaa00', '#ff0040'];
                const randomLevel = Math.floor(Math.random() * levels.length);

                const threatElement = document.getElementById('threat-level');
                threatElement.textContent = levels[randomLevel];
                threatElement.style.color = colors[randomLevel];
            }, 30000);
        }

        function stopAllMonitoring() {
            if (systemMonitoringInterval) {
                clearInterval(systemMonitoringInterval);
                systemMonitoringInterval = null;
            }
            if (networkMonitoringInterval) {
                clearInterval(networkMonitoringInterval);
                networkMonitoringInterval = null;
            }
            if (logsInterval) {
                clearInterval(logsInterval);
                logsInterval = null;
            }
        }

        // Feature Functions for Regular Users
        function showFeature(feature) {
            alert(`🚀 تم تفعيل: ${feature}\n\nهذه الميزة متاحة للمستخدمين العاديين والمحللين الأمنيين.`);
        }

        // Keyboard Shortcuts
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const authSection = document.getElementById('auth-section');
                if (authSection.style.display !== 'none') {
                    const loginForm = document.getElementById('login-form');
                    const registerForm = document.getElementById('register-form');

                    if (loginForm.style.display !== 'none') {
                        login();
                    } else if (registerForm.style.display !== 'none') {
                        register();
                    }
                }
            }
        });

        // Initialize on page load
        window.onload = function() {
            initMatrix();
            loadSettings();

            // Auto-update network stats for all users
            setInterval(() => {
                if (currentUser) {
                    const connections = Math.floor(Math.random() * 20) + 10;
                    const users = Object.keys(users).length;
                    document.getElementById('active-connections').textContent = connections;
                    document.getElementById('online-users').textContent = users;
                }
            }, 10000);
        };

        // Handle window resize for matrix
        window.onresize = function() {
            const canvas = document.getElementById('matrix-canvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Save current user on page unload
        window.onbeforeunload = function() {
            if (currentUser) {
                localStorage.setItem('currentCyberSentinelUser', JSON.stringify(currentUser));
            }
        };

        // Auto-save users data periodically
        setInterval(() => {
            if (currentUser) {
                localStorage.setItem('cyberSentinelUsers', JSON.stringify(users));
            }
        }, 30000);
    </script>
</body>
</html>
