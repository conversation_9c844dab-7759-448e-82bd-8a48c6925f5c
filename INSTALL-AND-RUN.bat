@echo off
title 🛡️ Cyber Sentinel Pro - Install & Run
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🛡️  CYBER SENTINEL PRO - INSTALL & RUN  🛡️                               █
echo █                                                                              █
echo █     ✅ حل مشاكل PowerShell وتثبيت التبعيات                                 █
echo █     ✅ تشغيل التطبيق بشكل صحيح                                            █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔧 حل مشاكل PowerShell وتثبيت التبعيات...
echo.

REM Check Node.js
echo [CHECK] فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] Node.js غير مثبت!
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo 🌐 https://nodejs.org/
    echo.
    pause
    exit /b 1
)

node --version
echo [✅] Node.js متوفر
echo.

REM Check npm
echo [CHECK] فحص npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] npm غير متوفر!
    pause
    exit /b 1
)

npm --version
echo [✅] npm متوفر
echo.

REM Install dependencies using cmd instead of PowerShell
echo [INSTALL] تثبيت التبعيات...
echo [INFO] قد يستغرق هذا عدة دقائق...
echo.

REM Use cmd /c to avoid PowerShell execution policy issues
cmd /c "npm install"

if %errorlevel% neq 0 (
    echo [❌] فشل في تثبيت التبعيات!
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تشغيل Command Prompt كمدير
    echo 2. npm cache clean --force
    echo 3. حذف مجلد node_modules وإعادة المحاولة
    echo.
    pause
    exit /b 1
)

echo [✅] تم تثبيت التبعيات بنجاح!
echo.

REM Verify installation
echo [VERIFY] التحقق من التثبيت...
if exist "node_modules" (
    echo [✅] node_modules موجود
) else (
    echo [❌] node_modules غير موجود
    pause
    exit /b 1
)

if exist "node_modules\react" (
    echo [✅] React مثبت
) else (
    echo [❌] React غير مثبت
    pause
    exit /b 1
)

if exist "node_modules\@mui\material" (
    echo [✅] Material-UI مثبت
) else (
    echo [❌] Material-UI غير مثبت
    pause
    exit /b 1
)

echo.
echo ===============================================================================
echo                              🎯 INSTALLATION COMPLETE
echo ===============================================================================
echo.
echo ✅ تم تثبيت جميع التبعيات بنجاح!
echo ✅ التطبيق جاهز للتشغيل!
echo.
echo 🔑 بيانات الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: JaMaL@123
echo.
echo 🌟 الميزات المتاحة:
echo    🤖 الذكاء الاصطناعي للكشف عن التهديدات
echo    🎮 التصور ثلاثي الأبعاد التفاعلي
echo    🍯 نظام Honeypot الذكي
echo    🔊 النظام الصوتي المتقدم
echo    📊 المراقبة المباشرة للشبكة
echo.
echo 🌐 سيفتح التطبيق على: http://localhost:3000
echo.
echo ===============================================================================
echo.

echo [🚀] بدء تشغيل Cyber Sentinel Pro...
echo [ℹ️] للإيقاف: اضغط Ctrl+C
echo.

REM Open browser
start "" cmd /c "timeout /t 10 /nobreak >nul && start http://localhost:3000"

REM Start the application using cmd to avoid PowerShell issues
cmd /c "npm start"

echo.
echo ===============================================================================
echo                              ✅ APPLICATION STOPPED
echo ===============================================================================
echo.
echo [✅] تم إيقاف Cyber Sentinel Pro بنجاح!
echo [📧] للدعم: <EMAIL>
echo.
pause
