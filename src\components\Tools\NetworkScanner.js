import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  NetworkCheck,
  PlayArrow,
  Stop,
  Refresh,
  Download,
  Visibility,
  ExpandMore,
  Computer,
  Router,
  Security,
  Speed,
  Timeline,
  Warning
} from '@mui/icons-material';

const NetworkScanner = () => {
  const [scanConfig, setScanConfig] = useState({
    target: '',
    scanType: 'quick',
    ports: 'common',
    customPorts: '',
    timing: 'normal',
    detectOS: false,
    detectServices: true,
    scriptScan: false
  });

  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanResults, setScanResults] = useState([]);
  const [scanHistory, setScanHistory] = useState([]);
  const [selectedHost, setSelectedHost] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [error, setError] = useState('');
  const [scanStats, setScanStats] = useState({
    totalHosts: 0,
    activeHosts: 0,
    openPorts: 0,
    services: 0
  });

  // أنواع الفحص
  const scanTypes = [
    { value: 'quick', label: 'فحص سريع', description: 'فحص أساسي للمنافذ الشائعة' },
    { value: 'comprehensive', label: 'فحص شامل', description: 'فحص جميع المنافذ مع تحديد الخدمات' },
    { value: 'stealth', label: 'فحص خفي', description: 'فحص SYN للتجنب الكشف' },
    { value: 'aggressive', label: 'فحص قوي', description: 'فحص شامل مع تحديد نظام التشغيل' },
    { value: 'custom', label: 'فحص مخصص', description: 'إعدادات مخصصة' }
  ];

  // خيارات المنافذ
  const portOptions = [
    { value: 'common', label: 'المنافذ الشائعة', ports: '21,22,23,25,53,80,110,443,993,995' },
    { value: 'top100', label: 'أهم 100 منفذ', ports: 'top-100' },
    { value: 'top1000', label: 'أهم 1000 منفذ', ports: 'top-1000' },
    { value: 'all', label: 'جميع المنافذ', ports: '1-65535' },
    { value: 'custom', label: 'مخصص', ports: 'custom' }
  ];

  // خيارات التوقيت
  const timingOptions = [
    { value: 'paranoid', label: 'بطيء جداً (T0)', description: 'للشبكات الحساسة' },
    { value: 'sneaky', label: 'بطيء (T1)', description: 'تجنب أنظمة الكشف' },
    { value: 'polite', label: 'مهذب (T2)', description: 'أقل استهلاكاً للموارد' },
    { value: 'normal', label: 'عادي (T3)', description: 'الافتراضي' },
    { value: 'aggressive', label: 'سريع (T4)', description: 'شبكات سريعة' },
    { value: 'insane', label: 'سريع جداً (T5)', description: 'شبكات محلية فقط' }
  ];

  // محاكاة نتائج الفحص
  const mockScanResults = [
    {
      ip: '***********',
      hostname: 'router.local',
      status: 'up',
      os: 'Linux 3.x',
      ports: [
        { port: 22, protocol: 'tcp', state: 'open', service: 'ssh', version: 'OpenSSH 7.4' },
        { port: 80, protocol: 'tcp', state: 'open', service: 'http', version: 'nginx 1.14.0' },
        { port: 443, protocol: 'tcp', state: 'open', service: 'https', version: 'nginx 1.14.0' }
      ],
      vulnerabilities: ['CVE-2021-44228', 'CVE-2022-0778']
    },
    {
      ip: '*************',
      hostname: 'desktop-pc',
      status: 'up',
      os: 'Windows 10',
      ports: [
        { port: 135, protocol: 'tcp', state: 'open', service: 'msrpc', version: 'Microsoft Windows RPC' },
        { port: 445, protocol: 'tcp', state: 'open', service: 'microsoft-ds', version: 'Windows 10' },
        { port: 3389, protocol: 'tcp', state: 'open', service: 'ms-wbt-server', version: 'Microsoft Terminal Services' }
      ],
      vulnerabilities: ['CVE-2019-0708']
    }
  ];

  // معالج تغيير الإعدادات
  const handleConfigChange = (field, value) => {
    setScanConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
  };

  // التحقق من صحة الهدف
  const validateTarget = (target) => {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/;
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    
    return ipRegex.test(target) || cidrRegex.test(target) || domainRegex.test(target);
  };

  // بدء الفحص
  const startScan = async () => {
    if (!scanConfig.target.trim()) {
      setError('يرجى إدخال هدف الفحص');
      return;
    }

    if (!validateTarget(scanConfig.target)) {
      setError('تنسيق الهدف غير صحيح. استخدم IP أو CIDR أو اسم النطاق');
      return;
    }

    setIsScanning(true);
    setScanProgress(0);
    setError('');
    setScanResults([]);

    try {
      // محاكاة الفحص
      const totalSteps = 100;
      for (let i = 0; i <= totalSteps; i++) {
        await new Promise(resolve => setTimeout(resolve, 50));
        setScanProgress((i / totalSteps) * 100);
      }

      // محاكاة النتائج
      setScanResults(mockScanResults);
      setScanStats({
        totalHosts: 254,
        activeHosts: mockScanResults.length,
        openPorts: mockScanResults.reduce((total, host) => total + host.ports.length, 0),
        services: mockScanResults.reduce((total, host) => total + host.ports.filter(p => p.service).length, 0)
      });

      // إضافة إلى التاريخ
      const newScan = {
        id: Date.now(),
        target: scanConfig.target,
        type: scanConfig.scanType,
        timestamp: new Date(),
        results: mockScanResults.length,
        status: 'completed'
      };
      setScanHistory(prev => [newScan, ...prev.slice(0, 9)]);

    } catch (error) {
      setError('حدث خطأ أثناء الفحص: ' + error.message);
    } finally {
      setIsScanning(false);
    }
  };

  // إيقاف الفحص
  const stopScan = () => {
    setIsScanning(false);
    setScanProgress(0);
  };

  // عرض تفاصيل المضيف
  const showHostDetails = (host) => {
    setSelectedHost(host);
    setShowDetails(true);
  };

  // تصدير النتائج
  const exportResults = () => {
    const data = {
      scan_config: scanConfig,
      timestamp: new Date().toISOString(),
      results: scanResults,
      statistics: scanStats
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `network_scan_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // لون حالة المنفذ
  const getPortStateColor = (state) => {
    switch (state) {
      case 'open': return '#00ff41';
      case 'closed': return '#ff0040';
      case 'filtered': return '#ffaa00';
      default: return '#cccccc';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* العنوان */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #00ff41, #00ccff)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 1
          }}
        >
          فحص الشبكة
        </Typography>
        <Typography variant="h6" color="text.secondary">
          اكتشاف الأجهزة والخدمات في الشبكة
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* إعدادات الفحص */}
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <NetworkCheck sx={{ color: '#00ccff', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  إعدادات الفحص
                </Typography>
              </Box>

              {/* الهدف */}
              <TextField
                fullWidth
                label="هدف الفحص"
                value={scanConfig.target}
                onChange={(e) => handleConfigChange('target', e.target.value)}
                placeholder="***********/24 أو example.com"
                disabled={isScanning}
                sx={{ mb: 3 }}
                helperText="IP، CIDR، أو اسم النطاق"
              />

              {/* نوع الفحص */}
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>نوع الفحص</InputLabel>
                <Select
                  value={scanConfig.scanType}
                  onChange={(e) => handleConfigChange('scanType', e.target.value)}
                  disabled={isScanning}
                >
                  {scanTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      <Box>
                        <Typography variant="body2">{type.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {type.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* المنافذ */}
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>المنافذ</InputLabel>
                <Select
                  value={scanConfig.ports}
                  onChange={(e) => handleConfigChange('ports', e.target.value)}
                  disabled={isScanning}
                >
                  {portOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* منافذ مخصصة */}
              {scanConfig.ports === 'custom' && (
                <TextField
                  fullWidth
                  label="المنافذ المخصصة"
                  value={scanConfig.customPorts}
                  onChange={(e) => handleConfigChange('customPorts', e.target.value)}
                  placeholder="80,443,8080-8090"
                  disabled={isScanning}
                  sx={{ mb: 3 }}
                />
              )}

              {/* التوقيت */}
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>سرعة الفحص</InputLabel>
                <Select
                  value={scanConfig.timing}
                  onChange={(e) => handleConfigChange('timing', e.target.value)}
                  disabled={isScanning}
                >
                  {timingOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      <Box>
                        <Typography variant="body2">{option.label}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.description}
                        </Typography>
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* أزرار التحكم */}
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={startScan}
                  disabled={isScanning}
                  startIcon={<PlayArrow />}
                  sx={{
                    flex: 1,
                    background: 'linear-gradient(45deg, #00ff41, #00ccff)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #00cc33, #0099cc)'
                    }
                  }}
                >
                  {isScanning ? 'جاري الفحص...' : 'بدء الفحص'}
                </Button>
                
                {isScanning && (
                  <Button
                    variant="outlined"
                    onClick={stopScan}
                    startIcon={<Stop />}
                    color="error"
                  >
                    إيقاف
                  </Button>
                )}
              </Box>

              {/* شريط التقدم */}
              {isScanning && (
                <Box sx={{ mt: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">التقدم</Typography>
                    <Typography variant="body2">{Math.round(scanProgress)}%</Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={scanProgress}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: 'rgba(0, 255, 65, 0.1)',
                      '& .MuiLinearProgress-bar': {
                        background: 'linear-gradient(90deg, #00ff41, #00ccff)',
                        borderRadius: 4
                      }
                    }}
                  />
                </Box>
              )}

              {/* رسالة الخطأ */}
              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* النتائج والإحصائيات */}
        <Grid item xs={12} md={8}>
          {/* الإحصائيات */}
          {scanResults.length > 0 && (
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={6} sm={3}>
                <Card sx={{ backgroundColor: 'rgba(0, 255, 65, 0.1)', border: '1px solid rgba(0, 255, 65, 0.3)' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Computer sx={{ fontSize: 40, color: '#00ff41', mb: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {scanStats.activeHosts}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      أجهزة نشطة
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={6} sm={3}>
                <Card sx={{ backgroundColor: 'rgba(0, 204, 255, 0.1)', border: '1px solid rgba(0, 204, 255, 0.3)' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Router sx={{ fontSize: 40, color: '#00ccff', mb: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {scanStats.openPorts}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      منافذ مفتوحة
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={6} sm={3}>
                <Card sx={{ backgroundColor: 'rgba(255, 170, 0, 0.1)', border: '1px solid rgba(255, 170, 0, 0.3)' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Security sx={{ fontSize: 40, color: '#ffaa00', mb: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {scanStats.services}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      خدمات مكتشفة
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={6} sm={3}>
                <Card sx={{ backgroundColor: 'rgba(255, 0, 64, 0.1)', border: '1px solid rgba(255, 0, 64, 0.3)' }}>
                  <CardContent sx={{ textAlign: 'center', py: 2 }}>
                    <Warning sx={{ fontSize: 40, color: '#ff0040', mb: 1 }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                      {scanResults.reduce((total, host) => total + (host.vulnerabilities?.length || 0), 0)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ثغرات محتملة
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* جدول النتائج */}
          {scanResults.length > 0 && (
            <Card
              sx={{
                backgroundColor: 'rgba(26, 26, 46, 0.9)',
                border: '1px solid rgba(0, 255, 65, 0.3)',
                borderRadius: 2
              }}
            >
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    نتائج الفحص
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<Download />}
                    onClick={exportResults}
                    size="small"
                  >
                    تصدير
                  </Button>
                </Box>

                <TableContainer component={Paper} sx={{ backgroundColor: 'transparent' }}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>عنوان IP</TableCell>
                        <TableCell>اسم المضيف</TableCell>
                        <TableCell>نظام التشغيل</TableCell>
                        <TableCell>المنافذ المفتوحة</TableCell>
                        <TableCell>الثغرات</TableCell>
                        <TableCell>الإجراءات</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {scanResults.map((host, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {host.ip}
                            </Typography>
                          </TableCell>
                          <TableCell>{host.hostname || '-'}</TableCell>
                          <TableCell>{host.os || '-'}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                              {host.ports.slice(0, 3).map((port, i) => (
                                <Chip
                                  key={i}
                                  label={port.port}
                                  size="small"
                                  sx={{
                                    backgroundColor: getPortStateColor(port.state) + '20',
                                    color: getPortStateColor(port.state),
                                    border: `1px solid ${getPortStateColor(port.state)}40`
                                  }}
                                />
                              ))}
                              {host.ports.length > 3 && (
                                <Chip
                                  label={`+${host.ports.length - 3}`}
                                  size="small"
                                  variant="outlined"
                                />
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            {host.vulnerabilities?.length > 0 ? (
                              <Chip
                                label={host.vulnerabilities.length}
                                size="small"
                                color="error"
                              />
                            ) : (
                              <Chip
                                label="0"
                                size="small"
                                color="success"
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Tooltip title="عرض التفاصيل">
                              <IconButton
                                size="small"
                                onClick={() => showHostDetails(host)}
                              >
                                <Visibility />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}

          {/* رسالة عدم وجود نتائج */}
          {!isScanning && scanResults.length === 0 && (
            <Card
              sx={{
                backgroundColor: 'rgba(26, 26, 46, 0.9)',
                border: '1px solid rgba(0, 255, 65, 0.3)',
                borderRadius: 2,
                textAlign: 'center',
                py: 8
              }}
            >
              <NetworkCheck sx={{ fontSize: 80, color: '#00ccff', mb: 2, opacity: 0.5 }} />
              <Typography variant="h6" color="text.secondary">
                لا توجد نتائج فحص
              </Typography>
              <Typography variant="body2" color="text.secondary">
                ابدأ فحصاً جديداً لعرض النتائج هنا
              </Typography>
            </Card>
          )}
        </Grid>
      </Grid>

      {/* نافذة تفاصيل المضيف */}
      <Dialog
        open={showDetails}
        onClose={() => setShowDetails(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          تفاصيل المضيف: {selectedHost?.ip}
        </DialogTitle>
        <DialogContent>
          {selectedHost && (
            <Box>
              {/* معلومات أساسية */}
              <Typography variant="h6" sx={{ mb: 2 }}>
                معلومات أساسية
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    عنوان IP:
                  </Typography>
                  <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                    {selectedHost.ip}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    اسم المضيف:
                  </Typography>
                  <Typography variant="body1">
                    {selectedHost.hostname || 'غير محدد'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    نظام التشغيل:
                  </Typography>
                  <Typography variant="body1">
                    {selectedHost.os || 'غير محدد'}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    الحالة:
                  </Typography>
                  <Chip
                    label={selectedHost.status === 'up' ? 'نشط' : 'غير نشط'}
                    color={selectedHost.status === 'up' ? 'success' : 'error'}
                    size="small"
                  />
                </Grid>
              </Grid>

              {/* المنافذ والخدمات */}
              <Typography variant="h6" sx={{ mb: 2 }}>
                المنافذ والخدمات
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 3 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>المنفذ</TableCell>
                      <TableCell>البروتوكول</TableCell>
                      <TableCell>الحالة</TableCell>
                      <TableCell>الخدمة</TableCell>
                      <TableCell>الإصدار</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedHost.ports.map((port, index) => (
                      <TableRow key={index}>
                        <TableCell>{port.port}</TableCell>
                        <TableCell>{port.protocol}</TableCell>
                        <TableCell>
                          <Chip
                            label={port.state}
                            size="small"
                            sx={{
                              backgroundColor: getPortStateColor(port.state) + '20',
                              color: getPortStateColor(port.state)
                            }}
                          />
                        </TableCell>
                        <TableCell>{port.service || '-'}</TableCell>
                        <TableCell>{port.version || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* الثغرات */}
              {selectedHost.vulnerabilities && selectedHost.vulnerabilities.length > 0 && (
                <>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    الثغرات المحتملة
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    {selectedHost.vulnerabilities.map((vuln, index) => (
                      <Chip
                        key={index}
                        label={vuln}
                        color="error"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetails(false)}>
            إغلاق
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default NetworkScanner;
