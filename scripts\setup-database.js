const { Client } = require('pg');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// إعدادات قاعدة البيانات
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'cybersentinel',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASS || 'password'
};

// إنشاء اتصال قاعدة البيانات
const createDatabaseConnection = async () => {
  const client = new Client(DB_CONFIG);
  
  try {
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    return client;
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    throw error;
  }
};

// إنشاء الجداول
const createTables = async (client) => {
  console.log('📊 إنشاء الجداول...');

  // جدول المستخدمين
  const usersTable = `
    CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      username VARCHAR(50) UNIQUE NOT NULL,
      email TEXT NOT NULL,
      password_hash TEXT NOT NULL,
      phone TEXT,
      full_name TEXT,
      is_admin BOOLEAN DEFAULT FALSE,
      is_verified BOOLEAN DEFAULT FALSE,
      two_factor_secret TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      last_login TIMESTAMP,
      login_attempts INTEGER DEFAULT 0,
      locked_until TIMESTAMP,
      verification_code VARCHAR(6),
      reset_token TEXT,
      reset_token_expires TIMESTAMP
    );
  `;

  // جدول الجلسات
  const sessionsTable = `
    CREATE TABLE IF NOT EXISTS sessions (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      session_token TEXT UNIQUE NOT NULL,
      ip_address INET,
      user_agent TEXT,
      device_fingerprint TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      expires_at TIMESTAMP NOT NULL,
      last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      is_active BOOLEAN DEFAULT TRUE
    );
  `;

  // جدول سجل الأمان
  const securityLogTable = `
    CREATE TABLE IF NOT EXISTS security_log (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE SET NULL,
      event_type VARCHAR(50) NOT NULL,
      threat_level VARCHAR(20) NOT NULL,
      message TEXT NOT NULL,
      ip_address INET,
      user_agent TEXT,
      additional_data JSONB,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // جدول الفحوصات
  const scansTable = `
    CREATE TABLE IF NOT EXISTS scans (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      scan_type VARCHAR(50) NOT NULL,
      target TEXT NOT NULL,
      status VARCHAR(20) DEFAULT 'pending',
      started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP,
      results JSONB,
      findings_count INTEGER DEFAULT 0,
      severity_high INTEGER DEFAULT 0,
      severity_medium INTEGER DEFAULT 0,
      severity_low INTEGER DEFAULT 0,
      scan_config JSONB,
      error_message TEXT
    );
  `;

  // جدول التقارير
  const reportsTable = `
    CREATE TABLE IF NOT EXISTS reports (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      report_type VARCHAR(50) NOT NULL,
      format VARCHAR(10) NOT NULL,
      content JSONB,
      file_path TEXT,
      file_size BIGINT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      is_public BOOLEAN DEFAULT FALSE,
      download_count INTEGER DEFAULT 0
    );
  `;

  // جدول الإعدادات
  const settingsTable = `
    CREATE TABLE IF NOT EXISTS settings (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      category VARCHAR(50) NOT NULL,
      key VARCHAR(100) NOT NULL,
      value JSONB,
      is_encrypted BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(user_id, category, key)
    );
  `;

  // جدول الإشعارات
  const notificationsTable = `
    CREATE TABLE IF NOT EXISTS notifications (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      title VARCHAR(255) NOT NULL,
      message TEXT NOT NULL,
      type VARCHAR(50) NOT NULL,
      priority VARCHAR(20) DEFAULT 'medium',
      is_read BOOLEAN DEFAULT FALSE,
      action_url TEXT,
      expires_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  // جدول API Keys
  const apiKeysTable = `
    CREATE TABLE IF NOT EXISTS api_keys (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      name VARCHAR(100) NOT NULL,
      key_hash TEXT NOT NULL,
      permissions JSONB,
      last_used TIMESTAMP,
      expires_at TIMESTAMP,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  const tables = [
    { name: 'users', sql: usersTable },
    { name: 'sessions', sql: sessionsTable },
    { name: 'security_log', sql: securityLogTable },
    { name: 'scans', sql: scansTable },
    { name: 'reports', sql: reportsTable },
    { name: 'settings', sql: settingsTable },
    { name: 'notifications', sql: notificationsTable },
    { name: 'api_keys', sql: apiKeysTable }
  ];

  for (const table of tables) {
    try {
      await client.query(table.sql);
      console.log(`✅ تم إنشاء جدول ${table.name}`);
    } catch (error) {
      console.error(`❌ خطأ في إنشاء جدول ${table.name}:`, error.message);
      throw error;
    }
  }
};

// إنشاء الفهارس
const createIndexes = async (client) => {
  console.log('📇 إنشاء الفهارس...');

  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);',
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);',
    'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);',
    'CREATE INDEX IF NOT EXISTS idx_security_log_user_id ON security_log(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_security_log_event_type ON security_log(event_type);',
    'CREATE INDEX IF NOT EXISTS idx_security_log_created_at ON security_log(created_at);',
    'CREATE INDEX IF NOT EXISTS idx_scans_user_id ON scans(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_scans_status ON scans(status);',
    'CREATE INDEX IF NOT EXISTS idx_scans_created_at ON scans(started_at);',
    'CREATE INDEX IF NOT EXISTS idx_reports_user_id ON reports(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_settings_user_id ON settings(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);'
  ];

  for (const index of indexes) {
    try {
      await client.query(index);
    } catch (error) {
      console.error('❌ خطأ في إنشاء فهرس:', error.message);
    }
  }

  console.log('✅ تم إنشاء الفهارس');
};

// إنشاء المستخدم الافتراضي (admin)
const createDefaultUser = async (client) => {
  console.log('👤 إنشاء المستخدم الافتراضي...');

  const adminUsername = 'admin';
  const adminPassword = 'JaMaL@123';
  const adminEmail = '<EMAIL>';

  // التحقق من وجود المستخدم
  const existingUser = await client.query(
    'SELECT id FROM users WHERE username = $1',
    [adminUsername]
  );

  if (existingUser.rows.length > 0) {
    console.log('ℹ️ المستخدم الافتراضي موجود بالفعل');
    return;
  }

  // تشفير كلمة المرور
  const passwordHash = await bcrypt.hash(adminPassword, 12);

  // إنشاء سر التحقق بخطوتين
  const twoFactorSecret = crypto.randomBytes(32).toString('base64');

  // إدراج المستخدم
  const insertUser = `
    INSERT INTO users (
      username, email, password_hash, full_name, 
      is_admin, is_verified, two_factor_secret
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING id;
  `;

  try {
    const result = await client.query(insertUser, [
      adminUsername,
      adminEmail,
      passwordHash,
      'مدير النظام',
      true,
      true,
      twoFactorSecret
    ]);

    console.log('✅ تم إنشاء المستخدم الافتراضي:');
    console.log(`   اسم المستخدم: ${adminUsername}`);
    console.log(`   كلمة المرور: ${adminPassword}`);
    console.log(`   البريد الإلكتروني: ${adminEmail}`);
    console.log('⚠️ يرجى تغيير كلمة المرور بعد أول تسجيل دخول');

    return result.rows[0].id;
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الافتراضي:', error.message);
    throw error;
  }
};

// إنشاء الإعدادات الافتراضية
const createDefaultSettings = async (client, userId) => {
  console.log('⚙️ إنشاء الإعدادات الافتراضية...');

  const defaultSettings = [
    {
      category: 'security',
      key: 'session_timeout',
      value: { minutes: 30 }
    },
    {
      category: 'security',
      key: 'max_login_attempts',
      value: { attempts: 5 }
    },
    {
      category: 'security',
      key: 'password_policy',
      value: {
        min_length: 8,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_symbols: true
      }
    },
    {
      category: 'scanning',
      key: 'default_nmap_options',
      value: { options: ['-sS', '-O', '-sV', '--script=default'] }
    },
    {
      category: 'scanning',
      key: 'max_concurrent_scans',
      value: { count: 5 }
    },
    {
      category: 'notifications',
      key: 'email_alerts',
      value: { enabled: true }
    },
    {
      category: 'reports',
      key: 'auto_cleanup_days',
      value: { days: 30 }
    }
  ];

  const insertSetting = `
    INSERT INTO settings (user_id, category, key, value)
    VALUES ($1, $2, $3, $4)
    ON CONFLICT (user_id, category, key) DO NOTHING;
  `;

  for (const setting of defaultSettings) {
    try {
      await client.query(insertSetting, [
        userId,
        setting.category,
        setting.key,
        JSON.stringify(setting.value)
      ]);
    } catch (error) {
      console.error(`❌ خطأ في إنشاء إعداد ${setting.key}:`, error.message);
    }
  }

  console.log('✅ تم إنشاء الإعدادات الافتراضية');
};

// إنشاء مجلدات النظام
const createSystemDirectories = () => {
  console.log('📁 إنشاء مجلدات النظام...');

  const directories = [
    'uploads',
    'reports',
    'logs',
    'temp',
    'backups',
    'ssl',
    'tools'
  ];

  for (const dir of directories) {
    const dirPath = path.join(__dirname, '..', dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`✅ تم إنشاء مجلد: ${dir}`);
    }
  }
};

// الدالة الرئيسية
const setupDatabase = async () => {
  console.log('🚀 بدء إعداد قاعدة البيانات...');
  console.log('=' .repeat(50));

  let client;

  try {
    // إنشاء الاتصال
    client = await createDatabaseConnection();

    // إنشاء الجداول
    await createTables(client);

    // إنشاء الفهارس
    await createIndexes(client);

    // إنشاء المستخدم الافتراضي
    const adminUserId = await createDefaultUser(client);

    // إنشاء الإعدادات الافتراضية
    if (adminUserId) {
      await createDefaultSettings(client, adminUserId);
    }

    // إنشاء مجلدات النظام
    createSystemDirectories();

    console.log('=' .repeat(50));
    console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('');
    console.log('📋 معلومات مهمة:');
    console.log('   - اسم المستخدم: admin');
    console.log('   - كلمة المرور: JaMaL@123');
    console.log('   - يرجى تغيير كلمة المرور بعد أول تسجيل دخول');
    console.log('   - تأكد من إعداد التحقق بخطوتين');
    console.log('');

  } catch (error) {
    console.error('💥 فشل في إعداد قاعدة البيانات:', error.message);
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
      console.log('🔌 تم إغلاق اتصال قاعدة البيانات');
    }
  }
};

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
