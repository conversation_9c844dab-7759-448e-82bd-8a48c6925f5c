@echo off
title Cyber Sentinel Pro - Simple Start
color 0A

echo.
echo 🛡️ CYBER SENTINEL PRO - SIMPLE START
echo ====================================
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo Please install Node.js from: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Create minimal package.json
echo Creating minimal package.json...
echo {> package.json
echo   "name": "cyber-sentinel-pro",>> package.json
echo   "version": "1.0.0",>> package.json
echo   "scripts": {>> package.json
echo     "start": "node server.js">> package.json
echo   },>> package.json
echo   "dependencies": {}>> package.json
echo }>> package.json

REM Create simple server
echo Creating simple server...
echo const http = require('http');> server.js
echo const fs = require('fs');>> server.js
echo const path = require('path');>> server.js
echo.>> server.js
echo const server = http.createServer((req, res) =^> {>> server.js
echo   if (req.url === '/' ^|^| req.url === '/index.html') {>> server.js
echo     res.writeHead(200, {'Content-Type': 'text/html'});>> server.js
echo     res.end(`>> server.js
echo     ^<!DOCTYPE html^>>> server.js
echo     ^<html^>>> server.js
echo     ^<head^>>> server.js
echo       ^<title^>Cyber Sentinel Pro^</title^>>> server.js
echo       ^<style^>>> server.js
echo         body { background: #0a0a0a; color: #00ff41; font-family: monospace; padding: 20px; }>> server.js
echo         .container { max-width: 800px; margin: 0 auto; text-align: center; }>> server.js
echo         .logo { font-size: 3em; margin: 20px 0; }>> server.js
echo         .login { background: #1a1a2e; padding: 20px; border-radius: 10px; margin: 20px 0; }>> server.js
echo         input { background: #333; color: #fff; border: 1px solid #00ff41; padding: 10px; margin: 5px; }>> server.js
echo         button { background: #00ff41; color: #000; border: none; padding: 10px 20px; cursor: pointer; }>> server.js
echo       ^</style^>>> server.js
echo     ^</head^>>> server.js
echo     ^<body^>>> server.js
echo       ^<div class="container"^>>> server.js
echo         ^<div class="logo"^>🛡️ CYBER SENTINEL PRO^</div^>>> server.js
echo         ^<h2^>SecOps Edition - Advanced Cybersecurity Testing Platform^</h2^>>> server.js
echo         ^<div class="login"^>>> server.js
echo           ^<h3^>تسجيل الدخول^</h3^>>> server.js
echo           ^<form^>>> server.js
echo             ^<div^>^<input type="text" placeholder="اسم المستخدم" value="admin"^>^</div^>>> server.js
echo             ^<div^>^<input type="password" placeholder="كلمة المرور" value="JaMaL@123"^>^</div^>>> server.js
echo             ^<div^>^<button type="button" onclick="login()"^>دخول^</button^>^</div^>>> server.js
echo           ^</form^>>> server.js
echo         ^</div^>>> server.js
echo         ^<div id="dashboard" style="display:none;"^>>> server.js
echo           ^<h2^>🎯 لوحة التحكم الرئيسية^</h2^>>> server.js
echo           ^<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;"^>>> server.js
echo             ^<div style="background: #1a1a2e; padding: 20px; border-radius: 10px;"^>>> server.js
echo               ^<h3^>🤖 الذكاء الاصطناعي^</h3^>>> server.js
echo               ^<p^>كشف التهديدات تلقائياً^</p^>>> server.js
echo             ^</div^>>> server.js
echo             ^<div style="background: #1a1a2e; padding: 20px; border-radius: 10px;"^>>> server.js
echo               ^<h3^>🎮 التصور ثلاثي الأبعاد^</h3^>>> server.js
echo               ^<p^>خريطة شبكة تفاعلية^</p^>>> server.js
echo             ^</div^>>> server.js
echo             ^<div style="background: #1a1a2e; padding: 20px; border-radius: 10px;"^>>> server.js
echo               ^<h3^>🍯 نظام Honeypot^</h3^>>> server.js
echo               ^<p^>فخاخ ذكية متعددة^</p^>>> server.js
echo             ^</div^>>> server.js
echo             ^<div style="background: #1a1a2e; padding: 20px; border-radius: 10px;"^>>> server.js
echo               ^<h3^>📊 المراقبة المباشرة^</h3^>>> server.js
echo               ^<p^>رسوم بيانية حية^</p^>>> server.js
echo             ^</div^>>> server.js
echo           ^</div^>>> server.js
echo           ^<button onclick="logout()"^>تسجيل خروج^</button^>>> server.js
echo         ^</div^>>> server.js
echo       ^</div^>>> server.js
echo       ^<script^>>> server.js
echo         function login() {>> server.js
echo           document.querySelector('.login').style.display = 'none';>> server.js
echo           document.getElementById('dashboard').style.display = 'block';>> server.js
echo         }>> server.js
echo         function logout() {>> server.js
echo           document.querySelector('.login').style.display = 'block';>> server.js
echo           document.getElementById('dashboard').style.display = 'none';>> server.js
echo         }>> server.js
echo       ^</script^>>> server.js
echo     ^</body^>>> server.js
echo     ^</html^>^`);>> server.js
echo   } else {>> server.js
echo     res.writeHead(404, {'Content-Type': 'text/plain'});>> server.js
echo     res.end('Not Found');>> server.js
echo   }>> server.js
echo });>> server.js
echo.>> server.js
echo const PORT = 3000;>> server.js
echo server.listen(PORT, () =^> {>> server.js
echo   console.log(`🛡️ Cyber Sentinel Pro running on http://localhost:${PORT}`);>> server.js
echo   console.log('Login: admin / JaMaL@123');>> server.js
echo });>> server.js

echo.
echo ✅ Files created successfully!
echo.
echo 🚀 Starting Cyber Sentinel Pro...
echo 📍 URL: http://localhost:3000
echo 🔑 Login: admin / JaMaL@123
echo.

REM Open browser
start "" timeout /t 3 /nobreak >nul && start http://localhost:3000

REM Start server
node server.js

pause
