version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cybersentinel-db
    environment:
      POSTGRES_DB: cybersentinel
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD:-CyberSentinel2024!}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - cybersentinel-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d cybersentinel"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cybersentinel-redis
    command: redis-server --requirepass ${REDIS_PASSWORD:-CyberSentinel2024!}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - cybersentinel-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Main Application
  app:
    build:
      context: .
      target: production
    container_name: cybersentinel-app
    environment:
      NODE_ENV: production
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: cybersentinel
      DB_USER: admin
      DB_PASS: ${DB_PASSWORD:-CyberSentinel2024!}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASS: ${REDIS_PASSWORD:-CyberSentinel2024!}
      JWT_SECRET: ${JWT_SECRET:-CyberSentinel-JWT-Secret-2024}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-CyberSentinel-Encryption-Key-2024}
      SESSION_SECRET: ${SESSION_SECRET:-CyberSentinel-Session-Secret-2024}
    volumes:
      - app_uploads:/app/uploads
      - app_reports:/app/reports
      - app_logs:/app/logs
      - app_temp:/app/temp
      - app_backups:/app/backups
    ports:
      - "5000:5000"
    networks:
      - cybersentinel-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Security Scanner Service
  scanner:
    build:
      context: .
      target: security-tools
    container_name: cybersentinel-scanner
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: cybersentinel
      DB_USER: admin
      DB_PASS: ${DB_PASSWORD:-CyberSentinel2024!}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASS: ${REDIS_PASSWORD:-CyberSentinel2024!}
    volumes:
      - app_uploads:/app/uploads
      - app_reports:/app/reports
      - app_logs:/app/logs
      - app_temp:/app/temp
      - scanner_tools:/app/tools
    networks:
      - cybersentinel-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    cap_add:
      - NET_ADMIN
      - NET_RAW
    privileged: true

  # Android Testing Service (Optional)
  android-tools:
    build:
      context: .
      target: android-tools
    container_name: cybersentinel-android
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: cybersentinel
      DB_USER: admin
      DB_PASS: ${DB_PASSWORD:-CyberSentinel2024!}
    volumes:
      - app_uploads:/app/uploads
      - app_reports:/app/reports
      - android_tools:/app/android-tools
    networks:
      - cybersentinel-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    profiles:
      - android

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: cybersentinel-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - app_reports:/var/www/reports
    ports:
      - "80:80"
      - "443:443"
    networks:
      - cybersentinel-network
    depends_on:
      - app
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: cybersentinel-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - cybersentinel-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: cybersentinel-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-CyberSentinel2024!}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    networks:
      - cybersentinel-network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles:
      - monitoring

  # ELK Stack for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: cybersentinel-elasticsearch
    environment:
      discovery.type: single-node
      ES_JAVA_OPTS: "-Xms512m -Xmx512m"
      xpack.security.enabled: false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - cybersentinel-network
    restart: unless-stopped
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: cybersentinel-logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - app_logs:/app/logs
    networks:
      - cybersentinel-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: cybersentinel-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - cybersentinel-network
    depends_on:
      - elasticsearch
    restart: unless-stopped
    profiles:
      - logging

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: cybersentinel-backup
    environment:
      PGPASSWORD: ${DB_PASSWORD:-CyberSentinel2024!}
    volumes:
      - app_backups:/backups
      - ./scripts/backup.sh:/backup.sh
    networks:
      - cybersentinel-network
    depends_on:
      - postgres
    restart: "no"
    profiles:
      - backup
    command: /bin/sh -c "chmod +x /backup.sh && /backup.sh"

networks:
  cybersentinel-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_reports:
    driver: local
  app_logs:
    driver: local
  app_temp:
    driver: local
  app_backups:
    driver: local
  scanner_tools:
    driver: local
  android_tools:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
