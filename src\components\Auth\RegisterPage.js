import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  LinearProgress,
  Chip,
  Stepper,
  Step,
  StepLabel,
  Dialog,
  DialogContent,
  DialogTitle
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  Lock,
  Email,
  Phone,
  AccountCircle,
  QrCode,
  Security
} from '@mui/icons-material';
import { Link } from 'react-router-dom';

const RegisterPage = ({ onRegister }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    fullName: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [qrCodeData, setQrCodeData] = useState(null);
  const [showQrDialog, setShowQrDialog] = useState(false);

  const steps = [
    'المعلومات الأساسية',
    'كلمة المرور',
    'التحقق بخطوتين'
  ];

  // تحديث قوة كلمة المرور
  useEffect(() => {
    const calculatePasswordStrength = (password) => {
      let strength = 0;
      if (password.length >= 8) strength += 20;
      if (/[A-Z]/.test(password)) strength += 20;
      if (/[a-z]/.test(password)) strength += 20;
      if (/[0-9]/.test(password)) strength += 20;
      if (/[^A-Za-z0-9]/.test(password)) strength += 20;
      return strength;
    };

    setPasswordStrength(calculatePasswordStrength(formData.password));
  }, [formData.password]);

  // معالج تغيير الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  // التحقق من صحة البيانات
  const validateStep = (step) => {
    switch (step) {
      case 0:
        if (!formData.fullName.trim()) {
          setError('الاسم الكامل مطلوب');
          return false;
        }
        if (!formData.username.trim()) {
          setError('اسم المستخدم مطلوب');
          return false;
        }
        if (formData.username.length < 3) {
          setError('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
          return false;
        }
        if (!formData.email.trim()) {
          setError('البريد الإلكتروني مطلوب');
          return false;
        }
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          setError('البريد الإلكتروني غير صالح');
          return false;
        }
        if (!formData.phone.trim()) {
          setError('رقم الهاتف مطلوب');
          return false;
        }
        return true;

      case 1:
        if (!formData.password) {
          setError('كلمة المرور مطلوبة');
          return false;
        }
        if (passwordStrength < 80) {
          setError('كلمة المرور ضعيفة جداً');
          return false;
        }
        if (formData.password !== formData.confirmPassword) {
          setError('كلمات المرور غير متطابقة');
          return false;
        }
        return true;

      default:
        return true;
    }
  };

  // الانتقال للخطوة التالية
  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  // العودة للخطوة السابقة
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateStep(activeStep)) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await onRegister(formData);
      
      if (result.success) {
        setSuccess('تم إنشاء الحساب بنجاح!');
        
        if (result.qrCode) {
          setQrCodeData({
            qrCode: result.qrCode,
            manualEntryKey: result.manualEntryKey
          });
          setShowQrDialog(true);
        }
        
        // إعادة تعيين النموذج
        setFormData({
          username: '',
          email: '',
          password: '',
          confirmPassword: '',
          phone: '',
          fullName: ''
        });
        setActiveStep(0);
      } else {
        setError(result.error || 'خطأ في إنشاء الحساب');
      }
    } catch (error) {
      setError('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // لون قوة كلمة المرور
  const getPasswordStrengthColor = () => {
    if (passwordStrength < 40) return 'error';
    if (passwordStrength < 80) return 'warning';
    return 'success';
  };

  // نص قوة كلمة المرور
  const getPasswordStrengthText = () => {
    if (passwordStrength < 40) return 'ضعيف';
    if (passwordStrength < 80) return 'متوسط';
    return 'قوي';
  };

  // محتوى الخطوة الحالية
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <>
            <TextField
              fullWidth
              name="fullName"
              label="الاسم الكامل"
              value={formData.fullName}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <AccountCircle color="primary" />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              fullWidth
              name="username"
              label="اسم المستخدم"
              value={formData.username}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="primary" />
                  </InputAdornment>
                ),
              }}
              helperText="يجب أن يكون 3 أحرف على الأقل"
            />

            <TextField
              fullWidth
              name="email"
              type="email"
              label="البريد الإلكتروني"
              value={formData.email}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email color="primary" />
                  </InputAdornment>
                ),
              }}
            />

            <TextField
              fullWidth
              name="phone"
              label="رقم الهاتف"
              value={formData.phone}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Phone color="primary" />
                  </InputAdornment>
                ),
              }}
              helperText="مطلوب للتحقق من الهوية"
            />
          </>
        );

      case 1:
        return (
          <>
            <TextField
              fullWidth
              name="password"
              type={showPassword ? 'text' : 'password'}
              label="كلمة المرور"
              value={formData.password}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="primary" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* مؤشر قوة كلمة المرور */}
            {formData.password && (
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    قوة كلمة المرور:
                  </Typography>
                  <Chip
                    label={getPasswordStrengthText()}
                    color={getPasswordStrengthColor()}
                    size="small"
                  />
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={passwordStrength}
                  color={getPasswordStrengthColor()}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  يجب أن تحتوي على: حرف كبير، حرف صغير، رقم، رمز خاص، 8 أحرف على الأقل
                </Typography>
              </Box>
            )}

            <TextField
              fullWidth
              name="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              label="تأكيد كلمة المرور"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="primary" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              error={formData.confirmPassword && formData.password !== formData.confirmPassword}
              helperText={
                formData.confirmPassword && formData.password !== formData.confirmPassword
                  ? 'كلمات المرور غير متطابقة'
                  : ''
              }
            />
          </>
        );

      case 2:
        return (
          <Box sx={{ textAlign: 'center' }}>
            <Security
              sx={{
                fontSize: 60,
                color: 'primary.main',
                mb: 2,
                filter: 'drop-shadow(0 0 10px #00ff41)'
              }}
            />
            <Typography variant="h6" sx={{ mb: 2 }}>
              التحقق بخطوتين
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              سيتم إعداد التحقق بخطوتين بعد إنشاء الحساب لحماية إضافية
            </Typography>
            
            <Alert
              severity="info"
              sx={{
                backgroundColor: 'rgba(0, 204, 255, 0.1)',
                border: '1px solid rgba(0, 204, 255, 0.3)',
                '& .MuiAlert-icon': {
                  color: '#00ccff'
                }
              }}
            >
              ستحتاج إلى تطبيق مصادقة مثل Google Authenticator أو Authy
            </Alert>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
        position: 'relative'
      }}
    >
      {/* تأثير الشبكة السيبرانية */}
      <Box
        className="cyber-grid"
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          pointerEvents: 'none'
        }}
      />

      <Card
        sx={{
          maxWidth: 500,
          width: '100%',
          backgroundColor: 'rgba(26, 26, 46, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(0, 255, 65, 0.3)',
          borderRadius: 3,
          boxShadow: '0 20px 40px rgba(0, 255, 65, 0.1)',
          position: 'relative',
          overflow: 'hidden'
        }}
        className="fade-in"
      >
        {/* شريط التحميل */}
        {loading && (
          <LinearProgress
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              backgroundColor: 'rgba(0, 255, 65, 0.1)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#00ff41'
              }
            }}
          />
        )}

        <CardContent sx={{ padding: 4 }}>
          {/* العنوان */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography
              variant="h4"
              component="h1"
              className="cyber-glow"
              sx={{ mb: 1, fontWeight: 'bold' }}
            >
              إنشاء حساب جديد
            </Typography>
            <Typography variant="body2" color="text.secondary">
              انضم إلى منصة Cyber Sentinel Pro
            </Typography>
          </Box>

          {/* مؤشر الخطوات */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel
                  sx={{
                    '& .MuiStepLabel-label': {
                      color: 'text.secondary',
                      fontSize: '0.875rem'
                    },
                    '& .MuiStepLabel-label.Mui-active': {
                      color: 'primary.main'
                    },
                    '& .MuiStepLabel-label.Mui-completed': {
                      color: 'success.main'
                    }
                  }}
                >
                  {label}
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* رسائل النجاح والخطأ */}
          {success && (
            <Alert
              severity="success"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(0, 255, 65, 0.1)',
                border: '1px solid rgba(0, 255, 65, 0.3)',
                '& .MuiAlert-icon': {
                  color: '#00ff41'
                }
              }}
            >
              {success}
            </Alert>
          )}

          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(255, 0, 64, 0.1)',
                border: '1px solid rgba(255, 0, 64, 0.3)',
                '& .MuiAlert-icon': {
                  color: '#ff0040'
                }
              }}
            >
              {error}
            </Alert>
          )}

          {/* نموذج التسجيل */}
          <Box component="form" onSubmit={handleSubmit}>
            {renderStepContent()}

            {/* أزرار التنقل */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
                sx={{ visibility: activeStep === 0 ? 'hidden' : 'visible' }}
              >
                السابق
              </Button>

              {activeStep === steps.length - 1 ? (
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  sx={{
                    background: 'linear-gradient(45deg, #00ff41 30%, #00ccff 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #00cc33 30%, #0099cc 90%)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(0, 255, 65, 0.4)'
                    }
                  }}
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء الحساب'}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  variant="contained"
                  disabled={loading}
                >
                  التالي
                </Button>
              )}
            </Box>

            <Divider sx={{ my: 3, borderColor: 'rgba(0, 255, 65, 0.2)' }} />

            {/* رابط تسجيل الدخول */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                لديك حساب بالفعل؟{' '}
                <Link
                  to="/login"
                  style={{
                    color: '#00ff41',
                    textDecoration: 'none',
                    fontWeight: 'bold'
                  }}
                >
                  تسجيل الدخول
                </Link>
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* نافذة QR Code */}
      <Dialog
        open={showQrDialog}
        onClose={() => setShowQrDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ textAlign: 'center' }}>
          إعداد التحقق بخطوتين
        </DialogTitle>
        <DialogContent sx={{ textAlign: 'center', pb: 3 }}>
          {qrCodeData && (
            <>
              <Typography variant="body2" sx={{ mb: 2 }}>
                امسح الرمز باستخدام تطبيق المصادقة:
              </Typography>
              <Box sx={{ mb: 3 }}>
                <img
                  src={qrCodeData.qrCode}
                  alt="QR Code"
                  style={{ maxWidth: '200px', border: '1px solid #00ff41' }}
                />
              </Box>
              <Typography variant="caption" color="text.secondary">
                أو أدخل هذا المفتاح يدوياً:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontFamily: 'monospace',
                  backgroundColor: 'rgba(0, 255, 65, 0.1)',
                  padding: 1,
                  borderRadius: 1,
                  mt: 1,
                  wordBreak: 'break-all'
                }}
              >
                {qrCodeData.manualEntryKey}
              </Typography>
              <Button
                onClick={() => setShowQrDialog(false)}
                variant="contained"
                sx={{ mt: 3 }}
              >
                تم
              </Button>
            </>
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default RegisterPage;
