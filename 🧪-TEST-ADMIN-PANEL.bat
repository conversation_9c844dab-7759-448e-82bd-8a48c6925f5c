@echo off
title 🧪 اختبار لوحة تحكم المدير - Cyber Sentinel Pro
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🧪  اختبار لوحة تحكم المدير - CYBER SENTINEL PRO  🧪                      █
echo █                                                                              █
echo █     ✅ فحص صلاحيات الوصول للوحة التحكم                                     █
echo █     ✅ التأكد من الأمان والحماية                                           █
echo █     ✅ اختبار جميع الوظائف                                                 █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🧪 بدء اختبار لوحة تحكم المدير...
echo.

REM Check if professional edition file exists
if exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [✅] تم العثور على النسخة المهنية
    echo.
    echo [INFO] 🌐 فتح التطبيق لاختبار لوحة التحكم...
    echo.
    echo ===============================================================================
    echo                              🎯 دليل اختبار لوحة التحكم
    echo ===============================================================================
    echo.
    echo 🔑 خطوات الاختبار:
    echo.
    echo 1️⃣ تسجيل الدخول كمدير:
    echo    👤 اسم المستخدم: admin
    echo    🔒 كلمة المرور: JaMaL@123
    echo    ✅ يجب أن يظهر زر "لوحة التحكم"
    echo.
    echo 2️⃣ اختبار لوحة التحكم:
    echo    🖱️ انقر على زر "لوحة التحكم"
    echo    ✅ يجب أن تفتح لوحة التحكم الكاملة
    echo    ✅ يجب أن تظهر جميع التبويبات (6 تبويبات)
    echo.
    echo 3️⃣ اختبار التبويبات:
    echo    👥 إدارة المستخدمين - يجب أن تظهر قائمة المستخدمين
    echo    💻 مراقبة النظام - يجب أن تظهر الإحصائيات الحقيقية
    echo    🌐 مراقبة الشبكة - يجب أن تظهر بيانات الشبكة المباشرة
    echo    🔒 الأمان المتقدم - يجب أن تعمل أدوات الفحص
    echo    📋 السجلات - يجب أن تظهر السجلات المباشرة
    echo    ⚙️ الإعدادات - يجب أن تعمل جميع الإعدادات
    echo.
    echo 4️⃣ اختبار الأمان:
    echo    🚪 سجل خروج ثم سجل دخول كمستخدم عادي
    echo    ❌ يجب ألا يظهر زر "لوحة التحكم"
    echo    ❌ يجب ألا تكون لوحة التحكم متاحة
    echo.
    echo 5️⃣ اختبار إدارة المستخدمين:
    echo    ➕ أنشئ حساب مستخدم جديد
    echo    👁️ اعرض قائمة المستخدمين في لوحة التحكم
    echo    🗑️ احذف المستخدم الجديد
    echo    📤 صدر بيانات المستخدمين
    echo.
    echo 6️⃣ اختبار المراقبة الحقيقية:
    echo    📊 تحقق من عرض استخدام الذاكرة الحقيقي
    echo    🌐 تحقق من عرض عنوان IP الحقيقي
    echo    ⏱️ تحقق من عرض وقت التشغيل الفعلي
    echo    🔍 جرب فحص المنافذ
    echo    📡 جرب قياس سرعة الإنترنت
    echo.
    echo 7️⃣ اختبار أدوات الأمان:
    echo    🔍 جرب الفحص الأمني الشامل
    echo    🛡️ جرب فحص الجدار الناري
    echo    🦠 جرب فحص البرمجيات الخبيثة
    echo    ⚔️ جرب اختبار الاختراق
    echo.
    echo ===============================================================================
    echo.
    
    REM Open the professional edition
    start "" "🛡️-CYBER-SENTINEL-PROFESSIONAL.html"
    
    echo [✅] تم فتح التطبيق للاختبار!
    echo.
    echo ===============================================================================
    echo                              ✅ نتائج الاختبار المتوقعة
    echo ===============================================================================
    echo.
    echo 🟢 للمدير (admin):
    echo    ✅ يظهر زر "لوحة التحكم"
    echo    ✅ تفتح لوحة التحكم عند النقر
    echo    ✅ جميع التبويبات تعمل
    echo    ✅ جميع الوظائف متاحة
    echo    ✅ يمكن إدارة المستخدمين
    echo    ✅ يمكن الوصول لجميع الأدوات
    echo.
    echo 🟡 للمستخدم العادي:
    echo    ❌ لا يظهر زر "لوحة التحكم"
    echo    ❌ لا يمكن الوصول للوحة التحكم
    echo    ✅ يمكن استخدام الميزات الأساسية فقط
    echo.
    echo 🟡 للمحلل الأمني:
    echo    ❌ لا يظهر زر "لوحة التحكم"
    echo    ❌ لا يمكن الوصول للوحة التحكم
    echo    ✅ يمكن استخدام أدوات التحليل المتقدمة
    echo.
    echo ===============================================================================
    echo.
    echo 🔒 اختبارات الأمان:
    echo.
    echo ✅ حماية حساب المدير:
    echo    • لا يمكن حذف حساب المدير
    echo    • لا يمكن إنشاء حسابات مدير جديدة
    echo    • حساب المدير محمي في قائمة المستخدمين
    echo.
    echo ✅ حماية لوحة التحكم:
    echo    • فحص صلاحيات قبل فتح لوحة التحكم
    echo    • فحص صلاحيات قبل إدارة المستخدمين
    echo    • فحص صلاحيات قبل حذف المستخدمين
    echo    • فحص صلاحيات قبل تصدير البيانات
    echo.
    echo ✅ إخفاء لوحة التحكم:
    echo    • زر لوحة التحكم مخفي افتراضياً
    echo    • يظهر فقط للمدير عند تسجيل الدخول
    echo    • يختفي عند تسجيل الخروج
    echo    • يختفي للمستخدمين غير المديرين
    echo.
    echo ===============================================================================
    echo.
    echo 💡 نصائح للاختبار:
    echo.
    echo 🔍 اختبر جميع السيناريوهات:
    echo    • سجل دخول كمدير واختبر جميع الوظائف
    echo    • سجل خروج وأنشئ حساب مستخدم عادي
    echo    • سجل دخول كمستخدم عادي وتأكد من عدم ظهور لوحة التحكم
    echo    • أنشئ حساب محلل أمني واختبر الصلاحيات
    echo.
    echo 📊 راقب الأداء:
    echo    • تحقق من سرعة تحميل البيانات
    echo    • راقب استهلاك الذاكرة
    echo    • اختبر الاستجابة للنقرات
    echo.
    echo 🌐 اختبر الاتصال:
    echo    • تأكد من عمل الحصول على IP الحقيقي
    echo    • اختبر قياس سرعة الإنترنت
    echo    • جرب فحص المنافذ
    echo.
    echo ===============================================================================
    echo.
    echo [INFO] ابدأ الاختبار الآن في المتصفح
    echo [INFO] اتبع الخطوات المذكورة أعلاه
    echo.
    
) else (
    echo [ERROR] ❌ لم يتم العثور على ملف النسخة المهنية!
    echo [INFO] يرجى التأكد من وجود الملف: 🛡️-CYBER-SENTINEL-PROFESSIONAL.html
    echo.
    pause
    exit /b 1
)

echo ===============================================================================
echo                              🧪 اختبار لوحة التحكم جاهز!
echo ===============================================================================
echo.
echo 🎯 ما يجب اختباره:
echo    ✅ صلاحيات الوصول للوحة التحكم
echo    ✅ أمان وحماية البيانات
echo    ✅ وظائف إدارة المستخدمين
echo    ✅ مراقبة النظام الحقيقية
echo    ✅ أدوات الأمان المتقدمة
echo    ✅ السجلات والتقارير
echo.
echo 📋 تقرير الاختبار:
echo    • سجل أي مشاكل تواجهها
echo    • تأكد من عمل جميع الوظائف
echo    • اختبر الأمان بعناية
echo.
echo 🔧 في حالة وجود مشاكل:
echo    • تحقق من وحدة التحكم في المتصفح (F12)
echo    • تأكد من تفعيل JavaScript
echo    • جرب متصفح آخر
echo.
echo ===============================================================================
echo.
echo [INFO] يمكنك إغلاق هذه النافذة بعد انتهاء الاختبار
echo.
pause
