@echo off
title Cyber Sentinel Pro - Browser Mode
color 0A

echo.
echo  ██████╗██╗   ██╗██████╗ ███████╗██████╗     ███████╗███████╗███╗   ██╗████████╗██╗███╗   ██╗███████╗██╗     
echo ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗    ██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║████╗  ██║██╔════╝██║     
echo ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝    ███████╗█████╗  ██╔██╗ ██║   ██║   ██║██╔██╗ ██║█████╗  ██║     
echo ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗    ╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██║╚██╗██║██╔══╝  ██║     
echo ╚██████╗   ██║   ██████╔╝███████╗██║  ██║    ███████║███████╗██║ ╚████║   ██║   ██║██║ ╚████║███████╗███████╗
echo  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝╚══════╝╚══════╝
echo.
echo                                    Browser Mode - SecOps Edition
echo                              Advanced Cybersecurity Testing Platform
echo.
echo ===============================================================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed!
    echo [INFO] Please install Node.js from: https://nodejs.org/
    echo [INFO] Then run this script again.
    pause
    exit /b 1
)

echo [SUCCESS] Node.js is installed!
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo [INFO] Installing dependencies for the first time...
    echo [INFO] This may take a few minutes...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed successfully!
    echo.
)

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating .env configuration file...
    copy ".env.example" ".env" >nul 2>&1
    echo [SUCCESS] Configuration file created!
    echo.
)

echo [INFO] Starting Cyber Sentinel Pro in Browser Mode...
echo.
echo ===============================================================================
echo                              BROWSER ACCESS
echo ===============================================================================
echo.
echo 🌐 The application will open automatically in your default browser
echo 📍 Manual access: http://localhost:3000
echo 🔑 Default login credentials:
echo    Username: admin
echo    Password: JaMaL@123
echo.
echo ⚠️  IMPORTANT SECURITY NOTES:
echo    - Change the default password after first login
echo    - This tool is for authorized security testing only
echo    - Do not use against systems you don't own
echo.
echo ===============================================================================
echo.

REM Start the React development server
echo [INFO] Launching React development server...
echo [INFO] Please wait while the application starts...
echo.

REM Open browser after a delay
start "" cmd /c "timeout /t 10 /nobreak >nul && start http://localhost:3000"

REM Start the React app
npm start

REM If we reach here, the server has stopped
echo.
echo [INFO] Cyber Sentinel Pro has been stopped.
echo [INFO] Thank you for using our cybersecurity platform!
pause
