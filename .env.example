# Cyber Sentinel Pro - Environment Variables
# Copy this file to .env and update the values

# Application Settings
NODE_ENV=production
PORT=5000
APP_NAME="Cyber Sentinel Pro"
APP_VERSION=1.0.0
APP_URL=https://localhost:5000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cybersentinel
DB_USER=admin
DB_PASS=CyberSentinel2024!
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASS=CyberSentinel2024!
REDIS_DB=0

# Security Keys (Generate new ones for production)
JWT_SECRET=CyberSentinel-JWT-Secret-2024-Ultra-Secure-Key
ENCRYPTION_KEY=CyberSentinel-Encryption-Key-2024-Ultra-Secure
SESSION_SECRET=CyberSentinel-Session-Secret-2024-Ultra-Secure
CRYPTO_ALGORITHM=aes-256-gcm

# Session Configuration
SESSION_TIMEOUT=1800000
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_MAX=5

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM="Cyber Sentinel Pro <<EMAIL>>"

# Two-Factor Authentication
TOTP_ISSUER="Cyber Sentinel Pro"
TOTP_WINDOW=2
TOTP_STEP=30

# File Upload Settings
UPLOAD_MAX_SIZE=104857600
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,txt,csv,json,xml
UPLOAD_PATH=./uploads

# Report Generation
REPORT_PATH=./reports
REPORT_MAX_SIZE=52428800
REPORT_RETENTION_DAYS=30

# Logging Configuration
LOG_LEVEL=info
LOG_PATH=./logs
LOG_MAX_SIZE=10485760
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# Security Tools Configuration
NMAP_PATH=/usr/bin/nmap
NMAP_TIMEOUT=300
NMAP_MAX_TARGETS=1000
NMAP_DEFAULT_OPTIONS=-sS,-O,-sV,--script=default

MASSCAN_PATH=/usr/bin/masscan
MASSCAN_RATE=1000

NIKTO_PATH=/usr/bin/nikto
SQLMAP_PATH=/usr/bin/sqlmap
HYDRA_PATH=/usr/bin/hydra

# External API Keys
VIRUSTOTAL_API_KEY=your_virustotal_api_key
SHODAN_API_KEY=your_shodan_api_key
CENSYS_API_ID=your_censys_api_id
CENSYS_API_SECRET=your_censys_api_secret
HAVE_I_BEEN_PWNED_API_KEY=your_hibp_api_key

# Vulnerability Database
CVE_DATABASE_URL=https://cve.circl.lu/api/
NVD_API_KEY=your_nvd_api_key
EXPLOIT_DB_PATH=./exploitdb

# Network Configuration
NETWORK_TIMEOUT=30000
MAX_CONCURRENT_SCANS=5
SCAN_RESULT_TTL=3600

# Metasploit Configuration
MSF_PATH=/usr/share/metasploit-framework
MSF_DATABASE_URL=postgresql://msf:msf@localhost:5432/msf

# Android Testing
ADB_PATH=/usr/bin/adb
ANDROID_SDK_PATH=/opt/android-sdk
FRIDA_SERVER_PATH=./tools/frida-server

# Wireless Testing
AIRCRACK_PATH=/usr/bin/aircrack-ng
REAVER_PATH=/usr/bin/reaver
BETTERCAP_PATH=/usr/bin/bettercap

# Web Application Testing
BURP_PATH=./tools/burpsuite
OWASP_ZAP_PATH=/usr/share/zaproxy
GOBUSTER_PATH=/usr/bin/gobuster
DIRB_PATH=/usr/bin/dirb

# Social Engineering
SET_PATH=/usr/share/set
GOPHISH_PATH=./tools/gophish

# Forensics Tools
VOLATILITY_PATH=/usr/bin/vol.py
AUTOPSY_PATH=./tools/autopsy
SLEUTHKIT_PATH=/usr/bin

# Monitoring and Alerting
PROMETHEUS_URL=http://localhost:9090
GRAFANA_URL=http://localhost:3001
GRAFANA_PASSWORD=CyberSentinel2024!

ELASTICSEARCH_URL=http://localhost:9200
KIBANA_URL=http://localhost:5601

# Notification Services
SLACK_WEBHOOK_URL=your_slack_webhook_url
DISCORD_WEBHOOK_URL=your_discord_webhook_url
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Twilio (SMS Notifications)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Backup Configuration
BACKUP_PATH=./backups
BACKUP_RETENTION_DAYS=7
BACKUP_ENCRYPTION_KEY=CyberSentinel-Backup-Key-2024

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=cybersentinel-backups

GOOGLE_CLOUD_PROJECT_ID=your_gcp_project_id
GOOGLE_CLOUD_KEYFILE=./config/gcp-service-account.json

# SSL/TLS Configuration
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem
SSL_CA_PATH=./ssl/ca.pem

# Development Settings
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000

# Testing Configuration
TEST_DATABASE_URL=postgresql://test:test@localhost:5432/cybersentinel_test
TEST_TIMEOUT=30000

# Performance Settings
CLUSTER_WORKERS=0
MEMORY_LIMIT=1024
CPU_LIMIT=2

# Feature Flags
ENABLE_ANDROID_TESTING=true
ENABLE_WIRELESS_TESTING=true
ENABLE_WEB_APP_TESTING=true
ENABLE_SOCIAL_ENGINEERING=false
ENABLE_FORENSICS=true
ENABLE_REPORTING=true
ENABLE_API=true

# Compliance and Legal
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365
AUDIT_LOG_RETENTION_DAYS=2555

# License and Registration
LICENSE_KEY=your_license_key
REGISTRATION_REQUIRED=true
TRIAL_PERIOD_DAYS=30

# Update and Maintenance
AUTO_UPDATE_CHECK=true
UPDATE_CHANNEL=stable
MAINTENANCE_MODE=false

# Custom Branding
COMPANY_NAME="Your Company Name"
COMPANY_LOGO=./assets/logo.png
CUSTOM_CSS=./assets/custom.css

# Advanced Security
HSTS_MAX_AGE=31536000
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
REFERRER_POLICY=strict-origin-when-cross-origin

# Honeypot Configuration
HONEYPOT_ENABLED=false
HONEYPOT_PORTS=22,23,80,443,3389
HONEYPOT_LOG_PATH=./logs/honeypot.log
