import React, { useState, useEffect, useRef } from 'react';
import { Box, IconButton, Slider, Tooltip, Fade } from '@mui/material';
import { VolumeUp, VolumeOff, VolumeDown, MusicNote } from '@mui/icons-material';

const AudioManager = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.3);
  const [showControls, setShowControls] = useState(false);
  const audioContextRef = useRef(null);
  const oscillatorRef = useRef(null);
  const gainNodeRef = useRef(null);
  const isInitialized = useRef(false);

  // تهيئة Web Audio API
  const initializeAudio = async () => {
    if (isInitialized.current) return;

    try {
      // إنشاء Audio Context
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      
      // إنشاء Gain Node للتحكم في الصوت
      gainNodeRef.current = audioContextRef.current.createGain();
      gainNodeRef.current.connect(audioContextRef.current.destination);
      gainNodeRef.current.gain.value = volume;

      isInitialized.current = true;
      console.log('🎵 تم تهيئة نظام الصوت بنجاح');
    } catch (error) {
      console.error('خطأ في تهيئة نظام الصوت:', error);
    }
  };

  // إنشاء موسيقى سيبرانية باستخدام Web Audio API
  const createCyberMusic = () => {
    if (!audioContextRef.current || !gainNodeRef.current) return;

    // إيقاف الموسيقى الحالية
    if (oscillatorRef.current) {
      oscillatorRef.current.stop();
    }

    // إنشاء oscillator جديد
    const oscillator = audioContextRef.current.createOscillator();
    const filter = audioContextRef.current.createBiquadFilter();
    const delay = audioContextRef.current.createDelay();
    const feedback = audioContextRef.current.createGain();

    // إعداد الفلتر
    filter.type = 'lowpass';
    filter.frequency.value = 800;
    filter.Q.value = 10;

    // إعداد التأخير والصدى
    delay.delayTime.value = 0.3;
    feedback.gain.value = 0.4;

    // ربط العقد
    oscillator.connect(filter);
    filter.connect(delay);
    delay.connect(feedback);
    feedback.connect(delay);
    delay.connect(gainNodeRef.current);
    filter.connect(gainNodeRef.current);

    // إعداد الموجة
    oscillator.type = 'sawtooth';
    
    // إنشاء نمط موسيقي سيبراني
    const notes = [220, 246.94, 261.63, 293.66, 329.63, 369.99, 415.30]; // A3 to G#4
    let noteIndex = 0;
    let time = audioContextRef.current.currentTime;

    const playSequence = () => {
      if (!isPlaying) return;

      // تغيير التردد
      oscillator.frequency.setValueAtTime(notes[noteIndex], time);
      oscillator.frequency.exponentialRampToValueAtTime(notes[noteIndex] * 1.5, time + 0.1);
      oscillator.frequency.exponentialRampToValueAtTime(notes[noteIndex], time + 0.2);

      // تغيير الفلتر
      filter.frequency.setValueAtTime(400, time);
      filter.frequency.exponentialRampToValueAtTime(1200, time + 0.5);

      noteIndex = (noteIndex + 1) % notes.length;
      time += 0.5;

      setTimeout(playSequence, 500);
    };

    oscillator.start();
    oscillatorRef.current = oscillator;
    
    playSequence();
  };

  // تشغيل/إيقاف الموسيقى
  const toggleMusic = async () => {
    if (!isInitialized.current) {
      await initializeAudio();
    }

    if (audioContextRef.current?.state === 'suspended') {
      await audioContextRef.current.resume();
    }

    setIsPlaying(!isPlaying);
  };

  // تغيير مستوى الصوت
  const handleVolumeChange = (event, newValue) => {
    setVolume(newValue);
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = newValue;
    }
  };

  // تأثيرات جانبية
  useEffect(() => {
    if (isPlaying) {
      createCyberMusic();
    } else if (oscillatorRef.current) {
      oscillatorRef.current.stop();
      oscillatorRef.current = null;
    }

    return () => {
      if (oscillatorRef.current) {
        oscillatorRef.current.stop();
      }
    };
  }, [isPlaying]);

  // تنظيف الموارد عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (oscillatorRef.current) {
        oscillatorRef.current.stop();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  // أيقونة الصوت حسب المستوى
  const getVolumeIcon = () => {
    if (volume === 0) return <VolumeOff />;
    if (volume < 0.5) return <VolumeDown />;
    return <VolumeUp />;
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 20,
        right: 20,
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        backgroundColor: 'rgba(26, 26, 46, 0.9)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 255, 65, 0.3)',
        borderRadius: 3,
        padding: 1,
        transition: 'all 0.3s ease'
      }}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* زر تشغيل/إيقاف الموسيقى */}
      <Tooltip title={isPlaying ? 'إيقاف الموسيقى' : 'تشغيل الموسيقى'}>
        <IconButton
          onClick={toggleMusic}
          sx={{
            color: isPlaying ? '#00ff41' : '#cccccc',
            '&:hover': {
              color: '#00ff41',
              backgroundColor: 'rgba(0, 255, 65, 0.1)'
            }
          }}
        >
          <MusicNote />
        </IconButton>
      </Tooltip>

      {/* أيقونة مستوى الصوت */}
      <Fade in={showControls} timeout={300}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="مستوى الصوت">
            <IconButton
              size="small"
              onClick={() => setVolume(volume === 0 ? 0.3 : 0)}
              sx={{
                color: volume === 0 ? '#ff6666' : '#00ccff',
                '&:hover': {
                  backgroundColor: 'rgba(0, 204, 255, 0.1)'
                }
              }}
            >
              {getVolumeIcon()}
            </IconButton>
          </Tooltip>

          {/* شريط التحكم في الصوت */}
          <Box sx={{ width: 80 }}>
            <Slider
              value={volume}
              onChange={handleVolumeChange}
              min={0}
              max={1}
              step={0.1}
              size="small"
              sx={{
                color: '#00ccff',
                '& .MuiSlider-thumb': {
                  backgroundColor: '#00ccff',
                  '&:hover': {
                    boxShadow: '0 0 10px rgba(0, 204, 255, 0.5)'
                  }
                },
                '& .MuiSlider-track': {
                  backgroundColor: '#00ccff'
                },
                '& .MuiSlider-rail': {
                  backgroundColor: 'rgba(255, 255, 255, 0.2)'
                }
              }}
            />
          </Box>

          {/* مؤشر مستوى الصوت */}
          <Box
            sx={{
              minWidth: 30,
              textAlign: 'center',
              fontSize: '0.75rem',
              color: '#cccccc'
            }}
          >
            {Math.round(volume * 100)}%
          </Box>
        </Box>
      </Fade>

      {/* مؤشر التشغيل */}
      {isPlaying && (
        <Box
          sx={{
            position: 'absolute',
            top: -2,
            right: -2,
            width: 8,
            height: 8,
            backgroundColor: '#00ff41',
            borderRadius: '50%',
            animation: 'pulse 2s infinite',
            boxShadow: '0 0 10px #00ff41'
          }}
        />
      )}

      <style jsx>{`
        @keyframes pulse {
          0% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.5;
            transform: scale(1.2);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </Box>
  );
};

export default AudioManager;
