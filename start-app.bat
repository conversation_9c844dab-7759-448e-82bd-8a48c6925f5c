@echo off
title Cyber Sentinel Pro - SecOps Edition
color 0A
chcp 65001 >nul

echo.
echo  ██████╗██╗   ██╗██████╗ ███████╗██████╗     ███████╗███████╗███╗   ██╗████████╗██╗███╗   ██╗███████╗██╗     
echo ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗    ██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║████╗  ██║██╔════╝██║     
echo ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝    ███████╗█████╗  ██╔██╗ ██║   ██║   ██║██╔██╗ ██║█████╗  ██║     
echo ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗    ╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██║╚██╗██║██╔══╝  ██║     
echo ╚██████╗   ██║   ██████╔╝███████╗██║  ██║    ███████║███████╗██║ ╚████║   ██║   ██║██║ ╚████║███████╗███████╗
echo  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝╚══════╝╚══════╝
echo.
echo                                    SecOps Edition v1.0.0
echo                              Advanced Cybersecurity Testing Platform
echo.
echo ===============================================================================
echo.

REM Check if Node.js is installed
echo [INFO] فحص تثبيت Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js غير مثبت!
    echo [INFO] يرجى تثبيت Node.js من: https://nodejs.org/
    echo [INFO] ثم تشغيل هذا الملف مرة أخرى.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo [SUCCESS] Node.js مثبت - الإصدار: %NODE_VERSION%
echo.

REM Check if npm is available
echo [INFO] فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm غير متوفر!
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo [SUCCESS] npm متوفر - الإصدار: %NPM_VERSION%
echo.

REM Check if dependencies are installed
echo [INFO] فحص التبعيات...
if not exist "node_modules" (
    echo [INFO] تثبيت التبعيات للمرة الأولى...
    echo [INFO] قد يستغرق هذا عدة دقائق...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] فشل في تثبيت التبعيات!
        echo [INFO] جرب تشغيل: npm cache clean --force
        echo [INFO] ثم: npm install
        pause
        exit /b 1
    )
    echo [SUCCESS] تم تثبيت التبعيات بنجاح!
    echo.
) else (
    echo [SUCCESS] التبعيات مثبتة مسبقاً!
    echo.
)

REM Create public directory if it doesn't exist
if not exist "public" (
    echo [INFO] إنشاء مجلد public...
    mkdir public
)

REM Create index.html if it doesn't exist
if not exist "public\index.html" (
    echo [INFO] إنشاء ملف index.html...
    echo ^<!DOCTYPE html^> > public\index.html
    echo ^<html lang="ar" dir="rtl"^> >> public\index.html
    echo ^<head^> >> public\index.html
    echo   ^<meta charset="utf-8" /^> >> public\index.html
    echo   ^<link rel="icon" href="%%PUBLIC_URL%%/favicon.ico" /^> >> public\index.html
    echo   ^<meta name="viewport" content="width=device-width, initial-scale=1" /^> >> public\index.html
    echo   ^<meta name="theme-color" content="#000000" /^> >> public\index.html
    echo   ^<meta name="description" content="Cyber Sentinel Pro - Advanced Cybersecurity Testing Platform" /^> >> public\index.html
    echo   ^<title^>Cyber Sentinel Pro - SecOps Edition^</title^> >> public\index.html
    echo ^</head^> >> public\index.html
    echo ^<body^> >> public\index.html
    echo   ^<noscript^>You need to enable JavaScript to run this app.^</noscript^> >> public\index.html
    echo   ^<div id="root"^>^</div^> >> public\index.html
    echo ^</body^> >> public\index.html
    echo ^</html^> >> public\index.html
    echo [SUCCESS] تم إنشاء ملف index.html!
)

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo [INFO] إنشاء ملف .env...
    echo REACT_APP_NAME=Cyber Sentinel Pro > .env
    echo REACT_APP_VERSION=1.0.0 >> .env
    echo REACT_APP_DESCRIPTION=Advanced Cybersecurity Testing Platform >> .env
    echo REACT_APP_API_URL=http://localhost:5000 >> .env
    echo REACT_APP_ENCRYPTION_KEY=CyberSentinel-2024-SecOps-Edition >> .env
    echo GENERATE_SOURCEMAP=false >> .env
    echo [SUCCESS] تم إنشاء ملف .env!
    echo.
)

echo ===============================================================================
echo                              معلومات التطبيق
echo ===============================================================================
echo.
echo 🌐 سيتم فتح التطبيق تلقائياً في المتصفح الافتراضي
echo 📍 الرابط المباشر: http://localhost:3000
echo 🔑 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: JaMaL@123
echo.
echo ⚠️  ملاحظات أمنية مهمة:
echo    - غير كلمة المرور الافتراضية بعد أول تسجيل دخول
echo    - هذا البرنامج مخصص لاختبار الأمان المصرح به فقط
echo    - لا تستخدمه ضد أنظمة لا تملكها أو لا تملك إذناً لاختبارها
echo    - احترم جميع القوانين المحلية والدولية
echo.
echo ===============================================================================
echo.

REM Start the React development server
echo [INFO] بدء تشغيل خادم التطوير...
echo [INFO] يرجى الانتظار حتى يتم تحميل التطبيق...
echo.
echo [INFO] للإيقاف: اضغط Ctrl+C
echo.

REM Open browser after a delay
start "" cmd /c "timeout /t 15 /nobreak >nul && start http://localhost:3000"

REM Start the React app
npm start

REM If we reach here, the server has stopped
echo.
echo [INFO] تم إيقاف Cyber Sentinel Pro.
echo [INFO] شكراً لاستخدام منصة الأمان السيبراني المتقدمة!
echo.
pause
