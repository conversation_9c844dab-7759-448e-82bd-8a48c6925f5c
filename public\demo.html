<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber Sentinel Pro - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Matrix Background Effect */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }

        .matrix-bg canvas {
            display: block;
        }

        /* Header */
        .header {
            text-align: center;
            padding: 2rem;
            background: rgba(26, 26, 46, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 255, 65, 0.3);
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff41, #00ccff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px rgba(0, 255, 65, 0.5);
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 2rem;
        }

        /* Demo Container */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Feature Cards */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
            border-color: rgba(0, 255, 65, 0.6);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #00ff41;
        }

        .feature-description {
            color: #cccccc;
            line-height: 1.6;
        }

        /* Demo Section */
        .demo-section {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .demo-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #00ccff;
        }

        /* Simulated Terminal */
        .terminal {
            background: #000000;
            border: 1px solid #00ff41;
            border-radius: 5px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .terminal-line {
            margin: 0.5rem 0;
            animation: typewriter 2s steps(40) infinite;
        }

        .terminal-prompt {
            color: #00ff41;
        }

        .terminal-output {
            color: #00ccff;
        }

        .terminal-warning {
            color: #ffaa00;
        }

        .terminal-error {
            color: #ff0040;
        }

        /* Network Visualization */
        .network-viz {
            width: 100%;
            height: 300px;
            background: radial-gradient(circle, rgba(0, 255, 65, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 5px;
            position: relative;
            overflow: hidden;
        }

        .network-node {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #00ff41;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .network-connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00ccff, transparent);
            animation: flow 3s infinite;
        }

        /* Animations */
        @keyframes typewriter {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes flow {
            0% { opacity: 0; transform: translateX(-100%); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateX(100%); }
        }

        /* Buttons */
        .btn {
            background: linear-gradient(45deg, #00ff41, #00ccff);
            color: #000000;
            border: none;
            padding: 1rem 2rem;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
        }

        /* Status Indicators */
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .status-item {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 5px;
            padding: 1rem;
            text-align: center;
        }

        .status-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00ff41;
        }

        .status-label {
            color: #cccccc;
            font-size: 0.9rem;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 2rem;
            background: rgba(10, 10, 10, 0.9);
            border-top: 1px solid rgba(0, 255, 65, 0.3);
            margin-top: 3rem;
        }

        .warning-box {
            background: rgba(255, 0, 64, 0.1);
            border: 1px solid rgba(255, 0, 64, 0.3);
            border-radius: 5px;
            padding: 1rem;
            margin: 2rem 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Matrix Background -->
    <div class="matrix-bg">
        <canvas id="matrix-canvas"></canvas>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="logo">🛡️ CYBER SENTINEL PRO</div>
        <div class="subtitle">SecOps Edition - Advanced Cybersecurity Testing Platform</div>
        <div class="subtitle">نسخة تجريبية - Demo Version</div>
    </header>

    <!-- Main Content -->
    <div class="demo-container">
        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card" onclick="showDemo('ai')">
                <span class="feature-icon">🤖</span>
                <div class="feature-title">الذكاء الاصطناعي</div>
                <div class="feature-description">
                    نظام متقدم للكشف عن التهديدات باستخدام الذكاء الاصطناعي والتعلم الآلي
                </div>
            </div>

            <div class="feature-card" onclick="showDemo('3d')">
                <span class="feature-icon">🎮</span>
                <div class="feature-title">التصور ثلاثي الأبعاد</div>
                <div class="feature-description">
                    خريطة شبكة تفاعلية ثلاثية الأبعاد مع تأثيرات بصرية متقدمة
                </div>
            </div>

            <div class="feature-card" onclick="showDemo('honeypot')">
                <span class="feature-icon">🍯</span>
                <div class="feature-title">نظام Honeypot</div>
                <div class="feature-description">
                    فخاخ ذكية لجذب المهاجمين وتحليل تكتيكاتهم
                </div>
            </div>

            <div class="feature-card" onclick="showDemo('monitoring')">
                <span class="feature-icon">📊</span>
                <div class="feature-title">المراقبة المباشرة</div>
                <div class="feature-description">
                    مراقبة الشبكة والنظام في الوقت الفعلي مع تنبيهات ذكية
                </div>
            </div>
        </div>

        <!-- Demo Section -->
        <div class="demo-section">
            <div class="demo-title">🚀 عرض توضيحي مباشر</div>
            
            <!-- Status Grid -->
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="threat-level">منخفض</div>
                    <div class="status-label">مستوى التهديد</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="active-scans">3</div>
                    <div class="status-label">فحوصات نشطة</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="threats-blocked">127</div>
                    <div class="status-label">تهديدات محجوبة</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="network-health">98%</div>
                    <div class="status-label">صحة الشبكة</div>
                </div>
            </div>

            <!-- Network Visualization -->
            <div class="network-viz" id="network-viz">
                <!-- Network nodes and connections will be generated by JavaScript -->
            </div>

            <!-- Simulated Terminal -->
            <div class="terminal" id="terminal">
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">نظام الحماية السيبرانية نشط</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">تم تحميل نموذج الذكاء الاصطناعي</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-warning">تحذير: نشاط مشبوه مكتشف من IP: *************</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">تم حجب محاولة اختراق</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-output">Honeypot SSH: محاولة دخول من ************</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin-top: 2rem;">
                <a href="http://localhost:3000" class="btn" target="_blank">🚀 تشغيل التطبيق الكامل</a>
                <button class="btn" onclick="startDemo()">▶️ بدء العرض التوضيحي</button>
                <button class="btn" onclick="stopDemo()">⏹️ إيقاف العرض</button>
            </div>
        </div>

        <!-- Warning Box -->
        <div class="warning-box">
            <h3>⚠️ تحذير قانوني مهم</h3>
            <p>
                هذا البرنامج مخصص لاختبار الأمان المصرح به والأغراض التعليمية فقط.
                <br>
                الاستخدام غير المصرح به ضد أنظمة لا تملكها غير قانوني.
                <br>
                المطورون غير مسؤولين عن أي استخدام غير قانوني.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <p>© 2024 Cyber Sentinel Pro - SecOps Edition. جميع الحقوق محفوظة.</p>
        <p>Advanced Cybersecurity Testing Platform with AI & 3D Visualization</p>
    </footer>

    <script>
        // Matrix Background Effect
        function initMatrix() {
            const canvas = document.getElementById('matrix-canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
            const matrixArray = matrix.split("");
            
            const fontSize = 10;
            const columns = canvas.width / fontSize;
            const drops = [];
            
            for(let x = 0; x < columns; x++) {
                drops[x] = 1;
            }
            
            function draw() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#00ff41';
                ctx.font = fontSize + 'px monospace';
                
                for(let i = 0; i < drops.length; i++) {
                    const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);
                    
                    if(drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        drops[i] = 0;
                    }
                    drops[i]++;
                }
            }
            
            setInterval(draw, 35);
        }

        // Network Visualization
        function initNetworkViz() {
            const container = document.getElementById('network-viz');
            
            // Create nodes
            for(let i = 0; i < 20; i++) {
                const node = document.createElement('div');
                node.className = 'network-node';
                node.style.left = Math.random() * 90 + '%';
                node.style.top = Math.random() * 90 + '%';
                node.style.animationDelay = Math.random() * 2 + 's';
                container.appendChild(node);
            }
            
            // Create connections
            for(let i = 0; i < 10; i++) {
                const connection = document.createElement('div');
                connection.className = 'network-connection';
                connection.style.left = Math.random() * 80 + '%';
                connection.style.top = Math.random() * 90 + '%';
                connection.style.width = Math.random() * 200 + 50 + 'px';
                connection.style.animationDelay = Math.random() * 3 + 's';
                container.appendChild(connection);
            }
        }

        // Demo Functions
        let demoInterval;
        
        function startDemo() {
            const terminal = document.getElementById('terminal');
            const messages = [
                { type: 'output', text: 'بدء فحص الشبكة...' },
                { type: 'output', text: 'تم اكتشاف 15 جهاز في الشبكة' },
                { type: 'warning', text: 'تحذير: محاولة فحص منافذ من IP خارجي' },
                { type: 'output', text: 'تم تفعيل Honeypot SSH' },
                { type: 'error', text: 'تم حجب هجوم DDoS' },
                { type: 'output', text: 'الذكاء الاصطناعي: تم تحليل 1,247 حزمة' },
                { type: 'warning', text: 'نشاط مشبوه: محاولة SQL Injection' },
                { type: 'output', text: 'تم تحديث قواعد الحماية' }
            ];
            
            let messageIndex = 0;
            
            demoInterval = setInterval(() => {
                const message = messages[messageIndex % messages.length];
                const line = document.createElement('div');
                line.className = 'terminal-line';
                line.innerHTML = `
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span> 
                    <span class="terminal-${message.type}">${message.text}</span>
                `;
                
                terminal.appendChild(line);
                terminal.scrollTop = terminal.scrollHeight;
                
                // Keep only last 10 lines
                if(terminal.children.length > 10) {
                    terminal.removeChild(terminal.firstChild);
                }
                
                messageIndex++;
                
                // Update status randomly
                updateStatus();
            }, 2000);
        }
        
        function stopDemo() {
            if(demoInterval) {
                clearInterval(demoInterval);
            }
        }
        
        function updateStatus() {
            const threatLevels = ['منخفض', 'متوسط', 'عالي'];
            const threatColors = ['#00ff41', '#ffaa00', '#ff0040'];
            
            const threatLevel = threatLevels[Math.floor(Math.random() * threatLevels.length)];
            const threatElement = document.getElementById('threat-level');
            threatElement.textContent = threatLevel;
            threatElement.style.color = threatColors[threatLevels.indexOf(threatLevel)];
            
            document.getElementById('active-scans').textContent = Math.floor(Math.random() * 10) + 1;
            document.getElementById('threats-blocked').textContent = Math.floor(Math.random() * 500) + 100;
            document.getElementById('network-health').textContent = (95 + Math.random() * 5).toFixed(1) + '%';
        }
        
        function showDemo(type) {
            alert(`عرض توضيحي لـ ${type} - سيتم تطوير هذه الميزة في النسخة الكاملة`);
        }
        
        // Initialize everything
        window.onload = function() {
            initMatrix();
            initNetworkViz();
            
            // Auto-start demo after 3 seconds
            setTimeout(startDemo, 3000);
        };
        
        // Handle window resize
        window.onresize = function() {
            const canvas = document.getElementById('matrix-canvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };
    </script>
</body>
</html>
