@echo off
title ⚡ تحويل سريع إلى EXE - Cyber Sentinel Pro
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  ⚡  تحويل سريع إلى EXE - CYBER SENTINEL PRO  ⚡                           █
echo █                                                                              █
echo █     🚀 تحويل فوري وسريع                                                    █
echo █     ⚡ بدون تعقيدات                                                         █
echo █     ✅ نتيجة مضمونة                                                         █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] ⚡ مرحباً بك في المحول السريع!
echo.

echo 🎯 اختر طريقة التحويل:
echo.
echo 1️⃣ تحويل احترافي (Electron) - ملف EXE حقيقي
echo 2️⃣ تحويل آمن (Electron + تشخيص) - محسن ومحمي
echo 3️⃣ تحويل مبسط (Portable) - حزمة محمولة
echo 4️⃣ تشخيص Node.js - فحص المشاكل
echo 5️⃣ عرض دليل التحويل
echo 6️⃣ خروج
echo.

set /p choice="أدخل اختيارك (1-6): "

if "%choice%"=="1" goto professional
if "%choice%"=="2" goto safe
if "%choice%"=="3" goto simple
if "%choice%"=="4" goto diagnose
if "%choice%"=="5" goto guide
if "%choice%"=="6" goto exit
goto invalid

:professional
echo.
echo ===============================================================================
echo                              🔧 التحويل الاحترافي
echo ===============================================================================
echo.
echo [INFO] بدء التحويل الاحترافي باستخدام Electron...
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] ⚠️ Node.js غير مثبت!
    echo.
    echo [ACTION] سيتم فتح صفحة تحميل Node.js...
    start https://nodejs.org
    echo.
    echo [INFO] بعد تثبيت Node.js:
    echo    1. أعد تشغيل هذا الملف
    echo    2. اختر الخيار 1 مرة أخرى
    echo.
    pause
    goto menu
)

echo [✅] Node.js متوفر
echo [INFO] تشغيل المحول الاحترافي...
echo.

call "🔧-BUILD-EXE.bat"
goto end

:safe
echo.
echo ===============================================================================
echo                              🔧 التحويل الآمن
echo ===============================================================================
echo.
echo [INFO] بدء التحويل الآمن مع تشخيص محسن...
echo.

call "🔧-SAFE-BUILD-EXE.bat"
goto end

:simple
echo.
echo ===============================================================================
echo                              📦 التحويل المبسط
echo ===============================================================================
echo.
echo [INFO] بدء التحويل المبسط (حزمة محمولة)...
echo.

call "🔧-SIMPLE-EXE-CONVERTER.bat"
goto end

:diagnose
echo.
echo ===============================================================================
echo                              🔍 تشخيص Node.js
echo ===============================================================================
echo.
echo [INFO] تشغيل أداة تشخيص Node.js...
echo.

call "🔍-DIAGNOSE-NODEJS.bat"
echo.
pause
goto menu

:guide
echo.
echo ===============================================================================
echo                              📖 دليل التحويل
echo ===============================================================================
echo.
echo [INFO] فتح دليل التحويل الشامل...
echo.

if exist "📖-EXE-CONVERSION-GUIDE.txt" (
    start notepad "📖-EXE-CONVERSION-GUIDE.txt"
    echo [✅] تم فتح الدليل في Notepad
) else (
    echo [ERROR] ❌ لم يتم العثور على دليل التحويل
)

echo.
pause
goto menu

:invalid
echo.
echo [ERROR] ❌ اختيار غير صحيح!
echo [INFO] يرجى اختيار رقم من 1 إلى 6
echo.
pause

:menu
cls
echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  ⚡  تحويل سريع إلى EXE - CYBER SENTINEL PRO  ⚡                           █
echo █                                                                              █
echo █     🚀 تحويل فوري وسريع                                                    █
echo █     ⚡ بدون تعقيدات                                                         █
echo █     ✅ نتيجة مضمونة                                                         █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo 🎯 اختر طريقة التحويل:
echo.
echo 1️⃣ تحويل احترافي (Electron) - ملف EXE حقيقي
echo 2️⃣ تحويل آمن (Electron + تشخيص) - محسن ومحمي
echo 3️⃣ تحويل مبسط (Portable) - حزمة محمولة
echo 4️⃣ تشخيص Node.js - فحص المشاكل
echo 5️⃣ عرض دليل التحويل
echo 6️⃣ خروج
echo.

set /p choice="أدخل اختيارك (1-6): "

if "%choice%"=="1" goto professional
if "%choice%"=="2" goto safe
if "%choice%"=="3" goto simple
if "%choice%"=="4" goto diagnose
if "%choice%"=="5" goto guide
if "%choice%"=="6" goto exit
goto invalid

:end
echo.
echo ===============================================================================
echo                              ✅ اكتمل التحويل!
echo ===============================================================================
echo.
echo 🎉 تم تحويل Cyber Sentinel Pro بنجاح!
echo.
echo 📁 تحقق من الملفات المُنشأة:
echo    📂 مجلد dist (للتحويل الاحترافي)
echo    📂 مجلد CyberSentinelPro_Portable (للتحويل المبسط)
echo.
echo 🚀 للتشغيل:
echo    💻 انقر نقراً مزدوجاً على ملف EXE
echo    🔑 استخدم بيانات المدير: admin / JaMaL@123
echo.
echo 💡 نصائح:
echo    • اختبر التطبيق قبل التوزيع
echo    • احتفظ بنسخة احتياطية
echo    • شارك بأمان مع الآخرين
echo.
pause
goto exit

:exit
echo.
echo [INFO] شكراً لاستخدام محول Cyber Sentinel Pro!
echo [INFO] للدعم: <EMAIL>
echo.
exit /b 0
