@echo off
title 🔍 تشخيص مشاكل Node.js - Cyber Sentinel Pro
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🔍  تشخيص مشاكل NODE.JS - CYBER SENTINEL PRO  🔍                         █
echo █                                                                              █
echo █     🔧 فحص شامل لـ Node.js و npm                                           █
echo █     🛠️ تشخيص المشاكل الشائعة                                              █
echo █     💡 حلول مقترحة                                                         █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔍 بدء تشخيص Node.js...
echo.

echo ===============================================================================
echo                              🔍 فحص النظام
echo ===============================================================================
echo.

echo [STEP 1] 🖥️ معلومات النظام:
echo    نظام التشغيل: %OS%
echo    معمارية النظام: %PROCESSOR_ARCHITECTURE%
echo    اسم الكمبيوتر: %COMPUTERNAME%
echo    اسم المستخدم: %USERNAME%
echo.

echo [STEP 2] 📁 فحص متغيرات البيئة:
echo    PATH الحالي:
echo    %PATH%
echo.

echo [STEP 3] 🔍 فحص Node.js:
echo [INFO] جاري فحص وجود Node.js...

REM Check if node command exists
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo [✅] تم العثور على Node.js
    echo [INFO] مسار Node.js:
    where node
    echo.
    echo [INFO] إصدار Node.js:
    node --version 2>&1
    echo.
    echo [INFO] معلومات Node.js التفصيلية:
    node -p "process.version + ' - ' + process.platform + ' - ' + process.arch" 2>&1
    echo.
) else (
    echo [❌] لم يتم العثور على Node.js
    echo [INFO] Node.js غير مثبت أو غير موجود في PATH
    echo.
)

echo [STEP 4] 📦 فحص npm:
echo [INFO] جاري فحص وجود npm...

REM Check if npm command exists
where npm >nul 2>&1
if %errorlevel% equ 0 (
    echo [✅] تم العثور على npm
    echo [INFO] مسار npm:
    where npm
    echo.
    echo [INFO] إصدار npm:
    npm --version 2>&1
    echo.
    echo [INFO] إعدادات npm:
    npm config get registry 2>&1
    npm config get cache 2>&1
    echo.
) else (
    echo [❌] لم يتم العثور على npm
    echo [INFO] npm غير مثبت أو غير موجود في PATH
    echo.
)

echo [STEP 5] 📂 فحص مجلد المشروع:
echo [INFO] المجلد الحالي: %CD%
echo.
echo [INFO] فحص الملفات المطلوبة:

if exist "package.json" (
    echo [✅] package.json موجود
) else (
    echo [❌] package.json مفقود
)

if exist "main.js" (
    echo [✅] main.js موجود
) else (
    echo [❌] main.js مفقود
)

if exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [✅] ملف التطبيق الرئيسي موجود
) else (
    echo [❌] ملف التطبيق الرئيسي مفقود
)

if exist "node_modules" (
    echo [✅] مجلد node_modules موجود
) else (
    echo [⚠️] مجلد node_modules غير موجود (سيتم إنشاؤه عند التثبيت)
)

echo.

echo ===============================================================================
echo                              🔧 التشخيص والحلول
echo ===============================================================================
echo.

REM Diagnose common issues
echo [DIAGNOSIS] 🔍 تشخيص المشاكل الشائعة:
echo.

REM Check if Node.js is installed
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [ISSUE] ❌ Node.js غير مثبت
    echo [SOLUTION] 💡 الحلول المقترحة:
    echo    1. حمل Node.js من: https://nodejs.org
    echo    2. اختر النسخة LTS (الموصى بها)
    echo    3. ثبت Node.js مع الإعدادات الافتراضية
    echo    4. أعد تشغيل Command Prompt
    echo    5. جرب الأمر: node --version
    echo.
    goto solutions
)

REM Check if npm is available
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [ISSUE] ❌ npm غير متاح
    echo [SOLUTION] 💡 الحلول المقترحة:
    echo    1. أعد تثبيت Node.js (npm يأتي معه)
    echo    2. تحقق من متغير PATH
    echo    3. شغل Command Prompt كمدير
    echo    4. جرب الأمر: npm --version
    echo.
    goto solutions
)

REM Check npm connectivity
echo [INFO] اختبار اتصال npm...
npm ping >nul 2>&1
if %errorlevel% neq 0 (
    echo [ISSUE] ⚠️ مشكلة في اتصال npm
    echo [SOLUTION] 💡 الحلول المقترحة:
    echo    1. تحقق من اتصال الإنترنت
    echo    2. تحقق من إعدادات Firewall
    echo    3. جرب: npm config set registry https://registry.npmjs.org/
    echo    4. امسح cache: npm cache clean --force
    echo.
) else (
    echo [✅] اتصال npm يعمل بشكل صحيح
)

REM Check project files
if not exist "package.json" (
    echo [ISSUE] ❌ ملف package.json مفقود
    echo [SOLUTION] 💡 تأكد من وجود جميع ملفات المشروع
    echo.
)

if not exist "main.js" (
    echo [ISSUE] ❌ ملف main.js مفقود
    echo [SOLUTION] 💡 تأكد من وجود جميع ملفات المشروع
    echo.
)

echo [✅] التشخيص مكتمل!

:solutions
echo.
echo ===============================================================================
echo                              💡 الحلول المقترحة
echo ===============================================================================
echo.

echo 🔧 حلول المشاكل الشائعة:
echo.

echo 1️⃣ مشكلة: Node.js غير مثبت
echo    💡 الحل:
echo       • اذهب إلى: https://nodejs.org
echo       • حمل النسخة LTS
echo       • ثبت مع الإعدادات الافتراضية
echo       • أعد تشغيل Command Prompt
echo.

echo 2️⃣ مشكلة: npm لا يعمل
echo    💡 الحل:
echo       • أعد تثبيت Node.js
echo       • شغل Command Prompt كمدير
echo       • جرب: npm cache clean --force
echo       • تحقق من متغير PATH
echo.

echo 3️⃣ مشكلة: فشل في تثبيت الحزم
echo    💡 الحل:
echo       • تحقق من اتصال الإنترنت
echo       • أغلق برامج الحماية مؤقتاً
echo       • جرب: npm install --verbose
echo       • امسح node_modules وأعد المحاولة
echo.

echo 4️⃣ مشكلة: فشل في البناء
echo    💡 الحل:
echo       • تأكد من وجود جميع الملفات
echo       • تحقق من مساحة القرص
echo       • جرب: npm run build-win --verbose
echo       • تحقق من سجل الأخطاء
echo.

echo 5️⃣ مشكلة: مشاكل الصلاحيات
echo    💡 الحل:
echo       • شغل Command Prompt كمدير
echo       • تحقق من صلاحيات المجلد
echo       • جرب تغيير مجلد العمل
echo       • تحقق من إعدادات UAC
echo.

echo ===============================================================================
echo                              🚀 خطوات التشغيل الصحيحة
echo ===============================================================================
echo.

echo 📋 للتشغيل الناجح:
echo.
echo 1. تأكد من تثبيت Node.js بشكل صحيح
echo 2. افتح Command Prompt كمدير
echo 3. انتقل إلى مجلد المشروع
echo 4. شغل: 🔧-BUILD-EXE.bat
echo 5. انتظر اكتمال العملية
echo 6. ابحث عن النتيجة في مجلد dist
echo.

echo ===============================================================================
echo                              📞 الدعم الفني
echo ===============================================================================
echo.

echo 🆘 إذا استمرت المشاكل:
echo.
echo 📧 البريد: <EMAIL>
echo 🌐 الموقع: https://cybersentinel.pro
echo 📱 التليجرام: @CyberSentinelSupport
echo 📖 الدليل: 📖-EXE-CONVERSION-GUIDE.txt
echo.

echo 📋 عند طلب الدعم، أرفق:
echo    • لقطة شاشة من رسائل الخطأ
echo    • نتيجة هذا التشخيص
echo    • إصدار نظام التشغيل
echo    • إصدار Node.js و npm
echo.

echo ===============================================================================
echo.

echo [INFO] انتهى التشخيص
echo [INFO] اضغط أي مفتاح للخروج...
pause >nul
