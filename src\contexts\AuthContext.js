import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authService } from '../services/authService';
import { securityService } from '../services/securityService';

// إنشاء السياق
const AuthContext = createContext();

// الحالات الأولية
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  sessionInfo: null,
  permissions: [],
  lastActivity: null,
  securityLevel: 'medium'
};

// أنواع الإجراءات
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  UPDATE_USER: 'UPDATE_USER',
  UPDATE_ACTIVITY: 'UPDATE_ACTIVITY',
  SET_SECURITY_LEVEL: 'SET_SECURITY_LEVEL',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// مخفض الحالة
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        sessionInfo: action.payload.sessionInfo,
        permissions: action.payload.permissions || [],
        lastActivity: new Date(),
        securityLevel: action.payload.securityLevel || 'medium'
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload.error,
        sessionInfo: null,
        permissions: []
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        isLoading: false
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };

    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      };

    case AUTH_ACTIONS.UPDATE_ACTIVITY:
      return {
        ...state,
        lastActivity: new Date()
      };

    case AUTH_ACTIONS.SET_SECURITY_LEVEL:
      return {
        ...state,
        securityLevel: action.payload
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    default:
      return state;
  }
};

// مزود السياق
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // تحقق من الجلسة عند بدء التطبيق
  useEffect(() => {
    const checkSession = async () => {
      try {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

        const sessionCheck = await authService.checkSession();
        
        if (sessionCheck.valid) {
          dispatch({
            type: AUTH_ACTIONS.LOGIN_SUCCESS,
            payload: {
              user: sessionCheck.user,
              sessionInfo: {
                expiresAt: sessionCheck.expiresAt,
                isValid: true
              },
              permissions: sessionCheck.user.permissions || [],
              securityLevel: sessionCheck.user.securityLevel || 'medium'
            }
          });
        } else {
          dispatch({ type: AUTH_ACTIONS.LOGOUT });
        }
      } catch (error) {
        console.error('خطأ في فحص الجلسة:', error);
        dispatch({ type: AUTH_ACTIONS.LOGOUT });
      } finally {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    checkSession();
  }, []);

  // مراقبة النشاط للحفاظ على الجلسة
  useEffect(() => {
    if (state.isAuthenticated) {
      const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
      
      const updateActivity = () => {
        dispatch({ type: AUTH_ACTIONS.UPDATE_ACTIVITY });
      };

      activityEvents.forEach(event => {
        document.addEventListener(event, updateActivity, true);
      });

      return () => {
        activityEvents.forEach(event => {
          document.removeEventListener(event, updateActivity, true);
        });
      };
    }
  }, [state.isAuthenticated]);

  // فحص انتهاء الجلسة
  useEffect(() => {
    if (state.isAuthenticated && state.lastActivity) {
      const checkSessionTimeout = setInterval(() => {
        const now = new Date();
        const timeSinceActivity = now - state.lastActivity;
        const maxInactivity = 30 * 60 * 1000; // 30 دقيقة

        if (timeSinceActivity > maxInactivity) {
          logout();
          securityService.logSecurityEvent({
            type: 'session_timeout',
            level: 'medium',
            message: 'انتهت الجلسة بسبب عدم النشاط'
          });
        }
      }, 60000); // فحص كل دقيقة

      return () => clearInterval(checkSessionTimeout);
    }
  }, [state.isAuthenticated, state.lastActivity]);

  // دوال المصادقة
  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const result = await authService.login(credentials);

      if (result.success) {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user: result.user,
            sessionInfo: {
              sessionId: result.sessionId,
              expiresAt: result.expiresAt,
              isValid: true
            },
            permissions: result.user.permissions || [],
            securityLevel: result.user.securityLevel || 'medium'
          }
        });

        // تسجيل حدث تسجيل الدخول
        securityService.logSecurityEvent({
          type: 'login_success',
          level: 'low',
          message: `تم تسجيل دخول المستخدم: ${result.user.username}`
        });

        return { success: true };
      } else {
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: { error: result.error }
        });

        // تسجيل محاولة تسجيل دخول فاشلة
        securityService.logSecurityEvent({
          type: 'login_failure',
          level: 'medium',
          message: `محاولة تسجيل دخول فاشلة: ${credentials.username}`
        });

        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'خطأ في الاتصال بالخادم';
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: errorMessage }
      });
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      const token = localStorage.getItem('cybersentinel_token');
      const sessionId = localStorage.getItem('cybersentinel_session');

      if (token && sessionId) {
        await authService.logout(token, sessionId);
      }

      // تسجيل حدث تسجيل الخروج
      securityService.logSecurityEvent({
        type: 'logout',
        level: 'low',
        message: `تم تسجيل خروج المستخدم: ${state.user?.username || 'غير معروف'}`
      });

      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  const register = async (userData) => {
    try {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      const result = await authService.register(userData);

      if (result.success) {
        // تسجيل حدث التسجيل
        securityService.logSecurityEvent({
          type: 'registration',
          level: 'low',
          message: `تم تسجيل مستخدم جديد: ${userData.username}`
        });
      }

      return result;
    } catch (error) {
      const errorMessage = 'خطأ في الاتصال بالخادم';
      dispatch({
        type: AUTH_ACTIONS.SET_ERROR,
        payload: errorMessage
      });
      return { success: false, error: errorMessage };
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
    }
  };

  const updateProfile = async (profileData) => {
    try {
      const result = await authService.updateProfile(profileData);

      if (result.success) {
        dispatch({
          type: AUTH_ACTIONS.UPDATE_USER,
          payload: result.user
        });

        securityService.logSecurityEvent({
          type: 'profile_update',
          level: 'low',
          message: 'تم تحديث الملف الشخصي'
        });
      }

      return result;
    } catch (error) {
      return { success: false, error: 'خطأ في تحديث الملف الشخصي' };
    }
  };

  const changePassword = async (currentPassword, newPassword) => {
    try {
      const result = await authService.changePassword(currentPassword, newPassword);

      if (result.success) {
        securityService.logSecurityEvent({
          type: 'password_change',
          level: 'medium',
          message: 'تم تغيير كلمة المرور'
        });
      }

      return result;
    } catch (error) {
      return { success: false, error: 'خطأ في تغيير كلمة المرور' };
    }
  };

  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  const hasPermission = (permission) => {
    return state.permissions.includes(permission) || state.user?.isAdmin;
  };

  const isAdmin = () => {
    return state.user?.isAdmin || false;
  };

  // قيم السياق
  const contextValue = {
    // الحالة
    ...state,
    
    // الدوال
    login,
    logout,
    register,
    updateProfile,
    changePassword,
    clearError,
    hasPermission,
    isAdmin,
    
    // معلومات إضافية
    sessionTimeRemaining: state.sessionInfo?.expiresAt 
      ? Math.max(0, new Date(state.sessionInfo.expiresAt) - new Date())
      : 0
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useAuth = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth يجب أن يُستخدم داخل AuthProvider');
  }
  
  return context;
};

export default AuthContext;
