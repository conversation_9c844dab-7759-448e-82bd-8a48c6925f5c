@echo off
setlocal enabledelayedexpansion
title Cyber Sentinel Pro EXE Builder
color 0A

REM Prevent auto-close with immediate pause
echo Cyber Sentinel Pro - EXE Builder
echo.
echo Press any key to continue...
pause >nul

cls
echo.
echo ========================================
echo    Cyber Sentinel Pro EXE Builder
echo ========================================
echo.

echo [1/7] Starting build process...
echo.

echo [2/7] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo Download the LTS version and install it
    echo Then restart Command Prompt and try again
    echo.
    echo Opening download page...
    start https://nodejs.org
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo OK: Node.js found
node --version

echo.
echo [3/7] Checking npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: npm not found
    echo Please reinstall Node.js
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo OK: npm found
npm --version

echo.
echo [4/7] Checking project files...
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure you are in the correct folder
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo OK: package.json found

if not exist "main.js" (
    echo ERROR: main.js not found
    echo Make sure you are in the correct folder
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo OK: main.js found

echo.
echo [5/7] Installing dependencies...
echo This may take several minutes...
echo.
npm install electron electron-builder electron-updater --save-dev
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo.
    echo Try these solutions:
    echo 1. Run Command Prompt as Administrator
    echo 2. Check internet connection
    echo 3. Disable antivirus temporarily
    echo 4. Try: npm cache clean --force
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)
echo OK: Dependencies installed

echo.
echo [6/7] Building EXE file...
echo This may take 5-10 minutes...
echo.
npm run build-win
if errorlevel 1 (
    echo WARNING: npm script failed, trying alternative...
    npx electron-builder --win
    if errorlevel 1 (
        echo ERROR: Build failed
        echo.
        echo Try these solutions:
        echo 1. Check available disk space (need 1GB+)
        echo 2. Close other applications
        echo 3. Disable antivirus temporarily
        echo.
        echo Press any key to exit...
        pause >nul
        exit /b 1
    )
)
echo OK: Build completed

echo.
echo [7/7] Checking results...
if exist "dist" (
    echo OK: dist folder created
    echo.
    echo Opening dist folder...
    start dist
    echo.
    echo ========================================
    echo         BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Your EXE file is ready in the dist folder
    echo.
    echo To run the application:
    echo 1. Go to dist folder
    echo 2. Double-click the EXE file
    echo 3. Login with: admin / JaMaL@123
    echo.
) else (
    echo WARNING: dist folder not found
    echo Build may have completed but files are elsewhere
    echo.
)

echo Press any key to exit...
pause >nul
exit /b 0
