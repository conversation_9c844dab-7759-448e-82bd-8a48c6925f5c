{"name": "cyber-sentinel-pro-quick", "version": "1.0.0", "description": "Quick start version of Cyber Sentinel Pro", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "demo": "npx serve public -l 3000", "quick": "npm install && npm start"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "@mui/material": "^5.14.20", "@mui/icons-material": "^5.14.19", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "react-router-dom": "^6.20.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}