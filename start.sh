#!/bin/bash

# Cyber Sentinel Pro - SecOps Edition
# Quick Start Script for Unix/Linux/macOS

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ASCII Art Banner
print_banner() {
    echo -e "${GREEN}"
    echo "  ██████╗██╗   ██╗██████╗ ███████╗██████╗     ███████╗███████╗███╗   ██╗████████╗██╗███╗   ██╗███████╗██╗     "
    echo " ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗    ██╔════╝██╔════╝████╗  ██║╚══██╔══╝██║████╗  ██║██╔════╝██║     "
    echo " ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝    ███████╗█████╗  ██╔██╗ ██║   ██║   ██║██╔██╗ ██║█████╗  ██║     "
    echo " ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗    ╚════██║██╔══╝  ██║╚██╗██║   ██║   ██║██║╚██╗██║██╔══╝  ██║     "
    echo " ╚██████╗   ██║   ██████╔╝███████╗██║  ██║    ███████║███████╗██║ ╚████║   ██║   ██║██║ ╚████║███████╗███████╗"
    echo "  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝╚══════╝╚══════╝"
    echo ""
    echo "                                    SecOps Edition v1.0.0"
    echo "                              Advanced Cybersecurity Testing Platform"
    echo -e "${NC}"
    echo "==============================================================================="
    echo ""
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        log_success "Node.js is installed: $NODE_VERSION"
    else
        log_error "Node.js is not installed"
        log_info "Please install Node.js from: https://nodejs.org/"
        log_info "Recommended version: 18.x or higher"
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        log_success "npm is installed: $NPM_VERSION"
    else
        log_error "npm is not installed"
        log_info "npm should be installed with Node.js"
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        log_success "Python is installed: $PYTHON_VERSION"
    elif command_exists python; then
        PYTHON_VERSION=$(python --version)
        log_success "Python is installed: $PYTHON_VERSION"
    else
        log_error "Python is not installed"
        log_info "Please install Python from: https://python.org/"
        log_info "Recommended version: 3.8 or higher"
        exit 1
    fi
    
    # Check pip
    if command_exists pip3; then
        PIP_VERSION=$(pip3 --version)
        log_success "pip is installed: $PIP_VERSION"
    elif command_exists pip; then
        PIP_VERSION=$(pip --version)
        log_success "pip is installed: $PIP_VERSION"
    else
        log_warning "pip is not installed"
        log_info "Installing pip..."
        if command_exists python3; then
            python3 -m ensurepip --upgrade
        else
            python -m ensurepip --upgrade
        fi
    fi
    
    echo ""
}

# Setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        log_info "Creating .env file from template..."
        cp ".env.example" ".env"
        log_success ".env file created. Please review and update the configuration."
    fi
    
    # Create necessary directories
    log_info "Creating application directories..."
    mkdir -p uploads reports logs temp backups ssl tools
    log_success "Directory structure created!"
    
    echo ""
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    # Install Node.js dependencies
    log_info "Installing Node.js dependencies..."
    log_info "This may take a few minutes on first run..."
    npm install
    if [ $? -eq 0 ]; then
        log_success "Node.js dependencies installed successfully!"
    else
        log_error "Failed to install Node.js dependencies"
        exit 1
    fi
    
    # Install Python dependencies
    log_info "Installing Python dependencies..."
    if command_exists pip3; then
        pip3 install -r requirements.txt
    else
        pip install -r requirements.txt
    fi
    
    if [ $? -eq 0 ]; then
        log_success "Python dependencies installed successfully!"
    else
        log_warning "Some Python dependencies may have failed to install"
        log_info "You can install them manually later if needed"
    fi
    
    echo ""
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    node scripts/setup-database.js
    if [ $? -eq 0 ]; then
        log_success "Database setup completed!"
    else
        log_warning "Database setup failed. You can run it manually later."
    fi
    echo ""
}

# Show menu
show_menu() {
    echo "==============================================================================="
    echo "                              STARTUP OPTIONS"
    echo "==============================================================================="
    echo ""
    echo "1. Start Development Server (Frontend + Backend)"
    echo "2. Start Production Server"
    echo "3. Start Electron Desktop App"
    echo "4. Start Backend Only"
    echo "5. Start Frontend Only"
    echo "6. Run Tests"
    echo "7. Build for Production"
    echo "8. Setup Database"
    echo "9. Docker Compose (All Services)"
    echo "10. Exit"
    echo ""
}

# Handle menu choice
handle_choice() {
    case $1 in
        1)
            log_info "Starting development server..."
            log_info "Frontend will be available at: http://localhost:3000"
            log_info "Backend will be available at: http://localhost:5000"
            log_info "Press Ctrl+C to stop the server"
            echo ""
            npm run dev
            ;;
        2)
            log_info "Building and starting production server..."
            npm run build && npm run start:prod
            ;;
        3)
            log_info "Starting Electron desktop application..."
            npm run electron
            ;;
        4)
            log_info "Starting backend server only..."
            log_info "Server will be available at: http://localhost:5000"
            npm run server
            ;;
        5)
            log_info "Starting frontend only..."
            log_info "Frontend will be available at: http://localhost:3000"
            npm start
            ;;
        6)
            log_info "Running tests..."
            npm test
            show_menu
            ;;
        7)
            log_info "Building for production..."
            npm run build
            log_success "Build completed! Files are in the 'build' directory."
            show_menu
            ;;
        8)
            setup_database
            show_menu
            ;;
        9)
            log_info "Starting all services with Docker Compose..."
            if command_exists docker-compose; then
                docker-compose up -d
                log_success "All services started! Check docker-compose logs for details."
            elif command_exists docker; then
                docker compose up -d
                log_success "All services started! Check docker compose logs for details."
            else
                log_error "Docker is not installed"
                log_info "Please install Docker from: https://docker.com/"
            fi
            show_menu
            ;;
        10)
            log_info "Goodbye!"
            exit 0
            ;;
        *)
            log_error "Invalid choice. Please enter a number between 1-10."
            show_menu
            ;;
    esac
}

# Show important information
show_info() {
    echo ""
    echo "==============================================================================="
    echo "                              IMPORTANT INFORMATION"
    echo "==============================================================================="
    echo ""
    echo -e "${YELLOW}Default Admin Credentials:${NC}"
    echo "   Username: admin"
    echo "   Password: JaMaL@123"
    echo ""
    echo -e "${YELLOW}Security Notes:${NC}"
    echo "   - Change the default password after first login"
    echo "   - Setup two-factor authentication"
    echo "   - Review the .env file for security settings"
    echo "   - This tool is for authorized security testing only"
    echo ""
    echo -e "${YELLOW}Documentation:${NC}"
    echo "   - README.md - Complete setup and usage guide"
    echo "   - /docs - Additional documentation"
    echo "   - GitHub: https://github.com/cybersentinel/cyber-sentinel-pro"
    echo ""
    echo -e "${YELLOW}Support:${NC}"
    echo "   - Email: <EMAIL>"
    echo "   - Telegram: @CyberSentinelSupport"
    echo ""
    echo "==============================================================================="
    echo -e "${RED}                                  WARNING${NC}"
    echo "==============================================================================="
    echo ""
    echo "This software is intended for authorized security testing and educational"
    echo "purposes only. Unauthorized use against systems you do not own or have"
    echo "explicit permission to test is illegal and unethical."
    echo ""
    echo "The developers are not responsible for any misuse of this software."
    echo "Use responsibly and in accordance with all applicable laws."
    echo ""
    echo "==============================================================================="
    echo ""
}

# Main function
main() {
    # Make sure we're in the right directory
    cd "$(dirname "$0")"
    
    # Print banner
    print_banner
    
    # Check prerequisites
    check_prerequisites
    
    # Setup environment
    setup_environment
    
    # Install dependencies
    install_dependencies
    
    # Ask about database setup
    echo -n "Do you want to setup the database now? (y/n): "
    read -r setup_db
    if [[ $setup_db =~ ^[Yy]$ ]]; then
        setup_database
    fi
    
    # Show menu and handle choices
    while true; do
        show_menu
        echo -n "Enter your choice (1-10): "
        read -r choice
        handle_choice "$choice"
        
        if [ "$choice" != "6" ] && [ "$choice" != "7" ] && [ "$choice" != "8" ] && [ "$choice" != "9" ]; then
            break
        fi
    done
    
    # Show important information
    show_info
}

# Make script executable
chmod +x "$0"

# Run main function
main "$@"
