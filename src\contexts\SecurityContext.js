import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { securityService, THREAT_LEVELS, THREAT_TYPES } from '../services/securityService';

// إنشاء السياق
const SecurityContext = createContext();

// الحالات الأولية
const initialState = {
  securityStatus: 'checking',
  threatLevel: THREAT_LEVELS.LOW,
  activeThreats: [],
  securityLog: [],
  metrics: {
    totalThreats: 0,
    blockedAttacks: 0,
    suspiciousActivities: 0,
    lastSecurityCheck: null
  },
  alerts: [],
  isMonitoring: false,
  systemIntegrity: 'unknown'
};

// أنواع الإجراءات
const SECURITY_ACTIONS = {
  SET_SECURITY_STATUS: 'SET_SECURITY_STATUS',
  SET_THREAT_LEVEL: 'SET_THREAT_LEVEL',
  ADD_THREAT: 'ADD_THREAT',
  REMOVE_THREAT: 'REMOVE_THREAT',
  UPDATE_METRICS: 'UPDATE_METRICS',
  ADD_ALERT: 'ADD_ALERT',
  REMOVE_ALERT: 'REMOVE_ALERT',
  SET_MONITORING: 'SET_MONITORING',
  UPDATE_SECURITY_LOG: 'UPDATE_SECURITY_LOG',
  SET_SYSTEM_INTEGRITY: 'SET_SYSTEM_INTEGRITY',
  CLEAR_ALERTS: 'CLEAR_ALERTS'
};

// مخفض الحالة
const securityReducer = (state, action) => {
  switch (action.type) {
    case SECURITY_ACTIONS.SET_SECURITY_STATUS:
      return {
        ...state,
        securityStatus: action.payload
      };

    case SECURITY_ACTIONS.SET_THREAT_LEVEL:
      return {
        ...state,
        threatLevel: action.payload
      };

    case SECURITY_ACTIONS.ADD_THREAT:
      return {
        ...state,
        activeThreats: [...state.activeThreats, action.payload],
        threatLevel: calculateThreatLevel([...state.activeThreats, action.payload])
      };

    case SECURITY_ACTIONS.REMOVE_THREAT:
      const filteredThreats = state.activeThreats.filter(threat => threat.id !== action.payload);
      return {
        ...state,
        activeThreats: filteredThreats,
        threatLevel: calculateThreatLevel(filteredThreats)
      };

    case SECURITY_ACTIONS.UPDATE_METRICS:
      return {
        ...state,
        metrics: { ...state.metrics, ...action.payload }
      };

    case SECURITY_ACTIONS.ADD_ALERT:
      return {
        ...state,
        alerts: [action.payload, ...state.alerts].slice(0, 50) // الاحتفاظ بآخر 50 تنبيه
      };

    case SECURITY_ACTIONS.REMOVE_ALERT:
      return {
        ...state,
        alerts: state.alerts.filter(alert => alert.id !== action.payload)
      };

    case SECURITY_ACTIONS.SET_MONITORING:
      return {
        ...state,
        isMonitoring: action.payload
      };

    case SECURITY_ACTIONS.UPDATE_SECURITY_LOG:
      return {
        ...state,
        securityLog: action.payload
      };

    case SECURITY_ACTIONS.SET_SYSTEM_INTEGRITY:
      return {
        ...state,
        systemIntegrity: action.payload
      };

    case SECURITY_ACTIONS.CLEAR_ALERTS:
      return {
        ...state,
        alerts: []
      };

    default:
      return state;
  }
};

// حساب مستوى التهديد الإجمالي
const calculateThreatLevel = (threats) => {
  if (threats.length === 0) return THREAT_LEVELS.LOW;
  
  const hasCritical = threats.some(threat => threat.level === THREAT_LEVELS.CRITICAL);
  const hasHigh = threats.some(threat => threat.level === THREAT_LEVELS.HIGH);
  const hasMedium = threats.some(threat => threat.level === THREAT_LEVELS.MEDIUM);
  
  if (hasCritical) return THREAT_LEVELS.CRITICAL;
  if (hasHigh) return THREAT_LEVELS.HIGH;
  if (hasMedium) return THREAT_LEVELS.MEDIUM;
  
  return THREAT_LEVELS.LOW;
};

// مزود السياق
export const SecurityProvider = ({ children }) => {
  const [state, dispatch] = useReducer(securityReducer, initialState);

  // تهيئة نظام المراقبة الأمنية
  useEffect(() => {
    const initializeSecurity = async () => {
      try {
        dispatch({ type: SECURITY_ACTIONS.SET_MONITORING, payload: true });
        
        // إجراء فحص أمني أولي
        const securityCheck = await securityService.performSecurityCheck();
        
        dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_STATUS, payload: securityCheck.status });
        dispatch({ type: SECURITY_ACTIONS.UPDATE_METRICS, payload: securityCheck.metrics });
        dispatch({ type: SECURITY_ACTIONS.SET_SYSTEM_INTEGRITY, payload: securityCheck.checks.integrity.status });
        
        // إضافة تنبيه إذا كان هناك مشاكل أمنية
        if (securityCheck.status !== 'secure') {
          addAlert({
            type: 'security_warning',
            level: THREAT_LEVELS.MEDIUM,
            message: 'تم اكتشاف مشاكل أمنية محتملة',
            timestamp: new Date()
          });
        }
        
        console.log('🛡️ تم تهيئة نظام الأمان بنجاح');
      } catch (error) {
        console.error('خطأ في تهيئة نظام الأمان:', error);
        dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_STATUS, payload: 'error' });
      }
    };

    initializeSecurity();
  }, []);

  // مراقبة دورية للأمان
  useEffect(() => {
    if (state.isMonitoring) {
      const monitoringInterval = setInterval(async () => {
        try {
          // تحديث سجل الأمان
          const securityLog = securityService.getSecurityLog(100);
          dispatch({ type: SECURITY_ACTIONS.UPDATE_SECURITY_LOG, payload: securityLog });
          
          // تحديث المقاييس
          const metrics = securityService.getSecurityMetrics();
          dispatch({ type: SECURITY_ACTIONS.UPDATE_METRICS, payload: metrics });
          
          // فحص أمني دوري (كل 5 دقائق)
          if (Date.now() % (5 * 60 * 1000) < 30000) {
            const securityCheck = await securityService.performSecurityCheck();
            dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_STATUS, payload: securityCheck.status });
          }
        } catch (error) {
          console.error('خطأ في المراقبة الأمنية:', error);
        }
      }, 30000); // كل 30 ثانية

      return () => clearInterval(monitoringInterval);
    }
  }, [state.isMonitoring]);

  // مراقبة أحداث الأمان الجديدة
  useEffect(() => {
    const handleSecurityEvent = (event) => {
      // إضافة التهديد إلى القائمة النشطة
      if (event.level === THREAT_LEVELS.HIGH || event.level === THREAT_LEVELS.CRITICAL) {
        addThreat({
          id: event.id || Date.now().toString(),
          type: event.type,
          level: event.level,
          message: event.message,
          timestamp: event.timestamp || new Date(),
          source: event.source || 'system'
        });
      }
      
      // إضافة تنبيه
      addAlert({
        id: Date.now().toString(),
        type: event.type,
        level: event.level,
        message: event.message,
        timestamp: event.timestamp || new Date()
      });
    };

    // الاستماع لأحداث الأمان (يمكن تحسينها باستخدام EventEmitter)
    window.addEventListener('security-event', handleSecurityEvent);
    
    return () => {
      window.removeEventListener('security-event', handleSecurityEvent);
    };
  }, []);

  // دوال الأمان
  const addThreat = (threat) => {
    dispatch({ type: SECURITY_ACTIONS.ADD_THREAT, payload: threat });
  };

  const removeThreat = (threatId) => {
    dispatch({ type: SECURITY_ACTIONS.REMOVE_THREAT, payload: threatId });
  };

  const addAlert = (alert) => {
    const alertWithId = {
      ...alert,
      id: alert.id || Date.now().toString(),
      timestamp: alert.timestamp || new Date()
    };
    
    dispatch({ type: SECURITY_ACTIONS.ADD_ALERT, payload: alertWithId });
    
    // إزالة التنبيه تلقائياً بعد 30 ثانية للتنبيهات منخفضة الأهمية
    if (alert.level === THREAT_LEVELS.LOW) {
      setTimeout(() => {
        removeAlert(alertWithId.id);
      }, 30000);
    }
  };

  const removeAlert = (alertId) => {
    dispatch({ type: SECURITY_ACTIONS.REMOVE_ALERT, payload: alertId });
  };

  const clearAllAlerts = () => {
    dispatch({ type: SECURITY_ACTIONS.CLEAR_ALERTS });
  };

  const performSecurityScan = async () => {
    try {
      dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_STATUS, payload: 'scanning' });
      
      const securityCheck = await securityService.performSecurityCheck();
      
      dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_STATUS, payload: securityCheck.status });
      dispatch({ type: SECURITY_ACTIONS.UPDATE_METRICS, payload: securityCheck.metrics });
      dispatch({ type: SECURITY_ACTIONS.SET_SYSTEM_INTEGRITY, payload: securityCheck.checks.integrity.status });
      
      addAlert({
        type: 'security_scan',
        level: THREAT_LEVELS.LOW,
        message: `تم إجراء فحص أمني - النتيجة: ${securityCheck.status}`,
        timestamp: new Date()
      });
      
      return securityCheck;
    } catch (error) {
      dispatch({ type: SECURITY_ACTIONS.SET_SECURITY_STATUS, payload: 'error' });
      throw error;
    }
  };

  const getSecurityReport = () => {
    return {
      status: state.securityStatus,
      threatLevel: state.threatLevel,
      activeThreats: state.activeThreats,
      metrics: state.metrics,
      systemIntegrity: state.systemIntegrity,
      recentAlerts: state.alerts.slice(0, 10),
      timestamp: new Date()
    };
  };

  const isSecure = () => {
    return state.securityStatus === 'secure' && 
           state.threatLevel === THREAT_LEVELS.LOW && 
           state.activeThreats.length === 0;
  };

  const getThreatLevelColor = () => {
    switch (state.threatLevel) {
      case THREAT_LEVELS.LOW:
        return '#00ff41';
      case THREAT_LEVELS.MEDIUM:
        return '#ffaa00';
      case THREAT_LEVELS.HIGH:
        return '#ff6600';
      case THREAT_LEVELS.CRITICAL:
        return '#ff0040';
      default:
        return '#cccccc';
    }
  };

  const getThreatLevelText = () => {
    switch (state.threatLevel) {
      case THREAT_LEVELS.LOW:
        return 'منخفض';
      case THREAT_LEVELS.MEDIUM:
        return 'متوسط';
      case THREAT_LEVELS.HIGH:
        return 'عالي';
      case THREAT_LEVELS.CRITICAL:
        return 'حرج';
      default:
        return 'غير معروف';
    }
  };

  // قيم السياق
  const contextValue = {
    // الحالة
    ...state,
    
    // الدوال
    addThreat,
    removeThreat,
    addAlert,
    removeAlert,
    clearAllAlerts,
    performSecurityScan,
    getSecurityReport,
    isSecure,
    getThreatLevelColor,
    getThreatLevelText,
    
    // الثوابت
    THREAT_LEVELS,
    THREAT_TYPES
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  
  if (!context) {
    throw new Error('useSecurity يجب أن يُستخدم داخل SecurityProvider');
  }
  
  return context;
};

export default SecurityContext;
