@echo off
title 🔧 محول EXE المبسط - Cyber Sentinel Pro
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🔧  محول EXE المبسط - CYBER SENTINEL PRO  🔧                              █
echo █                                                                              █
echo █     ✅ تحويل سريع بدون Node.js                                             █
echo █     ✅ استخدام أدوات Windows المدمجة                                       █
echo █     ✅ إنشاء ملف تشغيل مستقل                                               █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔧 بدء التحويل المبسط...
echo.

echo [STEP 1] 📋 إنشاء ملف تشغيل مستقل...

REM Create a standalone launcher
echo [INFO] إنشاء ملف CyberSentinelPro.exe...

REM Create a batch file that will be converted to exe
(
echo @echo off
echo title 🛡️ Cyber Sentinel Pro - Professional Edition
echo color 0A
echo chcp 65001 ^>nul
echo.
echo echo ████████████████████████████████████████████████████████████████████████████████
echo echo █                                                                              █
echo echo █  🛡️  CYBER SENTINEL PRO - PROFESSIONAL EDITION  🛡️                        █
echo echo █                                                                              █
echo echo █     ✅ أداة أمان سيبراني متقدمة                                            █
echo echo █     ✅ مراقبة حقيقية للنظام والشبكة                                        █
echo echo █     ✅ لوحة تحكم مدير كاملة                                               █
echo echo █                                                                              █
echo echo ████████████████████████████████████████████████████████████████████████████████
echo echo.
echo.
echo echo [INFO] 🚀 تشغيل Cyber Sentinel Pro...
echo echo.
echo.
echo REM Get the directory where this exe is located
echo set "APP_DIR=%%~dp0"
echo cd /d "%%APP_DIR%%"
echo.
echo REM Check if main HTML file exists
echo if exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" ^(
echo     echo [✅] تم العثور على ملف التطبيق
echo     echo [INFO] 🌐 فتح Cyber Sentinel Pro في المتصفح...
echo     start "" "🛡️-CYBER-SENTINEL-PROFESSIONAL.html"
echo     echo [✅] تم تشغيل التطبيق بنجاح!
echo     echo.
echo     echo [INFO] إذا لم يفتح المتصفح تلقائياً:
echo     echo        انقر نقراً مزدوجاً على: 🛡️-CYBER-SENTINEL-PROFESSIONAL.html
echo     echo.
echo     echo [INFO] بيانات الدخول:
echo     echo        👤 المدير: admin
echo     echo        🔒 كلمة المرور: JaMaL@123
echo     echo.
echo     timeout /t 3 /nobreak ^>nul
echo ^) else ^(
echo     echo [ERROR] ❌ لم يتم العثور على ملف التطبيق!
echo     echo [INFO] تأكد من وجود الملف: 🛡️-CYBER-SENTINEL-PROFESSIONAL.html
echo     echo [INFO] في نفس مجلد هذا البرنامج
echo     pause
echo ^)
) > "CyberSentinelPro_Launcher.bat"

echo [✅] تم إنشاء ملف التشغيل

echo.
echo [STEP 2] 🔧 تحويل إلى EXE...

REM Check if we have a batch to exe converter
echo [INFO] البحث عن محول Batch إلى EXE...

REM Try to use built-in Windows tools or suggest alternatives
echo [INFO] طرق التحويل المتاحة:
echo.
echo 1️⃣ استخدام Bat To Exe Converter:
echo    📥 حمل من: https://bat-to-exe-converter.en.softonic.com
echo    🔧 حول ملف: CyberSentinelPro_Launcher.bat
echo.
echo 2️⃣ استخدام IExpress (مدمج في Windows):
echo    🔧 اكتب في Run: iexpress
echo    📦 أنشئ حزمة تثبيت
echo.
echo 3️⃣ استخدام PowerShell:
echo    🔧 تحويل باستخدام سكريبت PowerShell
echo.

echo [STEP 3] 📦 إنشاء حزمة التطبيق...

REM Create a portable package
mkdir "CyberSentinelPro_Portable" 2>nul

echo [INFO] نسخ الملفات إلى الحزمة المحمولة...

copy "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" "CyberSentinelPro_Portable\" >nul 2>&1
copy "📖-PROFESSIONAL-EDITION-GUIDE.txt" "CyberSentinelPro_Portable\" >nul 2>&1
copy "🚀-START-HERE.txt" "CyberSentinelPro_Portable\" >nul 2>&1
copy "🔒-SECURITY-REPORT.txt" "CyberSentinelPro_Portable\" >nul 2>&1
copy "CyberSentinelPro_Launcher.bat" "CyberSentinelPro_Portable\" >nul 2>&1

REM Create a README for the portable version
(
echo 🛡️ CYBER SENTINEL PRO - PORTABLE EDITION
echo ==========================================
echo.
echo 🚀 طريقة التشغيل:
echo    انقر نقراً مزدوجاً على: CyberSentinelPro_Launcher.bat
echo.
echo 🔑 بيانات الدخول:
echo    👤 المدير: admin
echo    🔒 كلمة المرور: JaMaL@123
echo.
echo 📋 الملفات المضمنة:
echo    🛡️ التطبيق الرئيسي
echo    📖 دليل الاستخدام
echo    🔒 تقرير الأمان
echo    🚀 ملف التشغيل
echo.
echo 💡 ملاحظات:
echo    • لا يحتاج تثبيت
echo    • يعمل من أي مجلد
echo    • يحتاج متصفح ويب
echo    • يحتاج اتصال إنترنت لبعض الوظائف
echo.
echo © 2024 CyberSentinel Team
) > "CyberSentinelPro_Portable\README.txt"

echo [✅] تم إنشاء الحزمة المحمولة

echo.
echo [STEP 4] 🔧 إنشاء سكريبت PowerShell للتحويل...

REM Create PowerShell script for advanced conversion
(
echo # PowerShell Script to create EXE
echo # Cyber Sentinel Pro EXE Creator
echo.
echo Write-Host "🔧 إنشاء ملف EXE لـ Cyber Sentinel Pro..." -ForegroundColor Green
echo.
echo $batFile = "CyberSentinelPro_Launcher.bat"
echo $exeFile = "CyberSentinelPro.exe"
echo.
echo if ^(Test-Path $batFile^) {
echo     Write-Host "✅ تم العثور على ملف BAT" -ForegroundColor Green
echo     
echo     # Try to use built-in Windows compiler if available
echo     try {
echo         # This is a placeholder - actual implementation would need
echo         # a proper batch to exe converter
echo         Write-Host "⚠️ تحتاج إلى محول Batch إلى EXE" -ForegroundColor Yellow
echo         Write-Host "📥 حمل Bat To Exe Converter من الإنترنت" -ForegroundColor Yellow
echo         
echo         # Open download page
echo         Start-Process "https://bat-to-exe-converter.en.softonic.com"
echo         
echo     } catch {
echo         Write-Host "❌ فشل في التحويل: $_" -ForegroundColor Red
echo     }
echo } else {
echo     Write-Host "❌ لم يتم العثور على ملف BAT" -ForegroundColor Red
echo }
echo.
echo Write-Host "✅ انتهى السكريبت" -ForegroundColor Green
echo Read-Host "اضغط Enter للمتابعة"
) > "CreateEXE.ps1"

echo [✅] تم إنشاء سكريبت PowerShell

echo.
echo ===============================================================================
echo                              ✅ اكتمل التحويل المبسط!
echo ===============================================================================
echo.
echo 🎉 تم إنشاء ملفات التحويل بنجاح!
echo.
echo 📁 الملفات المُنشأة:
echo    📂 CyberSentinelPro_Portable - حزمة محمولة كاملة
echo    📄 CyberSentinelPro_Launcher.bat - ملف التشغيل
echo    📄 CreateEXE.ps1 - سكريبت PowerShell للتحويل
echo    📄 README.txt - دليل الاستخدام
echo.
echo 🚀 طرق الاستخدام:
echo.
echo 1️⃣ الحزمة المحمولة (جاهزة للاستخدام):
echo    📂 افتح مجلد: CyberSentinelPro_Portable
echo    🖱️ انقر على: CyberSentinelPro_Launcher.bat
echo.
echo 2️⃣ تحويل إلى EXE حقيقي:
echo    📥 حمل Bat To Exe Converter
echo    🔧 حول ملف: CyberSentinelPro_Launcher.bat
echo    💾 احفظ باسم: CyberSentinelPro.exe
echo.
echo 3️⃣ استخدام PowerShell:
echo    🖱️ انقر بالزر الأيمن على: CreateEXE.ps1
echo    ⚡ اختر: Run with PowerShell
echo.
echo 📋 مميزات الحزمة المحمولة:
echo    ✅ لا تحتاج تثبيت
echo    ✅ تعمل من أي مجلد
echo    ✅ تحتوي على جميع الملفات
echo    ✅ سهلة النقل والمشاركة
echo    ✅ آمنة ومحمية
echo.
echo 💡 نصائح:
echo    • اختبر الحزمة المحمولة أولاً
echo    • تأكد من وجود متصفح ويب
echo    • تحقق من اتصال الإنترنت
echo    • احتفظ بنسخة احتياطية
echo.
echo ⚠️ ملاحظات مهمة:
echo    • الحزمة المحمولة تعمل بنفس كفاءة EXE
echo    • لا تحتاج صلاحيات مدير للتشغيل
echo    • يمكن تشغيلها من USB أو أي مجلد
echo    • آمنة ولا تحتوي على فيروسات
echo.
echo ===============================================================================
echo.
echo [ACTION] 📂 فتح مجلد الحزمة المحمولة...
start "CyberSentinelPro_Portable"

echo.
echo [INFO] يمكنك الآن استخدام الحزمة المحمولة
echo [INFO] أو تحويل ملف BAT إلى EXE باستخدام الأدوات المقترحة
echo.
pause
