// اختبار سريع للتطبيق
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 فحص شامل لـ Cyber Sentinel Pro...\n');

// فحص الملفات الأساسية
const requiredFiles = [
  'package.json',
  'src/App.js',
  'src/index.js',
  'src/index.css',
  'public/index.html',
  '.env'
];

console.log('📁 فحص الملفات الأساسية:');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - مفقود!`);
  }
});

// فحص المكونات
const requiredComponents = [
  'src/components/Common/LoadingScreen.js',
  'src/components/Auth/LoginPage.js',
  'src/components/Auth/RegisterPage.js',
  'src/components/Dashboard/Dashboard.js',
  'src/components/Advanced/CyberCommandCenter.js',
  'src/components/Visualization/Network3D.js',
  'src/components/Monitoring/RealTimeMonitor.js'
];

console.log('\n🧩 فحص المكونات:');
requiredComponents.forEach(component => {
  if (fs.existsSync(component)) {
    console.log(`✅ ${component}`);
  } else {
    console.log(`❌ ${component} - مفقود!`);
  }
});

// فحص السياقات
const requiredContexts = [
  'src/contexts/AuthContext.js',
  'src/contexts/SecurityContext.js'
];

console.log('\n🔗 فحص السياقات:');
requiredContexts.forEach(context => {
  if (fs.existsSync(context)) {
    console.log(`✅ ${context}`);
  } else {
    console.log(`❌ ${context} - مفقود!`);
  }
});

// فحص الخدمات
const requiredServices = [
  'src/services/aiThreatDetection.js',
  'src/services/honeypotService.js',
  'src/services/advancedAudioService.js',
  'src/services/authService.js',
  'src/services/securityService.js'
];

console.log('\n⚙️ فحص الخدمات:');
requiredServices.forEach(service => {
  if (fs.existsSync(service)) {
    console.log(`✅ ${service}`);
  } else {
    console.log(`❌ ${service} - مفقود!`);
  }
});

// فحص package.json
console.log('\n📦 فحص package.json:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // فحص السكريبتات
  if (packageJson.scripts && packageJson.scripts.start) {
    console.log('✅ سكريبت start موجود');
  } else {
    console.log('❌ سكريبت start مفقود');
  }
  
  // فحص التبعيات الأساسية
  const requiredDeps = ['react', 'react-dom', 'react-scripts', '@mui/material'];
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}`);
    } else {
      console.log(`❌ ${dep} - مفقود من التبعيات`);
    }
  });
  
} catch (error) {
  console.log('❌ خطأ في قراءة package.json:', error.message);
}

// فحص node_modules
console.log('\n📚 فحص node_modules:');
if (fs.existsSync('node_modules')) {
  console.log('✅ node_modules موجود');
  
  // فحص بعض التبعيات المهمة
  const importantModules = ['react', 'react-dom', '@mui/material'];
  importantModules.forEach(module => {
    if (fs.existsSync(`node_modules/${module}`)) {
      console.log(`✅ ${module} مثبت`);
    } else {
      console.log(`❌ ${module} غير مثبت`);
    }
  });
} else {
  console.log('❌ node_modules غير موجود - يجب تشغيل npm install');
}

console.log('\n🎯 ملخص الفحص:');
console.log('================');

// إحصائيات
let totalFiles = requiredFiles.length + requiredComponents.length + requiredContexts.length + requiredServices.length;
let existingFiles = 0;

[...requiredFiles, ...requiredComponents, ...requiredContexts, ...requiredServices].forEach(file => {
  if (fs.existsSync(file)) {
    existingFiles++;
  }
});

const completionPercentage = Math.round((existingFiles / totalFiles) * 100);

console.log(`📊 اكتمال الملفات: ${existingFiles}/${totalFiles} (${completionPercentage}%)`);

if (completionPercentage === 100) {
  console.log('🎉 جميع الملفات موجودة! التطبيق جاهز للتشغيل');
} else {
  console.log('⚠️ بعض الملفات مفقودة - قد تحتاج لإنشائها');
}

console.log('\n🚀 لتشغيل التطبيق:');
console.log('1. npm install');
console.log('2. npm start');
console.log('3. افتح http://localhost:3000');
console.log('\n🔑 بيانات الدخول:');
console.log('👤 اسم المستخدم: admin');
console.log('🔒 كلمة المرور: JaMaL@123');
