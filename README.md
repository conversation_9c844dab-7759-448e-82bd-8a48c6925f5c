# 🛡️ Cyber Sentinel Pro - SecOps Edition

منصة اختبار الأمان السيبراني المتقدمة مع واجهة حديثة وأدوات احترافية.

## 🚀 الميزات الرئيسية

### 🔍 فحص شامل للثغرات
- تحليل المنافذ المفتوحة (Port Scanning)
- فحص الشبكات والأجهزة المرتبطة
- تحليل البروتوكولات (HTTP/FTP/SSH/etc.)
- التعرف على الثغرات المعروفة (CVE Scanner)

### 🛠️ أدوات اختبار قوية
- Nmap GUI + Command integration
- Metasploit Module Manager
- اختبار كلمات المرور (Brute-force/Hydra)
- تحليل واختبار الشبكات اللاسلكية (WPA/WPA2)

### 🧠 تحليل ذكي
- نظام تحليل تلقائي للثغرات (Auto Scan + AI Detection)
- توصيات لحلول الحماية
- تقارير PDF وHTML احترافية قابلة للطباعة والتسليم

### 🌐 واجهة ويب داخلية للإدارة
- إدارة عمليات الاختبار
- حفظ الجلسات والتقارير
- تسجيل الدخول بخطوتين للمحققين فقط

### 🔐 إضافات سيبرانية احترافية
- تتبع الأجهزة المتصلة بالشبكة (ARP Scan + MAC Lookup)
- التقاط وتحليل الحزم (Packet Sniffing via Wireshark API)
- اختبار تطبيقات الويب (SQLi, XSS, LFI, RFI, CSRF)
- استغلال بيئات الأندرويد للتدريب (Android Exploit Lab – بإذن)

## 📱 الأنظمة المدعومة

- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Linux (Ubuntu, Debian, CentOS)
- ✅ Android 8.0+

## 🔧 متطلبات التثبيت

### Windows
1. تحميل وتثبيت Node.js من: https://nodejs.org/
2. تحميل وتثبيت Git من: https://git-scm.com/
3. تحميل وتثبيت Python 3.8+ من: https://python.org/
4. تثبيت Nmap من: https://nmap.org/download.html

### macOS
```bash
# تثبيت Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# تثبيت المتطلبات
brew install node python nmap git
```

### Linux (Ubuntu/Debian)
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت المتطلبات
sudo apt install nodejs npm python3 python3-pip nmap git -y

# تثبيت yarn (اختياري)
npm install -g yarn
```

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/cybersentinel/cyber-sentinel-pro.git
cd cyber-sentinel-pro
```

### 2. تثبيت التبعيات
```bash
# تثبيت تبعيات Node.js
npm install

# أو باستخدام yarn
yarn install

# تثبيت تبعيات Python
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
npm run setup-db

# أو يدوياً
node scripts/setup-database.js
```

### 4. تشغيل التطبيق

#### وضع التطوير
```bash
# تشغيل الخادم والواجهة معاً
npm run dev

# أو تشغيلهما منفصلين
npm run server    # الخادم الخلفي
npm start         # الواجهة الأمامية
```

#### وضع الإنتاج
```bash
# بناء التطبيق
npm run build

# تشغيل التطبيق
npm run start:prod
```

#### تطبيق Electron
```bash
# تشغيل تطبيق سطح المكتب
npm run electron

# بناء ملفات التثبيت
npm run build:windows    # Windows
npm run build:mac        # macOS
npm run build:linux      # Linux
```

## 🔐 إعداد الأمان

### 1. إنشاء حساب الأدمن
- اسم المستخدم: `admin`
- كلمة المرور: `JaMaL@123`
- يتم إنشاء الحساب تلقائياً عند أول تشغيل

### 2. إعداد التحقق بخطوتين
1. تسجيل الدخول بحساب الأدمن
2. الذهاب إلى الإعدادات > الأمان
3. مسح QR Code باستخدام Google Authenticator أو Authy
4. إدخال الرمز للتأكيد

### 3. إعداد شهادات SSL (للإنتاج)
```bash
# إنشاء شهادة ذاتية التوقيع
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# نسخ الشهادات
cp cert.pem server/ssl/
cp key.pem server/ssl/
```

## 🎯 الاستخدام

### 1. تسجيل الدخول
- افتح المتصفح على: `http://localhost:3000`
- أدخل بيانات تسجيل الدخول
- أدخل رمز التحقق بخطوتين

### 2. فحص الشبكة
1. اذهب إلى "أدوات الفحص" > "فحص الشبكة"
2. أدخل عنوان IP أو نطاق الشبكة
3. اختر نوع الفحص (سريع/شامل/مخصص)
4. انقر "بدء الفحص"

### 3. اختبار الثغرات
1. اذهب إلى "اختبار الثغرات"
2. اختر الهدف والأدوات
3. تكوين معاملات الاختبار
4. مراجعة النتائج والتوصيات

### 4. إنشاء التقارير
1. اذهب إلى "التقارير"
2. اختر نوع التقرير (PDF/HTML)
3. تخصيص المحتوى والتصميم
4. تحميل أو إرسال التقرير

## 🔧 التكوين المتقدم

### متغيرات البيئة
إنشاء ملف `.env` في المجلد الجذر:

```env
# إعدادات الخادم
PORT=5000
NODE_ENV=production

# قاعدة البيانات
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cybersentinel
DB_USER=admin
DB_PASS=your_secure_password

# الأمان
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key
SESSION_SECRET=your_session_secret

# البريد الإلكتروني
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# APIs خارجية
VIRUSTOTAL_API_KEY=your_virustotal_key
SHODAN_API_KEY=your_shodan_key
```

### إعدادات Nmap
```json
{
  "nmap": {
    "path": "/usr/bin/nmap",
    "timeout": 300,
    "max_targets": 1000,
    "default_options": ["-sS", "-O", "-sV", "--script=default"]
  }
}
```

## 📊 مراقبة الأداء

### مقاييس النظام
- استخدام المعالج والذاكرة
- عدد الفحوصات النشطة
- إحصائيات الشبكة
- سجل الأحداث الأمنية

### التنبيهات
- تنبيهات الأمان في الوقت الفعلي
- إشعارات اكتمال الفحص
- تحديثات قواعد البيانات
- تقارير الأداء اليومية

## 🛡️ الأمان والخصوصية

### حماية البيانات
- تشفير AES-256 لجميع البيانات الحساسة
- تشفير قواعد البيانات
- حماية من هجمات CSRF و XSS
- مراقبة أمنية 24/7

### الامتثال القانوني
- استخدام مصرح به فقط
- توثيق جميع العمليات
- احترام قوانين الخصوصية
- إخلاء مسؤولية واضح

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## ⚠️ إخلاء المسؤولية

**تحذير مهم**: هذا البرنامج مخصص لأغراض تعليمية واختبار الأمان المصرح به فقط. 

- لا يُستخدم إلا بإذن صريح من مالك النظام
- المطورون غير مسؤولين عن أي استخدام غير قانوني
- يجب الامتثال لجميع القوانين المحلية والدولية
- استخدم هذه الأدوات بمسؤولية

## 📞 الدعم والتواصل

- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع الرسمي: https://cybersentinel.pro
- 📱 تليجرام: @CyberSentinelSupport
- 🐛 الإبلاغ عن الأخطاء: [GitHub Issues](https://github.com/cybersentinel/cyber-sentinel-pro/issues)

## 🙏 شكر خاص

- فريق Nmap لأدوات الفحص الممتازة
- مجتمع Metasploit للأدوات الأمنية
- مطوري React و Node.js
- جميع المساهمين في المشروع

---

**© 2024 CyberSentinel Team. جميع الحقوق محفوظة.**
