# 🚀 دليل التشغيل السريع - Cyber Sentinel Pro

## 🌟 الميزات الاستثنائية الجديدة

### 🤖 **نظام الذكاء الاصطناعي للكشف عن التهديدات**
- تحليل سلوك الشبكة بالذكاء الاصطناعي
- كشف الهجمات المتقدمة (APT) 
- تعلم مستمر من البيانات الجديدة
- دقة عالية في التنبؤ بالتهديدات

### 🎮 **واجهة ثلاثية الأبعاد تفاعلية**
- خريطة شبكة ثلاثية الأبعاد مع WebGL
- تصور البيانات في الوقت الفعلي
- تأثيرات بصرية متقدمة
- تفاعل مباشر مع عقد الشبكة

### 🍯 **نظام Honeypot ذكي**
- فخاخ متعددة (SSH, HTTP, FTP, Database)
- تحليل تكتيكات المهاجمين
- تسجيل تفصيلي للهجمات
- تنبيهات فورية للتهديدات

### 🔊 **نظام صوتي متقدم**
- موسيقى ديناميكية تتغير حسب التهديدات
- تأثيرات صوتية تفاعلية
- أصوات تنبيه ذكية
- تجربة صوتية غامرة

### 📊 **مراقبة الشبكة المباشرة**
- رسوم بيانية حية
- تحليل حركة البيانات
- إحصائيات النظام المباشرة
- تنبيهات في الوقت الفعلي

## ⚡ التشغيل السريع

### 1. متطلبات النظام
```bash
# Windows
- Node.js 18+ 
- Python 3.8+
- Git
- 4GB RAM (8GB مفضل)
- كرت رسوميات يدعم WebGL

# Linux/macOS
- Node.js 18+
- Python 3.8+
- Git
- 4GB RAM (8GB مفضل)
- OpenGL support
```

### 2. التثبيت السريع

#### Windows:
```cmd
# تشغيل start.bat
start.bat
```

#### Linux/macOS:
```bash
# تشغيل start.sh
chmod +x start.sh
./start.sh
```

#### يدوياً:
```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd cyber-sentinel-pro

# 2. تثبيت التبعيات
npm install
pip install -r requirements.txt

# 3. إعداد قاعدة البيانات
node scripts/setup-database.js

# 4. تشغيل التطبيق
npm run dev
```

### 3. الوصول للتطبيق
- **الواجهة الرئيسية**: http://localhost:3000
- **API الخلفي**: http://localhost:5000
- **تطبيق Electron**: `npm run electron`

### 4. بيانات الدخول الافتراضية
```
اسم المستخدم: admin
كلمة المرور: JaMaL@123
```

## 🎯 استخدام الميزات المتقدمة

### 🤖 تفعيل الذكاء الاصطناعي
1. اذهب إلى مركز القيادة السيبراني
2. فعل مفتاح "الذكاء الاصطناعي"
3. سيبدأ النظام بتحليل حركة الشبكة تلقائياً
4. ستظهر التنبيهات عند اكتشاف تهديدات

### 🍯 تشغيل Honeypot
1. في مركز القيادة، فعل مفتاح "Honeypot"
2. سيتم تفعيل فخاخ SSH, HTTP, FTP
3. راقب لوحة Honeypot لرؤية الهجمات
4. تحليل تفصيلي لكل محاولة اختراق

### 🎮 استخدام التصور ثلاثي الأبعاد
1. انتقل إلى تبويب "التصور ثلاثي الأبعاد"
2. استخدم الماوس للتنقل في الخريطة
3. انقر على العقد لرؤية التفاصيل
4. فعل/ألغ عرض التهديدات

### 📊 مراقبة الشبكة المباشرة
1. تبويب "المراقبة المباشرة"
2. رسوم بيانية حية للشبكة والنظام
3. تنبيهات فورية للأنشطة المشبوهة
4. إحصائيات تفصيلية

## 🔧 إعدادات متقدمة

### تخصيص الذكاء الاصطناعي
```javascript
// في ملف .env
AI_THRESHOLD=0.7
AI_LEARNING_RATE=0.001
AI_MODEL_UPDATE_INTERVAL=3600000
```

### إعدادات Honeypot
```javascript
// في ملف .env
HONEYPOT_ENABLED=true
HONEYPOT_PORTS=22,23,80,443,3389
HONEYPOT_LOG_PATH=./logs/honeypot.log
```

### إعدادات الصوت
```javascript
// في ملف .env
AUDIO_ENABLED=true
AUDIO_VOLUME=0.3
AUDIO_THEME=cyber
```

## 🚀 وضع الإنتاج

### Docker
```bash
# تشغيل جميع الخدمات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f

# إيقاف الخدمات
docker-compose down
```

### بناء التطبيق
```bash
# بناء للإنتاج
npm run build

# بناء Electron
npm run build-windows  # Windows
npm run build-mac      # macOS
npm run build-linux    # Linux
```

## 📊 مراقبة الأداء

### Grafana Dashboard
- URL: http://localhost:3001
- Username: admin
- Password: CyberSentinel2024!

### Elasticsearch/Kibana
- Elasticsearch: http://localhost:9200
- Kibana: http://localhost:5601

## 🔐 الأمان والحماية

### تغيير كلمة المرور الافتراضية
1. سجل دخول بالبيانات الافتراضية
2. اذهب إلى الإعدادات > الأمان
3. غير كلمة المرور
4. فعل التحقق بخطوتين

### إعداد SSL/TLS
```bash
# إنشاء شهادة ذاتية التوقيع
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# نسخ الشهادات
cp cert.pem ssl/
cp key.pem ssl/
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في تحميل النموذج AI
```bash
# تأكد من تثبيت TensorFlow.js
npm install @tensorflow/tfjs

# تحقق من ذاكرة المتصفح
# افتح Developer Tools > Memory
```

#### مشاكل WebGL
```bash
# تحقق من دعم WebGL
# اذهب إلى: chrome://gpu/
# تأكد من تفعيل Hardware Acceleration
```

#### مشاكل قاعدة البيانات
```bash
# إعادة إعداد قاعدة البيانات
node scripts/setup-database.js

# تحقق من اتصال PostgreSQL
psql -h localhost -U admin -d cybersentinel
```

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع الرسمي: https://cybersentinel.pro
- 📱 تليجرام: @CyberSentinelSupport
- 🐛 الإبلاغ عن الأخطاء: GitHub Issues

### الوثائق
- [دليل المستخدم الكامل](./docs/USER_GUIDE.md)
- [دليل المطور](./docs/DEVELOPER_GUIDE.md)
- [API Documentation](./docs/API.md)
- [أمثلة الاستخدام](./docs/EXAMPLES.md)

## ⚠️ تحذيرات مهمة

### الاستخدام القانوني
- هذا البرنامج مخصص لاختبار الأمان المصرح به فقط
- لا تستخدمه ضد أنظمة لا تملكها أو لا تملك إذناً لاختبارها
- احترم جميع القوانين المحلية والدولية
- المطورون غير مسؤولين عن أي استخدام غير قانوني

### الأداء
- يتطلب موارد نظام كافية للذكاء الاصطناعي والرسوم ثلاثية الأبعاد
- قد يؤثر على أداء النظام عند تشغيل جميع الميزات
- راقب استخدام الذاكرة والمعالج

---

**🛡️ Cyber Sentinel Pro - SecOps Edition**
*Advanced Cybersecurity Testing Platform with AI & 3D Visualization*

© 2024 CyberSentinel Team. جميع الحقوق محفوظة.
