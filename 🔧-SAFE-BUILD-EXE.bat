@echo off
title 🔧 تحويل آمن إلى EXE - Cyber Sentinel Pro
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🔧  تحويل آمن إلى EXE - CYBER SENTINEL PRO  🔧                            █
echo █                                                                              █
echo █     ✅ معالجة أخطاء محسنة                                                   █
echo █     🔍 تشخيص تلقائي للمشاكل                                                █
echo █     💡 حلول فورية                                                          █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔧 مرحباً بك في المحول الآمن!
echo [INFO] هذا المحول يتضمن معالجة أخطاء محسنة وتشخيص تلقائي
echo.

REM Set error handling
setlocal enabledelayedexpansion

echo ===============================================================================
echo                              🔍 فحص المتطلبات
echo ===============================================================================
echo.

echo [STEP 1] 🔍 فحص Node.js...
echo [DEBUG] جاري التحقق من وجود Node.js...

REM Check Node.js with timeout
timeout /t 2 /nobreak >nul
node --version >nul 2>&1
set node_check=%errorlevel%

if %node_check% neq 0 (
    echo [❌] Node.js غير مثبت أو غير متاح
    echo.
    echo [ACTION] 🚀 تشغيل أداة التشخيص...
    if exist "🔍-DIAGNOSE-NODEJS.bat" (
        call "🔍-DIAGNOSE-NODEJS.bat"
    ) else (
        echo [INFO] 📥 يرجى تثبيت Node.js من: https://nodejs.org
        start https://nodejs.org
    )
    echo.
    echo [INFO] بعد تثبيت Node.js، أعد تشغيل هذا الملف
    echo [INFO] اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
) else (
    echo [✅] Node.js متاح
    for /f "tokens=*" %%i in ('node --version 2^>nul') do set node_version=%%i
    echo [INFO] الإصدار: !node_version!
)

echo.
echo [STEP 2] 📦 فحص npm...
echo [DEBUG] جاري التحقق من وجود npm...

timeout /t 1 /nobreak >nul
npm --version >nul 2>&1
set npm_check=%errorlevel%

if %npm_check% neq 0 (
    echo [❌] npm غير متاح
    echo [INFO] npm يأتي عادة مع Node.js
    echo [ACTION] 🚀 تشغيل أداة التشخيص...
    if exist "🔍-DIAGNOSE-NODEJS.bat" (
        call "🔍-DIAGNOSE-NODEJS.bat"
    )
    echo [INFO] اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
) else (
    echo [✅] npm متاح
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set npm_version=%%i
    echo [INFO] الإصدار: !npm_version!
)

echo.
echo [STEP 3] 📁 فحص ملفات المشروع...

set missing_files=0

if not exist "package.json" (
    echo [❌] package.json مفقود
    set missing_files=1
) else (
    echo [✅] package.json موجود
)

if not exist "main.js" (
    echo [❌] main.js مفقود
    set missing_files=1
) else (
    echo [✅] main.js موجود
)

if not exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [❌] ملف التطبيق الرئيسي مفقود
    set missing_files=1
) else (
    echo [✅] ملف التطبيق الرئيسي موجود
)

if %missing_files% equ 1 (
    echo.
    echo [ERROR] ❌ ملفات مطلوبة مفقودة!
    echo [INFO] تأكد من وجود جميع الملفات في نفس المجلد
    echo [INFO] اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
)

echo.
echo ===============================================================================
echo                              📥 تثبيت المتطلبات
echo ===============================================================================
echo.

echo [STEP 4] 📦 تثبيت الحزم المطلوبة...
echo [INFO] جاري تثبيت Electron و Electron Builder...
echo [WARNING] هذه العملية قد تستغرق عدة دقائق، يرجى الانتظار...
echo.

REM Create a log file for npm output
set log_file=npm_install_log.txt
echo [DEBUG] سيتم حفظ سجل التثبيت في: %log_file%

REM Try npm install with verbose output
echo [INFO] بدء التثبيت... (يمكنك مراقبة التقدم)
npm install electron electron-builder electron-updater --save-dev --verbose > %log_file% 2>&1
set install_result=%errorlevel%

if %install_result% neq 0 (
    echo.
    echo [ERROR] ❌ فشل في تثبيت المتطلبات
    echo [DEBUG] رمز الخطأ: %install_result%
    echo.
    echo [INFO] 📋 عرض آخر 10 أسطر من سجل الأخطاء:
    if exist %log_file% (
        echo ----------------------------------------
        powershell -Command "Get-Content '%log_file%' | Select-Object -Last 10"
        echo ----------------------------------------
    )
    echo.
    echo [SOLUTIONS] 💡 حلول مقترحة:
    echo    1. تحقق من اتصال الإنترنت
    echo    2. شغل Command Prompt كمدير
    echo    3. أغلق برامج الحماية مؤقتاً
    echo    4. امسح مجلد node_modules: rmdir /s /q node_modules
    echo    5. امسح npm cache: npm cache clean --force
    echo    6. جرب مرة أخرى
    echo.
    echo [ACTION] هل تريد تشغيل أداة التشخيص؟ (y/n)
    set /p run_diag="اختر (y/n): "
    if /i "!run_diag!"=="y" (
        if exist "🔍-DIAGNOSE-NODEJS.bat" (
            call "🔍-DIAGNOSE-NODEJS.bat"
        )
    )
    echo.
    echo [INFO] اضغط أي مفتاح للخروج...
    pause >nul
    exit /b 1
) else (
    echo [✅] تم تثبيت المتطلبات بنجاح!
    if exist %log_file% (
        echo [INFO] سجل التثبيت محفوظ في: %log_file%
    )
)

echo.
echo ===============================================================================
echo                              🔧 بناء التطبيق
echo ===============================================================================
echo.

echo [STEP 5] 🏗️ بناء ملف EXE...
echo [INFO] جاري إنشاء ملف EXE للتطبيق...
echo [WARNING] هذه العملية قد تستغرق 5-10 دقائق، يرجى الانتظار...
echo.

REM Create a log file for build output
set build_log=build_log.txt
echo [DEBUG] سيتم حفظ سجل البناء في: %build_log%

REM Try npm run build-win first
echo [INFO] محاولة البناء باستخدام npm script...
npm run build-win > %build_log% 2>&1
set build_result=%errorlevel%

if %build_result% neq 0 (
    echo [WARNING] ⚠️ فشل البناء الأول، جاري المحاولة بطريقة بديلة...
    
    REM Try npx electron-builder
    echo [INFO] محاولة البناء باستخدام npx...
    npx electron-builder --win >> %build_log% 2>&1
    set build_result2=%errorlevel%
    
    if %build_result2% neq 0 (
        echo.
        echo [ERROR] ❌ فشل في بناء التطبيق
        echo [DEBUG] رمز خطأ npm: %build_result%
        echo [DEBUG] رمز خطأ npx: %build_result2%
        echo.
        echo [INFO] 📋 عرض آخر 15 سطر من سجل الأخطاء:
        if exist %build_log% (
            echo ----------------------------------------
            powershell -Command "Get-Content '%build_log%' | Select-Object -Last 15"
            echo ----------------------------------------
        )
        echo.
        echo [SOLUTIONS] 💡 حلول مقترحة:
        echo    1. تحقق من مساحة القرص المتاحة (يحتاج 500+ MB)
        echo    2. أغلق برامج الحماية مؤقتاً
        echo    3. تأكد من عدم استخدام ملفات المشروع
        echo    4. جرب إعادة تثبيت node_modules
        echo    5. تحقق من صحة ملف package.json
        echo.
        echo [INFO] اضغط أي مفتاح للخروج...
        pause >nul
        exit /b 1
    )
)

echo [✅] تم بناء التطبيق بنجاح!

echo.
echo ===============================================================================
echo                              📁 فحص النتائج
echo ===============================================================================
echo.

echo [STEP 6] 🔍 فحص الملفات المُنشأة...

if exist "dist" (
    echo [✅] مجلد dist موجود
    echo.
    echo [INFO] 📂 محتويات مجلد dist:
    dir dist /b 2>nul
    echo.
    
    REM Look for exe files
    set exe_found=0
    for /r dist %%i in (*.exe) do (
        echo [✅] ملف EXE: %%~nxi
        echo [PATH] المسار: %%i
        set exe_found=1
    )
    
    if %exe_found% equ 0 (
        echo [WARNING] ⚠️ لم يتم العثور على ملف EXE
        echo [INFO] تحقق من محتويات مجلد dist يدوياً
    )
    
    REM Look for installer files
    for /r dist %%i in (*.msi *.exe) do (
        if "%%~nxi" neq "electron.exe" (
            echo [✅] ملف التثبيت: %%~nxi
        )
    )
    
) else (
    echo [WARNING] ⚠️ مجلد dist غير موجود
    echo [INFO] قد تكون الملفات في مجلد آخر
    echo [INFO] تحقق من سجل البناء: %build_log%
)

echo.
echo ===============================================================================
echo                              ✅ اكتمل التحويل!
echo ===============================================================================
echo.

echo 🎉 تم تحويل Cyber Sentinel Pro إلى EXE بنجاح!
echo.

if exist "dist" (
    echo [ACTION] 📂 فتح مجلد النتائج...
    start dist
    echo.
)

echo 📋 الملفات المُنشأة:
echo    📂 مجلد dist - يحتوي على جميع الملفات
echo    💻 ملف EXE - البرنامج القابل للتشغيل
echo    📦 ملف التثبيت - لتوزيع البرنامج
echo    📄 سجلات العملية - للمراجعة والتشخيص
echo.

echo 🚀 للتشغيل:
echo    1. ابحث عن ملف EXE في مجلد dist
echo    2. انقر نقراً مزدوجاً لتشغيل التطبيق
echo    3. سجل دخول بحساب المدير: admin / JaMaL@123
echo.

echo 💡 نصائح:
echo    • احتفظ بملفات السجل للمراجعة
echo    • اختبر التطبيق قبل التوزيع
echo    • احتفظ بنسخة احتياطية من المشروع
echo.

echo 📞 للدعم:
echo    📧 البريد: <EMAIL>
echo    📖 الدليل: 📖-EXE-CONVERSION-GUIDE.txt
echo    🔍 التشخيص: 🔍-DIAGNOSE-NODEJS.bat
echo.

echo [INFO] اضغط أي مفتاح لإنهاء البرنامج...
pause >nul

REM Cleanup temporary files (optional)
if exist %log_file% (
    echo [CLEANUP] حذف ملفات مؤقتة...
    del %log_file% >nul 2>&1
)

exit /b 0
