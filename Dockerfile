# Cyber Sentinel Pro - Multi-stage Docker Build
# Base image with Node.js and Python
FROM node:18-bullseye-slim as base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    nmap \
    masscan \
    nikto \
    sqlmap \
    dirb \
    gobuster \
    hydra \
    john \
    hashcat \
    metasploit-framework \
    wireshark-common \
    tcpdump \
    netcat \
    socat \
    curl \
    wget \
    git \
    build-essential \
    libpcap-dev \
    libssl-dev \
    libffi-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY requirements.txt ./

# Install Node.js dependencies
RUN npm ci --only=production && npm cache clean --force

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Development stage
FROM base as development

# Install development dependencies
RUN npm ci && npm cache clean --force

# Install additional development tools
RUN pip3 install --no-cache-dir \
    pytest \
    black \
    flake8 \
    jupyter \
    ipython

# Copy source code
COPY . .

# Expose ports
EXPOSE 3000 5000 8080

# Development command
CMD ["npm", "run", "dev"]

# Production build stage
FROM base as build

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:18-bullseye-slim as production

# Install only runtime dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    nmap \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r cybersentinel && useradd -r -g cybersentinel cybersentinel

# Set working directory
WORKDIR /app

# Copy built application
COPY --from=build /app/build ./build
COPY --from=build /app/server ./server
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/package*.json ./
COPY --from=build /app/requirements.txt ./

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p uploads reports logs temp backups ssl tools && \
    chown -R cybersentinel:cybersentinel /app

# Copy security tools and configurations
COPY --from=build /app/tools ./tools
COPY --from=build /app/config ./config

# Set permissions
RUN chmod +x tools/*.sh && \
    chown -R cybersentinel:cybersentinel /app

# Switch to non-root user
USER cybersentinel

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/status || exit 1

# Expose ports
EXPOSE 5000

# Production command
CMD ["node", "server/index.js"]

# Security scanning stage
FROM base as security-tools

# Install additional security tools
RUN apt-get update && apt-get install -y \
    aircrack-ng \
    reaver \
    bettercap \
    ettercap-text-only \
    dnsrecon \
    fierce \
    theharvester \
    whatweb \
    wpscan \
    && rm -rf /var/lib/apt/lists/*

# Install additional Python security tools
RUN pip3 install --no-cache-dir \
    impacket \
    pwntools \
    ropper \
    ropgadget \
    binwalk \
    volatility3 \
    yara-python \
    pycrypto \
    requests-oauthlib

# Copy application
COPY --from=build /app .

# Security tools command
CMD ["python3", "tools/security_scanner.py"]

# Android testing stage
FROM base as android-tools

# Install Android SDK and tools
RUN apt-get update && apt-get install -y \
    openjdk-11-jdk \
    android-tools-adb \
    android-tools-fastboot \
    && rm -rf /var/lib/apt/lists/*

# Install Android security tools
RUN pip3 install --no-cache-dir \
    frida-tools \
    objection \
    androguard \
    apktool

# Copy application
COPY --from=build /app .

# Android tools command
CMD ["python3", "tools/android_scanner.py"]
