import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>, Card, CardContent, Typography, IconButton, Tooltip, Slider } from '@mui/material';
import { 
  FullscreenExit, 
  Fullscreen, 
  Refresh, 
  Settings,
  Visibility,
  VisibilityOff 
} from '@mui/icons-material';

const Network3D = ({ networkData = [], threats = [] }) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showThreats, setShowThreats] = useState(true);
  const [rotationSpeed, setRotationSpeed] = useState(0.01);
  const [zoomLevel, setZoomLevel] = useState(1);

  // متغيرات WebGL
  const glRef = useRef(null);
  const programRef = useRef(null);
  const nodesRef = useRef([]);
  const connectionsRef = useRef([]);
  const timeRef = useRef(0);

  // Vertex Shader للعقد
  const vertexShaderSource = `
    attribute vec3 position;
    attribute vec3 color;
    attribute float size;
    
    uniform mat4 modelViewMatrix;
    uniform mat4 projectionMatrix;
    uniform float time;
    
    varying vec3 vColor;
    varying float vSize;
    
    void main() {
      vColor = color;
      vSize = size;
      
      // تأثير النبض للعقد المهددة
      float pulse = sin(time * 5.0) * 0.3 + 0.7;
      vec3 pos = position;
      
      if (color.r > 0.8 && color.g < 0.2) { // عقدة مهددة (حمراء)
        pos *= pulse;
      }
      
      gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
      gl_PointSize = size * (1.0 + pulse * 0.5);
    }
  `;

  // Fragment Shader للعقد
  const fragmentShaderSource = `
    precision mediump float;
    
    varying vec3 vColor;
    varying float vSize;
    
    void main() {
      vec2 center = gl_PointCoord - vec2(0.5);
      float dist = length(center);
      
      if (dist > 0.5) {
        discard;
      }
      
      // تأثير الإضاءة
      float intensity = 1.0 - dist * 2.0;
      intensity = pow(intensity, 2.0);
      
      // تأثير الحافة المضيئة
      float edge = smoothstep(0.4, 0.5, dist);
      vec3 finalColor = vColor * intensity + vColor * edge * 0.5;
      
      gl_FragColor = vec4(finalColor, intensity);
    }
  `;

  // تهيئة WebGL
  const initWebGL = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      console.error('WebGL غير مدعوم');
      return;
    }

    glRef.current = gl;

    // إنشاء الشيدرز
    const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);

    // إنشاء البرنامج
    const program = createProgram(gl, vertexShader, fragmentShader);
    programRef.current = program;

    // إعدادات WebGL
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    gl.enable(gl.DEPTH_TEST);
    gl.clearColor(0.02, 0.02, 0.1, 1.0);

    // بدء الرسم
    animate();
  };

  // إنشاء شيدر
  const createShader = (gl, type, source) => {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);

    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('خطأ في تجميع الشيدر:', gl.getShaderInfoLog(shader));
      gl.deleteShader(shader);
      return null;
    }

    return shader;
  };

  // إنشاء برنامج
  const createProgram = (gl, vertexShader, fragmentShader) => {
    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      console.error('خطأ في ربط البرنامج:', gl.getProgramInfoLog(program));
      gl.deleteProgram(program);
      return null;
    }

    return program;
  };

  // توليد بيانات الشبكة المحاكاة
  const generateNetworkData = () => {
    const nodes = [];
    const connections = [];

    // إنشاء عقد الشبكة
    for (let i = 0; i < 50; i++) {
      const isThreat = Math.random() < 0.1; // 10% احتمال تهديد
      const isServer = Math.random() < 0.2; // 20% احتمال خادم
      
      nodes.push({
        id: i,
        x: (Math.random() - 0.5) * 10,
        y: (Math.random() - 0.5) * 10,
        z: (Math.random() - 0.5) * 10,
        color: isThreat ? [1.0, 0.2, 0.2] : isServer ? [0.2, 0.8, 1.0] : [0.2, 1.0, 0.4],
        size: isServer ? 15 : isThreat ? 12 : 8,
        type: isThreat ? 'threat' : isServer ? 'server' : 'client',
        connections: []
      });
    }

    // إنشاء الاتصالات
    for (let i = 0; i < nodes.length; i++) {
      const connectionCount = Math.floor(Math.random() * 5) + 1;
      for (let j = 0; j < connectionCount; j++) {
        const targetIndex = Math.floor(Math.random() * nodes.length);
        if (targetIndex !== i && !nodes[i].connections.includes(targetIndex)) {
          nodes[i].connections.push(targetIndex);
          
          connections.push({
            from: i,
            to: targetIndex,
            strength: Math.random(),
            isActive: Math.random() < 0.3
          });
        }
      }
    }

    nodesRef.current = nodes;
    connectionsRef.current = connections;
  };

  // رسم الشبكة
  const drawNetwork = () => {
    const gl = glRef.current;
    const program = programRef.current;
    
    if (!gl || !program) return;

    // مسح الشاشة
    gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
    gl.useProgram(program);

    // إعداد المصفوفات
    const modelViewMatrix = createModelViewMatrix();
    const projectionMatrix = createProjectionMatrix();

    // تمرير المتغيرات للشيدر
    const modelViewLocation = gl.getUniformLocation(program, 'modelViewMatrix');
    const projectionLocation = gl.getUniformLocation(program, 'projectionMatrix');
    const timeLocation = gl.getUniformLocation(program, 'time');

    gl.uniformMatrix4fv(modelViewLocation, false, modelViewMatrix);
    gl.uniformMatrix4fv(projectionLocation, false, projectionMatrix);
    gl.uniform1f(timeLocation, timeRef.current);

    // رسم الاتصالات
    drawConnections(gl, program);

    // رسم العقد
    drawNodes(gl, program);
  };

  // رسم العقد
  const drawNodes = (gl, program) => {
    const nodes = nodesRef.current;
    if (nodes.length === 0) return;

    // إعداد البيانات
    const positions = [];
    const colors = [];
    const sizes = [];

    nodes.forEach(node => {
      positions.push(node.x, node.y, node.z);
      colors.push(...node.color);
      sizes.push(node.size);
    });

    // إنشاء البوفرز
    const positionBuffer = gl.createBuffer();
    const colorBuffer = gl.createBuffer();
    const sizeBuffer = gl.createBuffer();

    // موضع العقد
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);
    const positionLocation = gl.getAttribLocation(program, 'position');
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

    // ألوان العقد
    gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(colors), gl.STATIC_DRAW);
    const colorLocation = gl.getAttribLocation(program, 'color');
    gl.enableVertexAttribArray(colorLocation);
    gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

    // أحجام العقد
    gl.bindBuffer(gl.ARRAY_BUFFER, sizeBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(sizes), gl.STATIC_DRAW);
    const sizeLocation = gl.getAttribLocation(program, 'size');
    gl.enableVertexAttribArray(sizeLocation);
    gl.vertexAttribPointer(sizeLocation, 1, gl.FLOAT, false, 0, 0);

    // رسم العقد
    gl.drawArrays(gl.POINTS, 0, nodes.length);

    // تنظيف البوفرز
    gl.deleteBuffer(positionBuffer);
    gl.deleteBuffer(colorBuffer);
    gl.deleteBuffer(sizeBuffer);
  };

  // رسم الاتصالات
  const drawConnections = (gl, program) => {
    const nodes = nodesRef.current;
    const connections = connectionsRef.current;

    connections.forEach(conn => {
      if (!conn.isActive && !showThreats) return;

      const fromNode = nodes[conn.from];
      const toNode = nodes[conn.to];

      if (!fromNode || !toNode) return;

      // رسم خط الاتصال
      const positions = [
        fromNode.x, fromNode.y, fromNode.z,
        toNode.x, toNode.y, toNode.z
      ];

      const connectionBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, connectionBuffer);
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);

      const positionLocation = gl.getAttribLocation(program, 'position');
      gl.enableVertexAttribArray(positionLocation);
      gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

      // لون الاتصال
      const connectionColor = conn.isActive ? [1.0, 0.8, 0.2] : [0.3, 0.3, 0.8];
      const colorBuffer = gl.createBuffer();
      gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
      gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
        ...connectionColor, ...connectionColor
      ]), gl.STATIC_DRAW);

      const colorLocation = gl.getAttribLocation(program, 'color');
      gl.enableVertexAttribArray(colorLocation);
      gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

      gl.drawArrays(gl.LINES, 0, 2);

      gl.deleteBuffer(connectionBuffer);
      gl.deleteBuffer(colorBuffer);
    });
  };

  // إنشاء مصفوفة العرض
  const createModelViewMatrix = () => {
    const matrix = new Float32Array(16);
    
    // مصفوفة الهوية
    matrix[0] = 1; matrix[5] = 1; matrix[10] = 1; matrix[15] = 1;
    
    // الدوران
    const rotation = timeRef.current * rotationSpeed;
    const cos = Math.cos(rotation);
    const sin = Math.sin(rotation);
    
    // دوران حول المحور Y
    matrix[0] = cos;
    matrix[2] = sin;
    matrix[8] = -sin;
    matrix[10] = cos;
    
    // التكبير
    for (let i = 0; i < 12; i++) {
      matrix[i] *= zoomLevel;
    }
    
    // الإزاحة
    matrix[14] = -20; // إبعاد الكاميرا
    
    return matrix;
  };

  // إنشاء مصفوفة الإسقاط
  const createProjectionMatrix = () => {
    const canvas = canvasRef.current;
    if (!canvas) return new Float32Array(16);

    const aspect = canvas.width / canvas.height;
    const fov = Math.PI / 4;
    const near = 0.1;
    const far = 100;

    const f = 1.0 / Math.tan(fov / 2);
    const matrix = new Float32Array(16);

    matrix[0] = f / aspect;
    matrix[5] = f;
    matrix[10] = (far + near) / (near - far);
    matrix[11] = -1;
    matrix[14] = (2 * far * near) / (near - far);

    return matrix;
  };

  // حلقة الرسم
  const animate = () => {
    timeRef.current += 0.016; // 60 FPS
    drawNetwork();
    animationRef.current = requestAnimationFrame(animate);
  };

  // تغيير حجم الشاشة
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // تحديث البيانات
  const refreshData = () => {
    generateNetworkData();
  };

  // تأثيرات جانبية
  useEffect(() => {
    generateNetworkData();
    initWebGL();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeCanvas = () => {
      const container = canvas.parentElement;
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
      
      if (glRef.current) {
        glRef.current.viewport(0, 0, canvas.width, canvas.height);
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [isFullscreen]);

  return (
    <Card
      sx={{
        backgroundColor: 'rgba(26, 26, 46, 0.9)',
        border: '1px solid rgba(0, 255, 65, 0.3)',
        borderRadius: 2,
        height: isFullscreen ? '100vh' : '600px',
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        right: isFullscreen ? 0 : 'auto',
        bottom: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto'
      }}
    >
      <CardContent sx={{ height: '100%', p: 2 }}>
        {/* شريط التحكم */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            خريطة الشبكة ثلاثية الأبعاد
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="إظهار/إخفاء التهديدات">
              <IconButton
                onClick={() => setShowThreats(!showThreats)}
                color={showThreats ? 'primary' : 'default'}
              >
                {showThreats ? <Visibility /> : <VisibilityOff />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title="تحديث البيانات">
              <IconButton onClick={refreshData}>
                <Refresh />
              </IconButton>
            </Tooltip>
            
            <Tooltip title={isFullscreen ? 'خروج من ملء الشاشة' : 'ملء الشاشة'}>
              <IconButton onClick={toggleFullscreen}>
                {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* أدوات التحكم */}
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Box sx={{ minWidth: 120 }}>
            <Typography variant="caption" color="text.secondary">
              سرعة الدوران
            </Typography>
            <Slider
              value={rotationSpeed * 1000}
              onChange={(e, value) => setRotationSpeed(value / 1000)}
              min={0}
              max={50}
              size="small"
            />
          </Box>
          
          <Box sx={{ minWidth: 120 }}>
            <Typography variant="caption" color="text.secondary">
              التكبير
            </Typography>
            <Slider
              value={zoomLevel}
              onChange={(e, value) => setZoomLevel(value)}
              min={0.5}
              max={3}
              step={0.1}
              size="small"
            />
          </Box>
        </Box>

        {/* Canvas للرسم ثلاثي الأبعاد */}
        <Box
          sx={{
            position: 'relative',
            height: 'calc(100% - 120px)',
            border: '1px solid rgba(0, 255, 65, 0.2)',
            borderRadius: 1,
            overflow: 'hidden'
          }}
        >
          <canvas
            ref={canvasRef}
            style={{
              width: '100%',
              height: '100%',
              display: 'block'
            }}
          />
          
          {/* مفتاح الألوان */}
          <Box
            sx={{
              position: 'absolute',
              top: 10,
              left: 10,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              padding: 1,
              borderRadius: 1,
              fontSize: '0.75rem'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box sx={{ width: 12, height: 12, backgroundColor: '#33ff66', mr: 1, borderRadius: '50%' }} />
              <Typography variant="caption">أجهزة عادية</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box sx={{ width: 12, height: 12, backgroundColor: '#33ccff', mr: 1, borderRadius: '50%' }} />
              <Typography variant="caption">خوادم</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{ width: 12, height: 12, backgroundColor: '#ff3333', mr: 1, borderRadius: '50%' }} />
              <Typography variant="caption">تهديدات</Typography>
            </Box>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default Network3D;
