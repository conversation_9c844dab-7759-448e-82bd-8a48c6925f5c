@echo off
title 🛡️ Cyber Sentinel Pro - Quick Start
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🛡️  CYBER SENTINEL PRO - SecOps Edition v1.0.0  🛡️                        █
echo █                                                                              █
echo █     🤖 AI + 🎮 3D Visualization + 🍯 Honeypot + 🔊 Audio                    █
echo █                                                                              █
echo █     Advanced Cybersecurity Testing Platform                                 █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🚀 تشغيل سريع لـ Cyber Sentinel Pro...
echo.

REM Quick check for Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] Node.js غير مثبت! يرجى تثبيته من: https://nodejs.org/
    pause
    exit /b 1
)

echo [✅] Node.js متوفر
echo.

REM Install dependencies if needed
if not exist "node_modules" (
    echo [📦] تثبيت التبعيات...
    npm install --silent
    if %errorlevel% neq 0 (
        echo [❌] فشل في التثبيت!
        pause
        exit /b 1
    )
)

echo [✅] التبعيات جاهزة
echo.

REM Create basic files if missing
if not exist "public\index.html" (
    if not exist "public" mkdir public
    echo ^<!DOCTYPE html^>^<html^>^<head^>^<meta charset="utf-8"^>^<title^>Cyber Sentinel Pro^</title^>^</head^>^<body^>^<div id="root"^>^</div^>^</body^>^</html^> > public\index.html
)

if not exist ".env" (
    echo GENERATE_SOURCEMAP=false > .env
    echo FAST_REFRESH=true >> .env
)

echo ===============================================================================
echo 🔑 بيانات الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: JaMaL@123
echo.
echo 🌐 سيفتح التطبيق على: http://localhost:3000
echo ===============================================================================
echo.

REM Open browser
start "" cmd /c "timeout /t 8 /nobreak >nul && start http://localhost:3000"

echo [🚀] تشغيل التطبيق...
echo [ℹ️] للإيقاف: اضغط Ctrl+C
echo.

npm start

echo.
echo [✅] تم إيقاف التطبيق بنجاح
pause
