@echo off
setlocal enabledelayedexpansion
title Cyber Sentinel Pro - Fixed Launcher
color 0A

REM Immediate pause to prevent any auto-close
echo Cyber Sentinel Pro - Fixed Launcher
echo.
echo This launcher will help you convert the app to EXE
echo without terminal auto-close issues.
echo.
echo Press any key to continue...
pause >nul

:main_menu
cls
echo.
echo ========================================
echo    Cyber Sentinel Pro - Fixed Launcher
echo ========================================
echo.
echo Choose your option:
echo.
echo 1. Test Terminal (Check if terminal works)
echo 2. Diagnose System (Check Node.js and requirements)
echo 3. Simple Build (Basic EXE conversion)
echo 4. Advanced Build (Full featured EXE)
echo 5. Portable Version (No Node.js needed)
echo 6. View Quick Fix Guide
echo 7. Exit
echo.
set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" goto test_terminal
if "%choice%"=="2" goto diagnose
if "%choice%"=="3" goto simple_build
if "%choice%"=="4" goto advanced_build
if "%choice%"=="5" goto portable
if "%choice%"=="6" goto guide
if "%choice%"=="7" goto exit
goto invalid_choice

:test_terminal
cls
echo.
echo ========================================
echo           Testing Terminal
echo ========================================
echo.
echo Running terminal test...
echo This will check if your terminal works correctly.
echo.
if exist "TEST-TERMINAL.bat" (
    call "TEST-TERMINAL.bat"
) else (
    echo ERROR: TEST-TERMINAL.bat not found
    echo Please make sure all files are in the same folder.
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:diagnose
cls
echo.
echo ========================================
echo         System Diagnosis
echo ========================================
echo.
echo Running system diagnosis...
echo This will check Node.js and project requirements.
echo.
if exist "🔍-SIMPLE-DIAGNOSIS.bat" (
    call "🔍-SIMPLE-DIAGNOSIS.bat"
) else (
    echo ERROR: Diagnosis file not found
    echo Please make sure all files are in the same folder.
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:simple_build
cls
echo.
echo ========================================
echo           Simple Build
echo ========================================
echo.
echo Starting simple EXE build...
echo This is the most basic and reliable method.
echo.
if exist "SIMPLE-BUILD.bat" (
    call "SIMPLE-BUILD.bat"
) else (
    echo ERROR: SIMPLE-BUILD.bat not found
    echo Please make sure all files are in the same folder.
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:advanced_build
cls
echo.
echo ========================================
echo          Advanced Build
echo ========================================
echo.
echo Starting advanced EXE build...
echo This includes enhanced error handling.
echo.
if exist "🔧-FIXED-BUILD-EXE.bat" (
    call "🔧-FIXED-BUILD-EXE.bat"
) else (
    echo ERROR: Advanced build file not found
    echo Trying alternative...
    if exist "🔧-SAFE-BUILD-EXE.bat" (
        call "🔧-SAFE-BUILD-EXE.bat"
    ) else (
        echo ERROR: No advanced build files found
        echo Please use Simple Build instead.
    )
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:portable
cls
echo.
echo ========================================
echo         Portable Version
echo ========================================
echo.
echo Creating portable version...
echo This doesn't require Node.js installation.
echo.
if exist "🔧-SIMPLE-EXE-CONVERTER.bat" (
    call "🔧-SIMPLE-EXE-CONVERTER.bat"
) else (
    echo ERROR: Portable converter not found
    echo Please make sure all files are in the same folder.
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:guide
cls
echo.
echo ========================================
echo         Quick Fix Guide
echo ========================================
echo.
if exist "QUICK-FIX-GUIDE.txt" (
    echo Opening Quick Fix Guide...
    start notepad "QUICK-FIX-GUIDE.txt"
    echo.
    echo The guide has been opened in Notepad.
    echo It contains detailed solutions for common issues.
) else (
    echo ERROR: Quick Fix Guide not found
    echo.
    echo MANUAL TROUBLESHOOTING:
    echo.
    echo 1. Terminal closes immediately:
    echo    - Try TEST-TERMINAL.bat first
    echo    - Run Command Prompt as Administrator
    echo.
    echo 2. Node.js not found:
    echo    - Install from https://nodejs.org
    echo    - Choose LTS version
    echo    - Restart Command Prompt after install
    echo.
    echo 3. Build fails:
    echo    - Check internet connection
    echo    - Disable antivirus temporarily
    echo    - Make sure you have 1GB+ free space
    echo.
    echo 4. Files missing:
    echo    - Make sure all project files are present
    echo    - Check file names are correct
    echo.
)
echo.
echo Press any key to return to main menu...
pause >nul
goto main_menu

:invalid_choice
cls
echo.
echo ========================================
echo           Invalid Choice
echo ========================================
echo.
echo ERROR: Invalid choice entered.
echo Please enter a number from 1 to 7.
echo.
echo Press any key to try again...
pause >nul
goto main_menu

:exit
cls
echo.
echo ========================================
echo              Goodbye!
echo ========================================
echo.
echo Thank you for using Cyber Sentinel Pro Fixed Launcher.
echo.
echo If you need help:
echo - Email: <EMAIL>
echo - Website: https://cybersentinel.pro
echo.
echo Press any key to exit...
pause >nul
exit /b 0
