import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Divider,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Security,
  Person,
  Lock,
  Shield,
  VpnKey
} from '@mui/icons-material';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const LoginPage = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    twoFactorCode: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [requiresTwoFactor, setRequiresTwoFactor] = useState(false);
  const [securityLevel, setSecurityLevel] = useState(0);
  const [attempts, setAttempts] = useState(0);
  const maxAttempts = 5;

  // تحديث مستوى الأمان بناءً على كلمة المرور
  useEffect(() => {
    const calculateSecurityLevel = (password) => {
      let level = 0;
      if (password.length >= 8) level += 20;
      if (/[A-Z]/.test(password)) level += 20;
      if (/[a-z]/.test(password)) level += 20;
      if (/[0-9]/.test(password)) level += 20;
      if (/[^A-Za-z0-9]/.test(password)) level += 20;
      return level;
    };

    setSecurityLevel(calculateSecurityLevel(formData.password));
  }, [formData.password]);

  // معالج تغيير الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  // معالج إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (attempts >= maxAttempts) {
      setError('تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة لاحقاً.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const result = await login(formData.username, formData.password);

      if (result.success) {
        // تسجيل دخول ناجح
        setFormData({ username: '', password: '', twoFactorCode: '' });
        setAttempts(0);
        navigate('/dashboard');
      } else {
        // خطأ في تسجيل الدخول
        setError(result.message || 'خطأ في تسجيل الدخول');
        setAttempts(prev => prev + 1);
      }
    } catch (error) {
      setError('خطأ في الاتصال بالخادم');
      setAttempts(prev => prev + 1);
    } finally {
      setLoading(false);
    }
  };

  // لون مستوى الأمان
  const getSecurityColor = () => {
    if (securityLevel < 40) return 'error';
    if (securityLevel < 80) return 'warning';
    return 'success';
  };

  // نص مستوى الأمان
  const getSecurityText = () => {
    if (securityLevel < 40) return 'ضعيف';
    if (securityLevel < 80) return 'متوسط';
    return 'قوي';
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
        position: 'relative'
      }}
    >
      {/* تأثير الشبكة السيبرانية */}
      <Box
        className="cyber-grid"
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          pointerEvents: 'none'
        }}
      />

      <Card
        sx={{
          maxWidth: 450,
          width: '100%',
          backgroundColor: 'rgba(26, 26, 46, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(0, 255, 65, 0.3)',
          borderRadius: 3,
          boxShadow: '0 20px 40px rgba(0, 255, 65, 0.1)',
          position: 'relative',
          overflow: 'hidden'
        }}
        className="fade-in"
      >
        {/* شريط التحميل */}
        {loading && (
          <LinearProgress
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              backgroundColor: 'rgba(0, 255, 65, 0.1)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#00ff41'
              }
            }}
          />
        )}

        <CardContent sx={{ padding: 4 }}>
          {/* العنوان */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Shield
              sx={{
                fontSize: 60,
                color: 'primary.main',
                mb: 2,
                filter: 'drop-shadow(0 0 10px #00ff41)'
              }}
            />
            <Typography
              variant="h4"
              component="h1"
              className="cyber-glow"
              sx={{ mb: 1, fontWeight: 'bold' }}
            >
              Cyber Sentinel Pro
            </Typography>
            <Typography variant="body2" color="text.secondary">
              SecOps Edition - تسجيل دخول آمن
            </Typography>
            
            {/* مؤشر محاولات تسجيل الدخول */}
            {attempts > 0 && (
              <Chip
                label={`المحاولات المتبقية: ${maxAttempts - attempts}`}
                color="warning"
                size="small"
                sx={{ mt: 1 }}
              />
            )}
          </Box>

          {/* رسالة الخطأ */}
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                backgroundColor: 'rgba(255, 0, 64, 0.1)',
                border: '1px solid rgba(255, 0, 64, 0.3)',
                '& .MuiAlert-icon': {
                  color: '#ff0040'
                }
              }}
            >
              {error}
            </Alert>
          )}

          {/* نموذج تسجيل الدخول */}
          <Box component="form" onSubmit={handleSubmit}>
            {/* اسم المستخدم */}
            <TextField
              fullWidth
              name="username"
              label="اسم المستخدم"
              value={formData.username}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="primary" />
                  </InputAdornment>
                ),
              }}
            />

            {/* كلمة المرور */}
            <TextField
              fullWidth
              name="password"
              type={showPassword ? 'text' : 'password'}
              label="كلمة المرور"
              value={formData.password}
              onChange={handleChange}
              required
              disabled={loading}
              sx={{ mb: 2 }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="primary" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {/* مؤشر قوة كلمة المرور */}
            {formData.password && (
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="caption" sx={{ mr: 1 }}>
                    قوة كلمة المرور:
                  </Typography>
                  <Chip
                    label={getSecurityText()}
                    color={getSecurityColor()}
                    size="small"
                  />
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={securityLevel}
                  color={getSecurityColor()}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }}
                />
              </Box>
            )}

            {/* رمز التحقق بخطوتين */}
            {requiresTwoFactor && (
              <TextField
                fullWidth
                name="twoFactorCode"
                label="رمز التحقق بخطوتين"
                value={formData.twoFactorCode}
                onChange={handleChange}
                required
                disabled={loading}
                sx={{ mb: 3 }}
                inputProps={{
                  maxLength: 6,
                  style: { textAlign: 'center', fontSize: '1.2rem', letterSpacing: '0.5rem' }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <VpnKey color="primary" />
                    </InputAdornment>
                  ),
                }}
                helperText="أدخل الرمز المكون من 6 أرقام من تطبيق المصادقة"
              />
            )}

            {/* زر تسجيل الدخول */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading || attempts >= maxAttempts}
              sx={{
                mb: 3,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #00ff41 30%, #00ccff 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #00cc33 30%, #0099cc 90%)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(0, 255, 65, 0.4)'
                }
              }}
              startIcon={<Security />}
            >
              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </Button>

            <Divider sx={{ mb: 3, borderColor: 'rgba(0, 255, 65, 0.2)' }} />

            {/* رابط التسجيل */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                ليس لديك حساب؟{' '}
                <Link
                  to="/register"
                  style={{
                    color: '#00ff41',
                    textDecoration: 'none',
                    fontWeight: 'bold'
                  }}
                >
                  إنشاء حساب جديد
                </Link>
              </Typography>
            </Box>
          </Box>

          {/* معلومات الأمان */}
          <Box
            sx={{
              mt: 4,
              p: 2,
              backgroundColor: 'rgba(0, 255, 65, 0.05)',
              border: '1px solid rgba(0, 255, 65, 0.2)',
              borderRadius: 2
            }}
          >
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center' }}>
              🔒 محمي بتشفير AES-256 | 🛡️ مراقبة أمنية 24/7
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LoginPage;
