<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Cyber Sentinel Pro - Full Version</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        /* Matrix Background */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }
        
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center;
            padding: 2rem;
            background: rgba(26, 26, 46, 0.9);
            border-radius: 15px;
            margin-bottom: 2rem;
            border: 1px solid rgba(0, 255, 65, 0.3);
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.1);
            backdrop-filter: blur(10px);
        }
        .logo {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff41, #00ccff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 1rem;
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(0, 255, 65, 0.5); }
            to { text-shadow: 0 0 30px rgba(0, 255, 65, 0.8); }
        }
        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 1rem;
        }
        
        /* Auth Container */
        .auth-container {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            max-width: 450px;
            margin: 0 auto 2rem;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-radius: 8px;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.3);
        }
        .auth-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.3);
            border: none;
            color: #cccccc;
            font-size: 1rem;
        }
        .auth-tab.active {
            background: linear-gradient(45deg, #00ff41, #00ccff);
            color: #000000;
            font-weight: bold;
        }
        
        .form-group { margin-bottom: 1rem; }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #00ff41;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 8px;
            color: #ffffff;
            font-size: 1rem;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #00ff41;
            box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
        }
        
        .btn {
            background: linear-gradient(45deg, #00ff41, #00ccff);
            color: #000000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 65, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #ff0040, #ff6600);
        }
        
        /* Dashboard */
        .dashboard { display: none; }
        .user-info {
            background: rgba(0, 0, 0, 0.5);
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .status-bar {
            background: rgba(0, 0, 0, 0.8);
            padding: 1rem;
            border-radius: 10px;
            margin: 2rem 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff41;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .feature-card {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 65, 0.2);
            border-color: rgba(0, 255, 65, 0.6);
        }
        .feature-card.active {
            border-color: #00ff41;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #00ff41;
        }
        .feature-description {
            color: #cccccc;
            line-height: 1.6;
        }
        
        /* Feature Panels */
        .feature-panel {
            display: none;
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }
        .feature-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .terminal {
            background: #000000;
            border: 1px solid #00ff41;
            border-radius: 10px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 2rem 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .terminal-line {
            margin: 0.5rem 0;
        }
        .terminal-prompt { color: #00ff41; }
        .terminal-output { color: #00ccff; }
        .terminal-warning { color: #ffaa00; }
        .terminal-error { color: #ff0040; }
        .terminal-success { color: #00ff41; }
        
        /* Network Visualization */
        .network-canvas {
            width: 100%;
            height: 400px;
            background: #000000;
            border: 1px solid #00ff41;
            border-radius: 10px;
            position: relative;
            overflow: hidden;
        }
        
        /* Charts */
        .chart-container {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        /* Tools Grid */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .tool-card {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 255, 65, 0.3);
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .tool-card:hover {
            border-color: #00ff41;
            box-shadow: 0 0 15px rgba(0, 255, 65, 0.2);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .logo { font-size: 2rem; }
            .features-grid { grid-template-columns: 1fr; }
            .status-bar { flex-direction: column; text-align: center; }
            .user-info { flex-direction: column; gap: 1rem; }
        }
        
        /* Animations */
        @keyframes typewriter {
            0% { width: 0; }
            100% { width: 100%; }
        }
        
        .typing {
            overflow: hidden;
            border-right: 2px solid #00ff41;
            white-space: nowrap;
            animation: typewriter 3s steps(40) 1s 1 normal both;
        }
        
        /* Alert */
        .alert {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid rgba(255, 170, 0, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin: 2rem 0;
            text-align: center;
        }
        .alert h3 {
            color: #ffaa00;
            margin-bottom: 0.5rem;
        }
        
        /* Loading */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 255, 65, 0.3);
            border-radius: 50%;
            border-top-color: #00ff41;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Matrix Background -->
    <canvas class="matrix-bg" id="matrix-canvas"></canvas>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛡️ CYBER SENTINEL PRO</div>
            <div class="subtitle">SecOps Edition - Full Version</div>
            <div class="subtitle">النسخة الكاملة - منصة اختبار الأمان السيبراني المتقدمة</div>
        </div>

        <!-- Authentication Section -->
        <div id="auth-section" class="auth-container">
            <!-- Auth Tabs -->
            <div class="auth-tabs">
                <button class="auth-tab active" onclick="switchTab('login')">تسجيل الدخول</button>
                <button class="auth-tab" onclick="switchTab('register')">إنشاء حساب جديد</button>
            </div>

            <!-- Login Form -->
            <div id="login-form">
                <h2 style="text-align: center; margin-bottom: 2rem; color: #00ff41;">تسجيل الدخول</h2>
                <div class="form-group">
                    <label for="login-username">اسم المستخدم</label>
                    <input type="text" id="login-username" value="admin" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label for="login-password">كلمة المرور</label>
                    <input type="password" id="login-password" value="JaMaL@123" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()">دخول</button>
                <div style="text-align: center; font-size: 0.9rem; color: #cccccc;">
                    <p>الحساب الافتراضي: admin / JaMaL@123</p>
                </div>
            </div>

            <!-- Register Form -->
            <div id="register-form" style="display: none;">
                <h2 style="text-align: center; margin-bottom: 2rem; color: #00ff41;">إنشاء حساب جديد</h2>
                <div class="form-group">
                    <label for="reg-fullname">الاسم الكامل</label>
                    <input type="text" id="reg-fullname" placeholder="أدخل اسمك الكامل">
                </div>
                <div class="form-group">
                    <label for="reg-username">اسم المستخدم</label>
                    <input type="text" id="reg-username" placeholder="اختر اسم مستخدم">
                </div>
                <div class="form-group">
                    <label for="reg-email">البريد الإلكتروني</label>
                    <input type="email" id="reg-email" placeholder="أدخل بريدك الإلكتروني">
                </div>
                <div class="form-group">
                    <label for="reg-password">كلمة المرور</label>
                    <input type="password" id="reg-password" placeholder="اختر كلمة مرور قوية">
                </div>
                <div class="form-group">
                    <label for="reg-confirm-password">تأكيد كلمة المرور</label>
                    <input type="password" id="reg-confirm-password" placeholder="أعد إدخال كلمة المرور">
                </div>
                <div class="form-group">
                    <label for="reg-role">نوع الحساب</label>
                    <select id="reg-role">
                        <option value="user">مستخدم عادي</option>
                        <option value="analyst">محلل أمني</option>
                        <option value="admin">مدير النظام</option>
                    </select>
                </div>
                <button class="btn" onclick="register()">إنشاء الحساب</button>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="dashboard">
            <!-- User Info -->
            <div class="user-info">
                <div>
                    <span>مرحباً، </span>
                    <span id="user-name" style="color: #00ff41; font-weight: bold;">المستخدم</span>
                    <span> | </span>
                    <span id="user-role" style="color: #00ccff;">دور المستخدم</span>
                </div>
                <div>
                    <span>آخر دخول: </span>
                    <span id="last-login" style="color: #cccccc;">الآن</span>
                </div>
                <button class="btn btn-secondary" onclick="logout()" style="width: auto; padding: 8px 16px;">تسجيل خروج</button>
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-indicator"></div>
                    <span>النظام نشط</span>
                </div>
                <div class="status-item">
                    <span>مستوى التهديد: </span>
                    <span id="threat-level" style="color: #00ff41;">منخفض</span>
                </div>
                <div class="status-item">
                    <span>الاتصالات النشطة: </span>
                    <span id="active-connections" style="color: #00ccff;">15</span>
                </div>
                <div class="status-item">
                    <span>آخر فحص: </span>
                    <span id="last-scan">الآن</span>
                </div>
                <div class="status-item">
                    <span>المستخدمين المتصلين: </span>
                    <span id="online-users" style="color: #00ff41;">3</span>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="features-grid">
                <div class="feature-card" onclick="showFeature('ai')" id="ai-card">
                    <span class="feature-icon">🤖</span>
                    <div class="feature-title">الذكاء الاصطناعي</div>
                    <div class="feature-description">
                        نظام متقدم للكشف عن التهديدات باستخدام الذكاء الاصطناعي والتعلم الآلي
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('3d')" id="3d-card">
                    <span class="feature-icon">🎮</span>
                    <div class="feature-title">التصور ثلاثي الأبعاد</div>
                    <div class="feature-description">
                        خريطة شبكة تفاعلية ثلاثية الأبعاد مع تأثيرات بصرية متقدمة
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('honeypot')" id="honeypot-card">
                    <span class="feature-icon">🍯</span>
                    <div class="feature-title">نظام Honeypot</div>
                    <div class="feature-description">
                        فخاخ ذكية لجذب المهاجمين وتحليل تكتيكاتهم
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('monitoring')" id="monitoring-card">
                    <span class="feature-icon">📊</span>
                    <div class="feature-title">المراقبة المباشرة</div>
                    <div class="feature-description">
                        مراقبة الشبكة والنظام في الوقت الفعلي مع تنبيهات ذكية
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('audio')" id="audio-card">
                    <span class="feature-icon">🔊</span>
                    <div class="feature-title">النظام الصوتي</div>
                    <div class="feature-description">
                        موسيقى ديناميكية وتأثيرات صوتية تفاعلية
                    </div>
                </div>
                <div class="feature-card" onclick="showFeature('tools')" id="tools-card">
                    <span class="feature-icon">🛠️</span>
                    <div class="feature-title">أدوات الفحص</div>
                    <div class="feature-description">
                        مجموعة شاملة من أدوات فحص الأمان واختبار الاختراق
                    </div>
                </div>
            </div>

            <!-- Feature Panels -->
            <!-- AI Panel -->
            <div id="ai-panel" class="feature-panel">
                <h3 style="color: #00ff41; margin-bottom: 1rem;">🤖 نظام الذكاء الاصطناعي للكشف عن التهديدات</h3>
                <div class="chart-container">
                    <h4>تحليل التهديدات في الوقت الفعلي</h4>
                    <div style="display: flex; justify-content: space-between; margin: 1rem 0;">
                        <div>دقة الكشف: <span style="color: #00ff41;">97.3%</span></div>
                        <div>التهديدات المكتشفة: <span style="color: #ff0040;">23</span></div>
                        <div>التهديدات المحجوبة: <span style="color: #00ff41;">21</span></div>
                    </div>
                    <div class="terminal">
                        <div class="terminal-line">
                            <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                            <span class="terminal-output">تحميل نموذج الذكاء الاصطناعي...</span>
                        </div>
                        <div class="terminal-line">
                            <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                            <span class="terminal-success">تم تحميل النموذج بنجاح - دقة 97.3%</span>
                        </div>
                        <div class="terminal-line">
                            <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                            <span class="terminal-warning">تحذير: نشاط مشبوه من IP *************</span>
                        </div>
                        <div class="terminal-line">
                            <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                            <span class="terminal-output">تحليل السلوك: محاولة فحص منافذ</span>
                        </div>
                        <div class="terminal-line">
                            <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                            <span class="terminal-success">تم حجب التهديد تلقائياً</span>
                        </div>
                    </div>
                    <button class="btn" onclick="runAIScan()">تشغيل فحص ذكي</button>
                </div>
            </div>

            <!-- 3D Panel -->
            <div id="3d-panel" class="feature-panel">
                <h3 style="color: #00ff41; margin-bottom: 1rem;">🎮 التصور ثلاثي الأبعاد للشبكة</h3>
                <div class="network-canvas" id="network-3d">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🌐</div>
                        <div>خريطة الشبكة ثلاثية الأبعاد</div>
                        <div style="margin: 1rem 0;">
                            <span class="loading"></span>
                            <span style="margin-left: 10px;">جاري تحميل البيانات...</span>
                        </div>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div class="chart-container">
                        <h4>عقد الشبكة</h4>
                        <div style="font-size: 2rem; color: #00ff41;">47</div>
                    </div>
                    <div class="chart-container">
                        <h4>الاتصالات النشطة</h4>
                        <div style="font-size: 2rem; color: #00ccff;">15</div>
                    </div>
                    <div class="chart-container">
                        <h4>التهديدات المكتشفة</h4>
                        <div style="font-size: 2rem; color: #ff0040;">3</div>
                    </div>
                </div>
                <button class="btn" onclick="refresh3D()">تحديث الخريطة</button>
            </div>

            <!-- Honeypot Panel -->
            <div id="honeypot-panel" class="feature-panel">
                <h3 style="color: #00ff41; margin-bottom: 1rem;">🍯 نظام Honeypot الذكي</h3>
                <div class="tools-grid">
                    <div class="tool-card" onclick="toggleHoneypot('ssh')">
                        <h4>SSH Honeypot</h4>
                        <div>الحالة: <span id="ssh-status" style="color: #00ff41;">نشط</span></div>
                        <div>المحاولات: <span style="color: #ff0040;">12</span></div>
                    </div>
                    <div class="tool-card" onclick="toggleHoneypot('http')">
                        <h4>HTTP Honeypot</h4>
                        <div>الحالة: <span id="http-status" style="color: #00ff41;">نشط</span></div>
                        <div>المحاولات: <span style="color: #ff0040;">8</span></div>
                    </div>
                    <div class="tool-card" onclick="toggleHoneypot('ftp')">
                        <h4>FTP Honeypot</h4>
                        <div>الحالة: <span id="ftp-status" style="color: #ffaa00;">متوقف</span></div>
                        <div>المحاولات: <span style="color: #ff0040;">0</span></div>
                    </div>
                    <div class="tool-card" onclick="toggleHoneypot('database')">
                        <h4>Database Honeypot</h4>
                        <div>الحالة: <span id="db-status" style="color: #00ff41;">نشط</span></div>
                        <div>المحاولات: <span style="color: #ff0040;">5</span></div>
                    </div>
                </div>
                <div class="terminal">
                    <div class="terminal-line">
                        <span class="terminal-prompt">honeypot@cyber-sentinel:~$</span>
                        <span class="terminal-output">SSH Honeypot: محاولة دخول من ************</span>
                    </div>
                    <div class="terminal-line">
                        <span class="terminal-prompt">honeypot@cyber-sentinel:~$</span>
                        <span class="terminal-warning">كلمة مرور مستخدمة: admin/123456</span>
                    </div>
                    <div class="terminal-line">
                        <span class="terminal-prompt">honeypot@cyber-sentinel:~$</span>
                        <span class="terminal-output">HTTP Honeypot: فحص ثغرات من 198.51.100.15</span>
                    </div>
                    <div class="terminal-line">
                        <span class="terminal-prompt">honeypot@cyber-sentinel:~$</span>
                        <span class="terminal-success">تم تسجيل جميع الأنشطة المشبوهة</span>
                    </div>
                </div>
            </div>

            <!-- Monitoring Panel -->
            <div id="monitoring-panel" class="feature-panel">
                <h3 style="color: #00ff41; margin-bottom: 1rem;">📊 المراقبة المباشرة للشبكة</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                    <div class="chart-container">
                        <h4>استخدام المعالج</h4>
                        <div style="font-size: 2rem; color: #00ff41;">23%</div>
                        <div style="background: rgba(0, 255, 65, 0.2); height: 10px; border-radius: 5px; margin-top: 10px;">
                            <div style="background: #00ff41; height: 100%; width: 23%; border-radius: 5px;"></div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>استخدام الذاكرة</h4>
                        <div style="font-size: 2rem; color: #00ccff;">67%</div>
                        <div style="background: rgba(0, 204, 255, 0.2); height: 10px; border-radius: 5px; margin-top: 10px;">
                            <div style="background: #00ccff; height: 100%; width: 67%; border-radius: 5px;"></div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>حركة الشبكة</h4>
                        <div style="font-size: 2rem; color: #ffaa00;">1.2 GB/s</div>
                        <div style="background: rgba(255, 170, 0, 0.2); height: 10px; border-radius: 5px; margin-top: 10px;">
                            <div style="background: #ffaa00; height: 100%; width: 45%; border-radius: 5px;"></div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <h4>مساحة القرص</h4>
                        <div style="font-size: 2rem; color: #ff0040;">89%</div>
                        <div style="background: rgba(255, 0, 64, 0.2); height: 10px; border-radius: 5px; margin-top: 10px;">
                            <div style="background: #ff0040; height: 100%; width: 89%; border-radius: 5px;"></div>
                        </div>
                    </div>
                </div>
                <div class="terminal">
                    <div class="terminal-line">
                        <span class="terminal-prompt">monitor@cyber-sentinel:~$</span>
                        <span class="terminal-output">مراقبة الشبكة نشطة - 47 جهاز متصل</span>
                    </div>
                    <div class="terminal-line">
                        <span class="terminal-prompt">monitor@cyber-sentinel:~$</span>
                        <span class="terminal-warning">تحذير: استخدام عالي للذاكرة على الخادم الرئيسي</span>
                    </div>
                    <div class="terminal-line">
                        <span class="terminal-prompt">monitor@cyber-sentinel:~$</span>
                        <span class="terminal-output">حركة بيانات غير عادية من 10.0.0.15</span>
                    </div>
                </div>
                <button class="btn" onclick="generateReport()">إنشاء تقرير مفصل</button>
            </div>

            <!-- Audio Panel -->
            <div id="audio-panel" class="feature-panel">
                <h3 style="color: #00ff41; margin-bottom: 1rem;">🔊 النظام الصوتي التفاعلي</h3>
                <div class="tools-grid">
                    <div class="tool-card" onclick="playSound('alert')">
                        <h4>🚨 تنبيه أمني</h4>
                        <div>صوت تحذير للتهديدات العالية</div>
                    </div>
                    <div class="tool-card" onclick="playSound('scan')">
                        <h4>🔍 صوت الفحص</h4>
                        <div>تأثير صوتي لعمليات الفحص</div>
                    </div>
                    <div class="tool-card" onclick="playSound('success')">
                        <h4>✅ نجاح العملية</h4>
                        <div>صوت تأكيد للعمليات الناجحة</div>
                    </div>
                    <div class="tool-card" onclick="playSound('background')">
                        <h4>🎵 موسيقى الخلفية</h4>
                        <div>موسيقى ديناميكية للواجهة</div>
                    </div>
                </div>
                <div class="chart-container">
                    <h4>إعدادات الصوت</h4>
                    <div style="margin: 1rem 0;">
                        <label>مستوى الصوت: </label>
                        <input type="range" id="volume-slider" min="0" max="100" value="50" style="width: 200px;">
                        <span id="volume-display">50%</span>
                    </div>
                    <div style="margin: 1rem 0;">
                        <label>
                            <input type="checkbox" id="sound-enabled" checked>
                            تفعيل الأصوات
                        </label>
                    </div>
                </div>
            </div>

            <!-- Tools Panel -->
            <div id="tools-panel" class="feature-panel">
                <h3 style="color: #00ff41; margin-bottom: 1rem;">🛠️ أدوات فحص الأمان</h3>
                <div class="tools-grid">
                    <div class="tool-card" onclick="runTool('nmap')">
                        <h4>🗺️ Nmap Scanner</h4>
                        <div>فحص المنافذ والخدمات</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" style="width: 100%; padding: 8px;">تشغيل</button>
                        </div>
                    </div>
                    <div class="tool-card" onclick="runTool('vulnerability')">
                        <h4>🔍 Vulnerability Scanner</h4>
                        <div>فحص الثغرات الأمنية</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" style="width: 100%; padding: 8px;">تشغيل</button>
                        </div>
                    </div>
                    <div class="tool-card" onclick="runTool('penetration')">
                        <h4>⚔️ Penetration Testing</h4>
                        <div>اختبار اختراق شامل</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" style="width: 100%; padding: 8px;">تشغيل</button>
                        </div>
                    </div>
                    <div class="tool-card" onclick="runTool('forensics')">
                        <h4>🔬 Digital Forensics</h4>
                        <div>تحليل الأدلة الرقمية</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" style="width: 100%; padding: 8px;">تشغيل</button>
                        </div>
                    </div>
                    <div class="tool-card" onclick="runTool('malware')">
                        <h4>🦠 Malware Analysis</h4>
                        <div>تحليل البرمجيات الخبيثة</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" style="width: 100%; padding: 8px;">تشغيل</button>
                        </div>
                    </div>
                    <div class="tool-card" onclick="runTool('encryption')">
                        <h4>🔐 Encryption Tools</h4>
                        <div>أدوات التشفير والحماية</div>
                        <div style="margin-top: 10px;">
                            <button class="btn" style="width: 100%; padding: 8px;">تشغيل</button>
                        </div>
                    </div>
                </div>
                <div class="terminal" id="tools-terminal">
                    <div class="terminal-line">
                        <span class="terminal-prompt">tools@cyber-sentinel:~$</span>
                        <span class="terminal-output">جميع أدوات الفحص جاهزة للاستخدام</span>
                    </div>
                    <div class="terminal-line">
                        <span class="terminal-prompt">tools@cyber-sentinel:~$</span>
                        <span class="terminal-output">اختر أداة من القائمة أعلاه لبدء الفحص</span>
                    </div>
                </div>
            </div>

            <!-- Main Terminal -->
            <div class="terminal">
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span>
                    <span class="terminal-output">نظام الحماية السيبرانية نشط</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span>
                    <span class="terminal-success">تم تحميل جميع الوحدات بنجاح</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span>
                    <span class="terminal-warning">تحذير: نشاط مشبوه مكتشف من IP: *************</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span>
                    <span class="terminal-output">تم حجب محاولة اختراق</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span>
                    <span class="terminal-output">Honeypot SSH: محاولة دخول من ************</span>
                </div>
                <div class="terminal-line">
                    <span class="terminal-prompt">cyber-sentinel@secops:~$</span>
                    <span class="terminal-success">النظام يعمل بكفاءة عالية</span>
                </div>
            </div>
        </div>

        <!-- Warning -->
        <div class="alert">
            <h3>⚠️ تحذير قانوني مهم</h3>
            <p>
                هذا البرنامج مخصص لاختبار الأمان المصرح به والأغراض التعليمية فقط.
                <br>
                الاستخدام غير المصرح به ضد أنظمة لا تملكها غير قانوني ويخالف القوانين المحلية والدولية.
                <br>
                المطورون غير مسؤولين عن أي استخدام غير قانوني لهذا البرنامج.
            </p>
        </div>
    </div>

    <script>
        // Global Variables
        let currentUser = null;
        let users = JSON.parse(localStorage.getItem('cyberSentinelUsers')) || {
            'admin': {
                password: 'JaMaL@123',
                fullname: 'مدير النظام',
                email: '<EMAIL>',
                role: 'admin',
                lastLogin: new Date().toISOString()
            }
        };
        let currentFeature = null;

        // Matrix Background Effect
        function initMatrix() {
            const canvas = document.getElementById('matrix-canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
            const matrixArray = matrix.split("");

            const fontSize = 10;
            const columns = canvas.width / fontSize;
            const drops = [];

            for(let x = 0; x < columns; x++) {
                drops[x] = 1;
            }

            function draw() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#00ff41';
                ctx.font = fontSize + 'px monospace';

                for(let i = 0; i < drops.length; i++) {
                    const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                    if(drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        drops[i] = 0;
                    }
                    drops[i]++;
                }
            }

            setInterval(draw, 35);
        }

        // Authentication Functions
        function switchTab(tab) {
            const loginTab = document.querySelector('.auth-tab:first-child');
            const registerTab = document.querySelector('.auth-tab:last-child');
            const loginForm = document.getElementById('login-form');
            const registerForm = document.getElementById('register-form');

            if (tab === 'login') {
                loginTab.classList.add('active');
                registerTab.classList.remove('active');
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
            } else {
                loginTab.classList.remove('active');
                registerTab.classList.add('active');
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
            }
        }

        function login() {
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;

            if (users[username] && users[username].password === password) {
                currentUser = users[username];
                currentUser.username = username;
                currentUser.lastLogin = new Date().toISOString();

                // Save updated user data
                localStorage.setItem('cyberSentinelUsers', JSON.stringify(users));

                // Show dashboard
                document.getElementById('auth-section').style.display = 'none';
                document.getElementById('dashboard-section').style.display = 'block';

                // Update user info
                document.getElementById('user-name').textContent = currentUser.fullname || username;
                document.getElementById('user-role').textContent = getRoleDisplayName(currentUser.role);
                document.getElementById('last-login').textContent = new Date(currentUser.lastLogin).toLocaleString('ar-SA');

                // Start real-time updates
                startRealTimeUpdates();

                // Play success sound
                playSystemSound('success');

                // Show welcome message
                setTimeout(() => {
                    alert(`🎉 مرحباً بك ${currentUser.fullname || username}!\n\nتم تسجيل الدخول بنجاح إلى Cyber Sentinel Pro`);
                }, 500);

            } else {
                alert('❌ اسم المستخدم أو كلمة المرور غير صحيحة');
                playSystemSound('error');
            }
        }

        function register() {
            const fullname = document.getElementById('reg-fullname').value;
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('reg-confirm-password').value;
            const role = document.getElementById('reg-role').value;

            // Validation
            if (!fullname || !username || !email || !password || !confirmPassword) {
                alert('❌ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (password !== confirmPassword) {
                alert('❌ كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }

            if (password.length < 6) {
                alert('❌ كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            if (users[username]) {
                alert('❌ اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر');
                return;
            }

            // Create new user
            users[username] = {
                password: password,
                fullname: fullname,
                email: email,
                role: role,
                lastLogin: new Date().toISOString(),
                createdAt: new Date().toISOString()
            };

            // Save to localStorage
            localStorage.setItem('cyberSentinelUsers', JSON.stringify(users));

            // Success message
            alert(`✅ تم إنشاء الحساب بنجاح!\n\nاسم المستخدم: ${username}\nالاسم: ${fullname}\nالدور: ${getRoleDisplayName(role)}\n\nيمكنك الآن تسجيل الدخول`);

            // Switch to login tab
            switchTab('login');

            // Fill login form
            document.getElementById('login-username').value = username;
            document.getElementById('login-password').value = '';

            playSystemSound('success');
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                currentUser = null;
                currentFeature = null;

                // Hide dashboard and show auth
                document.getElementById('dashboard-section').style.display = 'none';
                document.getElementById('auth-section').style.display = 'block';

                // Reset forms
                document.getElementById('login-username').value = 'admin';
                document.getElementById('login-password').value = 'JaMaL@123';
                switchTab('login');

                // Hide all feature panels
                hideAllFeaturePanels();

                playSystemSound('logout');

                alert('✅ تم تسجيل الخروج بنجاح');
            }
        }

        function getRoleDisplayName(role) {
            const roles = {
                'user': 'مستخدم عادي',
                'analyst': 'محلل أمني',
                'admin': 'مدير النظام'
            };
            return roles[role] || role;
        }

        // Feature Functions
        function showFeature(feature) {
            // Remove active class from all cards
            document.querySelectorAll('.feature-card').forEach(card => {
                card.classList.remove('active');
            });

            // Hide all panels
            hideAllFeaturePanels();

            // Show selected feature
            const card = document.getElementById(feature + '-card');
            const panel = document.getElementById(feature + '-panel');

            if (card && panel) {
                card.classList.add('active');
                panel.classList.add('active');
                currentFeature = feature;

                // Scroll to panel
                panel.scrollIntoView({ behavior: 'smooth' });

                playSystemSound('click');
            }
        }

        function hideAllFeaturePanels() {
            document.querySelectorAll('.feature-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            document.querySelectorAll('.feature-card').forEach(card => {
                card.classList.remove('active');
            });
        }

        // AI Functions
        function runAIScan() {
            const terminal = document.querySelector('#ai-panel .terminal');
            const newLine = document.createElement('div');
            newLine.className = 'terminal-line';
            newLine.innerHTML = `
                <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                <span class="terminal-output">بدء فحص ذكي شامل...</span>
            `;
            terminal.appendChild(newLine);

            setTimeout(() => {
                const scanResult = document.createElement('div');
                scanResult.className = 'terminal-line';
                scanResult.innerHTML = `
                    <span class="terminal-prompt">AI-Engine@cyber-sentinel:~$</span>
                    <span class="terminal-success">تم اكتشاف ${Math.floor(Math.random() * 5) + 1} تهديدات جديدة وحجبها</span>
                `;
                terminal.appendChild(scanResult);
                terminal.scrollTop = terminal.scrollHeight;

                playSystemSound('scan');
                alert('✅ تم إكمال الفحص الذكي بنجاح!\n\nتم اكتشاف وحجب عدة تهديدات جديدة.');
            }, 2000);
        }

        // 3D Functions
        function refresh3D() {
            const canvas = document.getElementById('network-3d');
            const loadingDiv = canvas.querySelector('div');

            loadingDiv.innerHTML = `
                <div style="font-size: 4rem; margin-bottom: 1rem;">🔄</div>
                <div>جاري تحديث خريطة الشبكة...</div>
                <div style="margin: 1rem 0;">
                    <span class="loading"></span>
                    <span style="margin-left: 10px;">يرجى الانتظار...</span>
                </div>
            `;

            setTimeout(() => {
                loadingDiv.innerHTML = `
                    <div style="font-size: 4rem; margin-bottom: 1rem;">🌐</div>
                    <div>تم تحديث خريطة الشبكة بنجاح</div>
                    <div style="margin: 1rem 0; color: #00ff41;">
                        ✅ تم اكتشاف ${Math.floor(Math.random() * 10) + 40} عقدة جديدة
                    </div>
                `;

                playSystemSound('success');
            }, 3000);
        }

        // Honeypot Functions
        function toggleHoneypot(type) {
            const statusElement = document.getElementById(type + '-status');
            const currentStatus = statusElement.textContent;

            if (currentStatus === 'نشط') {
                statusElement.textContent = 'متوقف';
                statusElement.style.color = '#ffaa00';
                alert(`⚠️ تم إيقاف ${type.toUpperCase()} Honeypot`);
            } else {
                statusElement.textContent = 'نشط';
                statusElement.style.color = '#00ff41';
                alert(`✅ تم تشغيل ${type.toUpperCase()} Honeypot`);
            }

            playSystemSound('click');
        }

        // Monitoring Functions
        function generateReport() {
            alert('📊 جاري إنشاء التقرير المفصل...\n\nسيتم حفظ التقرير في مجلد التقارير خلال دقائق قليلة.');

            setTimeout(() => {
                const reportData = `
🛡️ CYBER SENTINEL PRO - تقرير الأمان المفصل
===============================================

📅 تاريخ التقرير: ${new Date().toLocaleString('ar-SA')}
👤 المستخدم: ${currentUser?.fullname || 'غير محدد'}

📊 إحصائيات النظام:
- استخدام المعالج: 23%
- استخدام الذاكرة: 67%
- حركة الشبكة: 1.2 GB/s
- مساحة القرص: 89%

🔍 التهديدات المكتشفة: 23
✅ التهديدات المحجوبة: 21
⚠️ التهديدات النشطة: 2

🍯 إحصائيات Honeypot:
- SSH: 12 محاولة
- HTTP: 8 محاولات
- Database: 5 محاولات

🌐 عقد الشبكة: 47
🔗 الاتصالات النشطة: 15

===============================================
© 2024 Cyber Sentinel Pro - SecOps Edition
                `;

                // Create and download report
                const blob = new Blob([reportData], { type: 'text/plain;charset=utf-8' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `cyber-sentinel-report-${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                alert('✅ تم إنشاء وتحميل التقرير بنجاح!');
                playSystemSound('success');
            }, 2000);
        }

        // Audio Functions
        function playSound(type) {
            const sounds = {
                'alert': '🚨 تم تشغيل صوت التنبيه الأمني',
                'scan': '🔍 تم تشغيل صوت الفحص',
                'success': '✅ تم تشغيل صوت النجاح',
                'background': '🎵 تم تشغيل الموسيقى الخلفية'
            };

            alert(sounds[type] || 'تم تشغيل الصوت');
            playSystemSound(type);
        }

        function playSystemSound(type) {
            if (!document.getElementById('sound-enabled')?.checked) return;

            // Create audio context for sound effects
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                const volume = (document.getElementById('volume-slider')?.value || 50) / 100;
                gainNode.gain.setValueAtTime(volume * 0.1, audioContext.currentTime);

                switch(type) {
                    case 'success':
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
                        break;
                    case 'error':
                        oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
                        oscillator.frequency.setValueAtTime(200, audioContext.currentTime + 0.1);
                        break;
                    case 'scan':
                        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                        break;
                    case 'click':
                        oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
                        break;
                    default:
                        oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                }

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                console.log('Audio not supported');
            }
        }

        // Tools Functions
        function runTool(tool) {
            const terminal = document.getElementById('tools-terminal');
            const tools = {
                'nmap': {
                    name: 'Nmap Scanner',
                    description: 'فحص المنافذ والخدمات',
                    command: 'nmap -sS -O target_ip',
                    result: 'تم اكتشاف 15 منفذ مفتوح'
                },
                'vulnerability': {
                    name: 'Vulnerability Scanner',
                    description: 'فحص الثغرات الأمنية',
                    command: 'vulnscan --deep target_ip',
                    result: 'تم اكتشاف 3 ثغرات عالية الخطورة'
                },
                'penetration': {
                    name: 'Penetration Testing',
                    description: 'اختبار اختراق شامل',
                    command: 'pentest --full target_ip',
                    result: 'تم اختراق 2 من 10 خدمات'
                },
                'forensics': {
                    name: 'Digital Forensics',
                    description: 'تحليل الأدلة الرقمية',
                    command: 'forensics --analyze evidence.img',
                    result: 'تم استخراج 47 ملف مشبوه'
                },
                'malware': {
                    name: 'Malware Analysis',
                    description: 'تحليل البرمجيات الخبيثة',
                    command: 'malware-analyzer sample.exe',
                    result: 'تم اكتشاف Trojan.Win32.Generic'
                },
                'encryption': {
                    name: 'Encryption Tools',
                    description: 'أدوات التشفير والحماية',
                    command: 'encrypt --aes256 file.txt',
                    result: 'تم تشفير الملف بنجاح'
                }
            };

            const selectedTool = tools[tool];
            if (!selectedTool) return;

            // Add command to terminal
            const commandLine = document.createElement('div');
            commandLine.className = 'terminal-line';
            commandLine.innerHTML = `
                <span class="terminal-prompt">tools@cyber-sentinel:~$</span>
                <span class="terminal-output">${selectedTool.command}</span>
            `;
            terminal.appendChild(commandLine);

            // Add loading
            const loadingLine = document.createElement('div');
            loadingLine.className = 'terminal-line';
            loadingLine.innerHTML = `
                <span class="terminal-prompt">tools@cyber-sentinel:~$</span>
                <span class="terminal-warning">جاري تشغيل ${selectedTool.name}...</span>
            `;
            terminal.appendChild(loadingLine);

            terminal.scrollTop = terminal.scrollHeight;

            // Show result after delay
            setTimeout(() => {
                const resultLine = document.createElement('div');
                resultLine.className = 'terminal-line';
                resultLine.innerHTML = `
                    <span class="terminal-prompt">tools@cyber-sentinel:~$</span>
                    <span class="terminal-success">${selectedTool.result}</span>
                `;
                terminal.appendChild(resultLine);
                terminal.scrollTop = terminal.scrollHeight;

                playSystemSound('success');
                alert(`✅ تم إكمال ${selectedTool.name} بنجاح!\n\n${selectedTool.result}`);
            }, 3000);

            playSystemSound('scan');
        }

        // Real-time Updates
        function startRealTimeUpdates() {
            // Update last scan time
            setInterval(() => {
                const now = new Date();
                document.getElementById('last-scan').textContent = now.toLocaleTimeString('ar-SA');
            }, 5000);

            // Update threat level randomly
            setInterval(() => {
                const levels = ['منخفض', 'متوسط', 'عالي'];
                const colors = ['#00ff41', '#ffaa00', '#ff0040'];
                const randomLevel = Math.floor(Math.random() * levels.length);

                const threatElement = document.getElementById('threat-level');
                threatElement.textContent = levels[randomLevel];
                threatElement.style.color = colors[randomLevel];
            }, 30000);

            // Update active connections
            setInterval(() => {
                const connections = Math.floor(Math.random() * 20) + 10;
                document.getElementById('active-connections').textContent = connections;
            }, 15000);

            // Update online users
            setInterval(() => {
                const users = Math.floor(Math.random() * 10) + 1;
                document.getElementById('online-users').textContent = users;
            }, 20000);
        }

        // Volume Control
        document.addEventListener('DOMContentLoaded', function() {
            const volumeSlider = document.getElementById('volume-slider');
            const volumeDisplay = document.getElementById('volume-display');

            if (volumeSlider && volumeDisplay) {
                volumeSlider.addEventListener('input', function() {
                    volumeDisplay.textContent = this.value + '%';
                });
            }
        });

        // Keyboard Shortcuts
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const authSection = document.getElementById('auth-section');
                if (authSection.style.display !== 'none') {
                    const loginForm = document.getElementById('login-form');
                    const registerForm = document.getElementById('register-form');

                    if (loginForm.style.display !== 'none') {
                        login();
                    } else if (registerForm.style.display !== 'none') {
                        register();
                    }
                }
            }
        });

        // Initialize on page load
        window.onload = function() {
            initMatrix();

            // Check if user is already logged in
            const savedUser = localStorage.getItem('currentCyberSentinelUser');
            if (savedUser) {
                currentUser = JSON.parse(savedUser);
                // Auto-login logic can be added here if needed
            }
        };

        // Handle window resize for matrix
        window.onresize = function() {
            const canvas = document.getElementById('matrix-canvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        };

        // Save current user on page unload
        window.onbeforeunload = function() {
            if (currentUser) {
                localStorage.setItem('currentCyberSentinelUser', JSON.stringify(currentUser));
            }
        };
    </script>
</body>
</html>
