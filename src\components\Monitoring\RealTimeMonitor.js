import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Switch,
  FormControlLabel,
  Chip,
  Alert,
  LinearProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Timeline,
  NetworkCheck,
  Security,
  Speed,
  Memory,
  Storage,
  Pause,
  PlayArrow,
  Refresh,
  Fullscreen
} from '@mui/icons-material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';

// تسجيل مكونات Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

const RealTimeMonitor = () => {
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [networkData, setNetworkData] = useState([]);
  const [systemMetrics, setSystemMetrics] = useState({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 0
  });
  const [threatLevel, setThreatLevel] = useState('low');
  const [activeConnections, setActiveConnections] = useState(0);
  const [packetsPerSecond, setPacketsPerSecond] = useState(0);
  const [alerts, setAlerts] = useState([]);
  
  const intervalRef = useRef(null);
  const maxDataPoints = 50;

  // إعدادات الرسم البياني
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 0 // إلغاء الرسوم المتحركة للأداء
    },
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: '#ffffff',
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(26, 26, 46, 0.9)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#00ff41',
        borderWidth: 1
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          color: 'rgba(0, 255, 65, 0.1)'
        },
        ticks: {
          color: '#cccccc',
          maxTicksLimit: 10
        }
      },
      y: {
        display: true,
        grid: {
          color: 'rgba(0, 255, 65, 0.1)'
        },
        ticks: {
          color: '#cccccc'
        },
        min: 0,
        max: 100
      }
    }
  };

  // بيانات الرسم البياني لحركة الشبكة
  const networkChartData = {
    labels: networkData.map((_, index) => {
      const time = new Date(Date.now() - (maxDataPoints - index) * 1000);
      return time.toLocaleTimeString('ar-SA', { 
        hour12: false,
        minute: '2-digit',
        second: '2-digit'
      });
    }),
    datasets: [
      {
        label: 'استخدام النطاق الترددي (%)',
        data: networkData.map(d => d.bandwidth),
        borderColor: '#00ff41',
        backgroundColor: 'rgba(0, 255, 65, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4
      },
      {
        label: 'الحزم في الثانية',
        data: networkData.map(d => d.packets),
        borderColor: '#00ccff',
        backgroundColor: 'rgba(0, 204, 255, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4
      },
      {
        label: 'مستوى التهديد',
        data: networkData.map(d => d.threat),
        borderColor: '#ff0040',
        backgroundColor: 'rgba(255, 0, 64, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 4
      }
    ]
  };

  // بيانات الرسم البياني لموارد النظام
  const systemChartData = {
    labels: networkData.map((_, index) => {
      const time = new Date(Date.now() - (maxDataPoints - index) * 1000);
      return time.toLocaleTimeString('ar-SA', { 
        hour12: false,
        minute: '2-digit',
        second: '2-digit'
      });
    }),
    datasets: [
      {
        label: 'المعالج (%)',
        data: networkData.map(d => d.cpu),
        borderColor: '#ffaa00',
        backgroundColor: 'rgba(255, 170, 0, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 0
      },
      {
        label: 'الذاكرة (%)',
        data: networkData.map(d => d.memory),
        borderColor: '#ff6b35',
        backgroundColor: 'rgba(255, 107, 53, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 0
      },
      {
        label: 'التخزين (%)',
        data: networkData.map(d => d.disk),
        borderColor: '#9c27b0',
        backgroundColor: 'rgba(156, 39, 176, 0.1)',
        fill: true,
        tension: 0.4,
        pointRadius: 0
      }
    ]
  };

  // توليد بيانات محاكاة
  const generateMockData = () => {
    const now = Date.now();
    
    // محاكاة بيانات الشبكة
    const bandwidth = Math.random() * 80 + 10;
    const packets = Math.random() * 60 + 20;
    const threat = Math.random() * 30 + (threatLevel === 'high' ? 50 : threatLevel === 'medium' ? 20 : 5);
    
    // محاكاة موارد النظام
    const cpu = Math.random() * 70 + 15;
    const memory = Math.random() * 60 + 25;
    const disk = Math.random() * 40 + 30;
    
    const newDataPoint = {
      timestamp: now,
      bandwidth,
      packets,
      threat,
      cpu,
      memory,
      disk
    };

    setNetworkData(prev => {
      const updated = [...prev, newDataPoint];
      return updated.slice(-maxDataPoints);
    });

    // تحديث المقاييس الحالية
    setSystemMetrics({ cpu, memory, disk, network: bandwidth });
    setPacketsPerSecond(Math.round(packets * 100));
    setActiveConnections(Math.round(Math.random() * 50 + 10));

    // تحديد مستوى التهديد
    if (threat > 70) {
      setThreatLevel('critical');
    } else if (threat > 50) {
      setThreatLevel('high');
    } else if (threat > 30) {
      setThreatLevel('medium');
    } else {
      setThreatLevel('low');
    }

    // إضافة تنبيهات عشوائية
    if (Math.random() < 0.05) { // 5% احتمال
      const alertTypes = [
        'محاولة وصول مشبوهة من IP: *************',
        'استخدام عالي للنطاق الترددي',
        'فحص منافذ مكتشف',
        'نشاط غير طبيعي في الشبكة'
      ];
      
      const newAlert = {
        id: Date.now(),
        message: alertTypes[Math.floor(Math.random() * alertTypes.length)],
        severity: threat > 50 ? 'error' : threat > 30 ? 'warning' : 'info',
        timestamp: new Date()
      };

      setAlerts(prev => [newAlert, ...prev.slice(0, 4)]);
    }
  };

  // بدء/إيقاف المراقبة
  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
  };

  // إعادة تعيين البيانات
  const resetData = () => {
    setNetworkData([]);
    setAlerts([]);
  };

  // تأثيرات جانبية
  useEffect(() => {
    if (isMonitoring) {
      intervalRef.current = setInterval(generateMockData, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isMonitoring, threatLevel]);

  // لون مستوى التهديد
  const getThreatColor = () => {
    switch (threatLevel) {
      case 'critical': return '#ff0040';
      case 'high': return '#ff6600';
      case 'medium': return '#ffaa00';
      case 'low': return '#00ff41';
      default: return '#cccccc';
    }
  };

  // نص مستوى التهديد
  const getThreatText = () => {
    switch (threatLevel) {
      case 'critical': return 'حرج';
      case 'high': return 'عالي';
      case 'medium': return 'متوسط';
      case 'low': return 'منخفض';
      default: return 'غير معروف';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* العنوان وأدوات التحكم */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #00ff41, #00ccff)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
        >
          مراقبة الشبكة المباشرة
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Switch
                checked={isMonitoring}
                onChange={toggleMonitoring}
                color="primary"
              />
            }
            label="المراقبة النشطة"
          />
          
          <Tooltip title="إعادة تعيين البيانات">
            <IconButton onClick={resetData}>
              <Refresh />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* مؤشرات الحالة السريعة */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6} sm={3}>
          <Card sx={{ backgroundColor: 'rgba(0, 255, 65, 0.1)', border: '1px solid rgba(0, 255, 65, 0.3)' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <NetworkCheck sx={{ fontSize: 40, color: '#00ff41', mb: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {activeConnections}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                اتصالات نشطة
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Card sx={{ backgroundColor: 'rgba(0, 204, 255, 0.1)', border: '1px solid rgba(0, 204, 255, 0.3)' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Speed sx={{ fontSize: 40, color: '#00ccff', mb: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {packetsPerSecond}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                حزمة/ثانية
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Card sx={{ backgroundColor: `${getThreatColor()}20`, border: `1px solid ${getThreatColor()}50` }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Security sx={{ fontSize: 40, color: getThreatColor(), mb: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {getThreatText()}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                مستوى التهديد
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Card sx={{ backgroundColor: 'rgba(255, 170, 0, 0.1)', border: '1px solid rgba(255, 170, 0, 0.3)' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Memory sx={{ fontSize: 40, color: '#ffaa00', mb: 1 }} />
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {Math.round(systemMetrics.cpu)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                استخدام المعالج
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* الرسوم البيانية */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* رسم بياني لحركة الشبكة */}
        <Grid item xs={12} lg={8}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                حركة الشبكة المباشرة
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line data={networkChartData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* التنبيهات الأخيرة */}
        <Grid item xs={12} lg={4}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                التنبيهات الأخيرة
              </Typography>
              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                {alerts.length === 0 ? (
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                    لا توجد تنبيهات
                  </Typography>
                ) : (
                  alerts.map((alert) => (
                    <Alert
                      key={alert.id}
                      severity={alert.severity}
                      sx={{
                        mb: 1,
                        backgroundColor: `rgba(${
                          alert.severity === 'error' ? '255, 0, 64' :
                          alert.severity === 'warning' ? '255, 170, 0' :
                          '0, 204, 255'
                        }, 0.1)`,
                        border: `1px solid rgba(${
                          alert.severity === 'error' ? '255, 0, 64' :
                          alert.severity === 'warning' ? '255, 170, 0' :
                          '0, 204, 255'
                        }, 0.3)`
                      }}
                    >
                      <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                        {alert.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {alert.timestamp.toLocaleTimeString('ar-SA')}
                      </Typography>
                    </Alert>
                  ))
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* رسم بياني لموارد النظام */}
      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                موارد النظام
              </Typography>
              <Box sx={{ height: 300 }}>
                <Line data={systemChartData} options={chartOptions} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* مؤشرات موارد النظام */}
        <Grid item xs={12} lg={4}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
                حالة النظام
              </Typography>
              
              {/* المعالج */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">المعالج</Typography>
                  <Typography variant="body2">{Math.round(systemMetrics.cpu)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemMetrics.cpu}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 170, 0, 0.2)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#ffaa00'
                    }
                  }}
                />
              </Box>

              {/* الذاكرة */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">الذاكرة</Typography>
                  <Typography variant="body2">{Math.round(systemMetrics.memory)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemMetrics.memory}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(255, 107, 53, 0.2)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#ff6b35'
                    }
                  }}
                />
              </Box>

              {/* التخزين */}
              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">التخزين</Typography>
                  <Typography variant="body2">{Math.round(systemMetrics.disk)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemMetrics.disk}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(156, 39, 176, 0.2)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#9c27b0'
                    }
                  }}
                />
              </Box>

              {/* الشبكة */}
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">الشبكة</Typography>
                  <Typography variant="body2">{Math.round(systemMetrics.network)}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemMetrics.network}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: 'rgba(0, 255, 65, 0.2)',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#00ff41'
                    }
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* مؤشر حالة المراقبة */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 20,
          left: 20,
          zIndex: 1000
        }}
      >
        <Chip
          icon={isMonitoring ? <PlayArrow /> : <Pause />}
          label={isMonitoring ? 'مراقبة نشطة' : 'مراقبة متوقفة'}
          color={isMonitoring ? 'success' : 'default'}
          sx={{
            backgroundColor: isMonitoring ? 'rgba(0, 255, 65, 0.2)' : 'rgba(128, 128, 128, 0.2)',
            border: `1px solid ${isMonitoring ? '#00ff41' : '#808080'}`
          }}
        />
      </Box>
    </Box>
  );
};

export default RealTimeMonitor;
