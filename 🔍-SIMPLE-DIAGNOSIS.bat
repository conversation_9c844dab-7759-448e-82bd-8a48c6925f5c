@echo off
setlocal enabledelayedexpansion
title System Diagnosis - Cyber Sentinel Pro
color 0A

REM Immediate pause to prevent auto-close
echo System Diagnosis Tool - Cyber Sentinel Pro
echo Press any key to start diagnosis or Ctrl+C to exit...
pause >nul

cls
echo.
echo ================================================================================
echo                          SYSTEM DIAGNOSIS TOOL
echo                        Cyber Sentinel Pro Builder
echo ================================================================================
echo.

echo [INFO] Starting system diagnosis...
echo [INFO] This will check your system for Node.js and build requirements
echo.

echo [STEP 1] System Information:
echo Operating System: %OS%
echo Computer Name: %COMPUTERNAME%
echo User Name: %USERNAME%
echo Current Directory: %CD%
echo.

echo [STEP 2] Checking Node.js...
echo.

REM Check if node command exists
where node >nul 2>&1
if errorlevel 1 (
    echo [FAIL] Node.js is NOT installed
    echo.
    echo [SOLUTION] Install Node.js:
    echo 1. Go to: https://nodejs.org
    echo 2. Download LTS version (recommended)
    echo 3. Install with default settings
    echo 4. Restart Command Prompt
    echo 5. Run this diagnosis again
    echo.
    set node_status=MISSING
) else (
    echo [PASS] Node.js is installed
    echo Location: 
    where node
    echo Version: 
    node --version 2>nul
    echo.
    set node_status=OK
)

echo [STEP 3] Checking npm...
echo.

where npm >nul 2>&1
if errorlevel 1 (
    echo [FAIL] npm is NOT available
    echo.
    echo [SOLUTION] npm should come with Node.js
    echo Try reinstalling Node.js from https://nodejs.org
    echo.
    set npm_status=MISSING
) else (
    echo [PASS] npm is available
    echo Location:
    where npm
    echo Version:
    npm --version 2>nul
    echo.
    set npm_status=OK
)

echo [STEP 4] Checking project files...
echo.

if exist "package.json" (
    echo [PASS] package.json found
    set package_status=OK
) else (
    echo [FAIL] package.json NOT found
    set package_status=MISSING
)

if exist "main.js" (
    echo [PASS] main.js found
    set main_status=OK
) else (
    echo [FAIL] main.js NOT found
    set main_status=MISSING
)

if exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [PASS] Main application file found
    set app_status=OK
) else (
    echo [FAIL] Main application file NOT found
    set app_status=MISSING
)

echo.
echo [STEP 5] Testing npm connectivity...
echo.

npm ping >nul 2>&1
if errorlevel 1 (
    echo [FAIL] Cannot connect to npm registry
    echo [SOLUTION] Check internet connection and firewall settings
    set npm_connect=FAIL
) else (
    echo [PASS] npm registry connection OK
    set npm_connect=OK
)

echo.
echo ================================================================================
echo                              DIAGNOSIS RESULTS
echo ================================================================================
echo.

echo SYSTEM REQUIREMENTS CHECK:
echo.
echo Node.js Installation: !node_status!
echo npm Availability: !npm_status!
echo npm Connectivity: !npm_connect!
echo.
echo PROJECT FILES CHECK:
echo.
echo package.json: !package_status!
echo main.js: !main_status!
echo Application file: !app_status!
echo.

REM Determine overall status
set overall_status=OK
if "!node_status!"=="MISSING" set overall_status=FAIL
if "!npm_status!"=="MISSING" set overall_status=FAIL
if "!package_status!"=="MISSING" set overall_status=FAIL
if "!main_status!"=="MISSING" set overall_status=FAIL
if "!app_status!"=="MISSING" set overall_status=FAIL

echo OVERALL STATUS: !overall_status!
echo.

if "!overall_status!"=="OK" (
    echo ================================================================================
    echo                              SYSTEM READY!
    echo ================================================================================
    echo.
    echo [SUCCESS] Your system is ready for EXE conversion!
    echo.
    echo [NEXT STEPS] You can now run:
    echo 1. 🔧-FIXED-BUILD-EXE.bat (recommended)
    echo 2. ⚡-QUICK-CONVERT-TO-EXE.bat (menu options)
    echo 3. 🔧-SIMPLE-EXE-CONVERTER.bat (portable version)
    echo.
    echo [RECOMMENDATION] Use the FIXED version for best results
    echo.
) else (
    echo ================================================================================
    echo                            ISSUES DETECTED!
    echo ================================================================================
    echo.
    echo [PROBLEMS] The following issues need to be resolved:
    echo.
    
    if "!node_status!"=="MISSING" (
        echo [CRITICAL] Node.js is not installed
        echo SOLUTION: Download and install from https://nodejs.org
        echo.
    )
    
    if "!npm_status!"=="MISSING" (
        echo [CRITICAL] npm is not available
        echo SOLUTION: Reinstall Node.js (npm comes with it)
        echo.
    )
    
    if "!package_status!"=="MISSING" (
        echo [ERROR] package.json is missing
        echo SOLUTION: Make sure you have all project files
        echo.
    )
    
    if "!main_status!"=="MISSING" (
        echo [ERROR] main.js is missing
        echo SOLUTION: Make sure you have all project files
        echo.
    )
    
    if "!app_status!"=="MISSING" (
        echo [ERROR] Main application file is missing
        echo SOLUTION: Make sure you have all project files
        echo.
    )
    
    echo [ACTION REQUIRED] Fix the above issues and run diagnosis again
    echo.
)

echo ================================================================================
echo                              QUICK SOLUTIONS
echo ================================================================================
echo.

echo [SOLUTION 1] Install Node.js:
echo 1. Open: https://nodejs.org
echo 2. Download LTS version
echo 3. Install with default settings
echo 4. Restart Command Prompt as Administrator
echo 5. Run diagnosis again
echo.

echo [SOLUTION 2] Fix file permissions:
echo 1. Run Command Prompt as Administrator
echo 2. Navigate to project folder
echo 3. Run diagnosis again
echo.

echo [SOLUTION 3] Check project files:
echo 1. Make sure all files are in the same folder
echo 2. Check file names are correct
echo 3. Verify files are not corrupted
echo.

echo [SOLUTION 4] Network issues:
echo 1. Check internet connection
echo 2. Disable firewall temporarily
echo 3. Try different network
echo.

echo ================================================================================
echo.

if "!node_status!"=="MISSING" (
    echo [ACTION] Opening Node.js download page...
    start https://nodejs.org
    echo.
)

echo [INFO] Diagnosis completed.
echo [INFO] Save this information for support if needed.
echo.
echo Press any key to exit...
pause >nul
exit /b 0
