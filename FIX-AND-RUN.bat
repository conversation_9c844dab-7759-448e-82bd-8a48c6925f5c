@echo off
title 🛡️ Cyber Sentinel Pro - Fix & Run
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🛡️  CYBER SENTINEL PRO - FIX & RUN  🛡️                                   █
echo █                                                                              █
echo █     🔧 حل جميع المشاكل وتشغيل التطبيق                                      █
echo █     ✅ يعمل حتى مع مشاكل PowerShell                                        █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔧 تشخيص وحل المشاكل...
echo.

REM Set environment to use Command Prompt instead of PowerShell
set COMSPEC=%SystemRoot%\system32\cmd.exe

REM Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] ❌ ملف package.json غير موجود!
    echo [INFO] تأكد من أنك في مجلد المشروع الصحيح
    echo [INFO] المجلد الحالي: %CD%
    pause
    exit /b 1
)

echo [✅] ملف package.json موجود
echo.

REM Check Node.js using where command (works in cmd)
echo [CHECK] فحص Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] ❌ Node.js غير مثبت أو غير موجود في PATH!
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo 🌐 https://nodejs.org/
    echo.
    echo ⚡ اختر النسخة LTS وتأكد من إضافة Node.js إلى PATH
    echo.
    pause
    exit /b 1
)

REM Get Node version using cmd
for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
if "%NODE_VERSION%"=="" (
    echo [ERROR] ❌ لا يمكن الحصول على إصدار Node.js
    pause
    exit /b 1
)

echo [✅] Node.js مثبت - الإصدار: %NODE_VERSION%

REM Check npm using direct path
echo [CHECK] فحص npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] ⚠️ npm غير متوفر في PATH، سنحاول استخدام npx
    set USE_NPX=1
) else (
    set USE_NPX=0
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
    if "!NPM_VERSION!"=="" (
        echo [WARNING] ⚠️ مشكلة في npm، سنحاول استخدام npx
        set USE_NPX=1
    ) else (
        echo [✅] npm متوفر - الإصدار: !NPM_VERSION!
    )
)

echo.

REM Create a simple package.json if it's corrupted
echo [CHECK] فحص package.json...
findstr "react" package.json >nul 2>&1
if %errorlevel% neq 0 (
    echo [FIX] إصلاح package.json...
    echo {> package.json.tmp
    echo   "name": "cyber-sentinel-pro",>> package.json.tmp
    echo   "version": "1.0.0",>> package.json.tmp
    echo   "private": true,>> package.json.tmp
    echo   "dependencies": {>> package.json.tmp
    echo     "react": "^18.2.0",>> package.json.tmp
    echo     "react-dom": "^18.2.0",>> package.json.tmp
    echo     "react-scripts": "5.0.1",>> package.json.tmp
    echo     "@mui/material": "^5.14.20",>> package.json.tmp
    echo     "@mui/icons-material": "^5.14.19",>> package.json.tmp
    echo     "@emotion/react": "^11.11.1",>> package.json.tmp
    echo     "@emotion/styled": "^11.11.0",>> package.json.tmp
    echo     "react-router-dom": "^6.20.1">> package.json.tmp
    echo   },>> package.json.tmp
    echo   "scripts": {>> package.json.tmp
    echo     "start": "react-scripts start",>> package.json.tmp
    echo     "build": "react-scripts build",>> package.json.tmp
    echo     "test": "react-scripts test",>> package.json.tmp
    echo     "eject": "react-scripts eject">> package.json.tmp
    echo   },>> package.json.tmp
    echo   "browserslist": {>> package.json.tmp
    echo     "production": [">0.2%%", "not dead", "not op_mini all"],>> package.json.tmp
    echo     "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]>> package.json.tmp
    echo   }>> package.json.tmp
    echo }>> package.json.tmp
    
    move package.json.tmp package.json
    echo [✅] تم إصلاح package.json
)

REM Clean install
echo [CLEAN] تنظيف التثبيت السابق...
if exist "node_modules" (
    echo [INFO] حذف node_modules القديم...
    rmdir /s /q node_modules 2>nul
)

if exist "package-lock.json" (
    echo [INFO] حذف package-lock.json...
    del package-lock.json 2>nul
)

echo.
echo [INSTALL] تثبيت التبعيات...
echo [INFO] قد يستغرق هذا عدة دقائق...
echo.

REM Try different installation methods
if %USE_NPX%==1 (
    echo [INFO] استخدام npx للتثبيت...
    npx create-react-app . --template typescript --use-npm
    if %errorlevel% neq 0 (
        echo [ERROR] فشل التثبيت باستخدام npx
        goto :manual_install
    )
) else (
    echo [INFO] استخدام npm للتثبيت...
    npm install --no-optional --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo [WARNING] فشل npm install، جاري المحاولة بطريقة أخرى...
        goto :manual_install
    )
)

goto :check_installation

:manual_install
echo.
echo [MANUAL] تثبيت يدوي للتبعيات الأساسية...
echo.

REM Manual installation of core packages
echo [INSTALL] تثبيت React...
npm install react@18.2.0 react-dom@18.2.0 --save --no-optional
if %errorlevel% neq 0 (
    echo [ERROR] فشل في تثبيت React
    goto :error_exit
)

echo [INSTALL] تثبيت React Scripts...
npm install react-scripts@5.0.1 --save --no-optional
if %errorlevel% neq 0 (
    echo [ERROR] فشل في تثبيت React Scripts
    goto :error_exit
)

echo [INSTALL] تثبيت Material-UI...
npm install @mui/material@5.14.20 @mui/icons-material@5.14.19 --save --no-optional
if %errorlevel% neq 0 (
    echo [WARNING] فشل في تثبيت Material-UI، سنتابع بدونه
)

:check_installation
echo.
echo [VERIFY] التحقق من التثبيت...

if not exist "node_modules" (
    echo [ERROR] ❌ فشل في إنشاء node_modules
    goto :error_exit
)

if not exist "node_modules\react" (
    echo [ERROR] ❌ React غير مثبت
    goto :error_exit
)

if not exist "node_modules\react-scripts" (
    echo [ERROR] ❌ React Scripts غير مثبت
    goto :error_exit
)

echo [✅] التحقق من التثبيت نجح!
echo.

REM Create basic src files if missing
echo [CHECK] فحص ملفات src...

if not exist "src" mkdir src

if not exist "src\index.js" (
    echo [CREATE] إنشاء src\index.js...
    echo import React from 'react'; > src\index.js
    echo import ReactDOM from 'react-dom/client'; >> src\index.js
    echo import App from './App'; >> src\index.js
    echo. >> src\index.js
    echo const root = ReactDOM.createRoot(document.getElementById('root'^)^); >> src\index.js
    echo root.render(^<App /^>^); >> src\index.js
)

if not exist "src\App.js" (
    echo [CREATE] إنشاء src\App.js...
    echo import React from 'react'; > src\App.js
    echo. >> src\App.js
    echo function App(^) { >> src\App.js
    echo   return ( >> src\App.js
    echo     ^<div style={{padding: '20px', textAlign: 'center'}}^> >> src\App.js
    echo       ^<h1^>🛡️ Cyber Sentinel Pro^</h1^> >> src\App.js
    echo       ^<p^>Advanced Cybersecurity Testing Platform^</p^> >> src\App.js
    echo       ^<p^>Login: admin / JaMaL@123^</p^> >> src\App.js
    echo     ^</div^> >> src\App.js
    echo   ^); >> src\App.js
    echo } >> src\App.js
    echo. >> src\App.js
    echo export default App; >> src\App.js
)

if not exist "public" mkdir public

if not exist "public\index.html" (
    echo [CREATE] إنشاء public\index.html...
    echo ^<!DOCTYPE html^> > public\index.html
    echo ^<html lang="en"^> >> public\index.html
    echo ^<head^> >> public\index.html
    echo   ^<meta charset="utf-8" /^> >> public\index.html
    echo   ^<meta name="viewport" content="width=device-width, initial-scale=1" /^> >> public\index.html
    echo   ^<title^>Cyber Sentinel Pro^</title^> >> public\index.html
    echo ^</head^> >> public\index.html
    echo ^<body^> >> public\index.html
    echo   ^<div id="root"^>^</div^> >> public\index.html
    echo ^</body^> >> public\index.html
    echo ^</html^> >> public\index.html
)

echo [✅] جميع الملفات الأساسية جاهزة!
echo.

echo ===============================================================================
echo                              🎯 READY TO LAUNCH
echo ===============================================================================
echo.
echo ✅ تم حل جميع المشاكل!
echo ✅ التطبيق جاهز للتشغيل!
echo.
echo 🔑 بيانات الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: JaMaL@123
echo.
echo 🌐 سيفتح التطبيق على: http://localhost:3000
echo.
echo ===============================================================================
echo.

echo [🚀] بدء تشغيل Cyber Sentinel Pro...
echo [ℹ️] للإيقاف: اضغط Ctrl+C
echo.

REM Open browser
start "" cmd /c "timeout /t 10 /nobreak >nul && start http://localhost:3000"

REM Start the application
npm start
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] فشل في تشغيل التطبيق
    echo [INFO] جاري المحاولة بطريقة أخرى...
    npx react-scripts start
)

goto :end

:error_exit
echo.
echo ===============================================================================
echo                              ❌ ERROR OCCURRED
echo ===============================================================================
echo.
echo [ERROR] حدث خطأ في التثبيت أو التشغيل
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من تثبيت Node.js من https://nodejs.org/
echo 2. أعد تشغيل Command Prompt كمدير
echo 3. تأكد من اتصال الإنترنت
echo 4. جرب حذف المجلد وإعادة إنشائه
echo.
echo 📧 للدعم: <EMAIL>
echo.
pause
exit /b 1

:end
echo.
echo ===============================================================================
echo                              ✅ APPLICATION STOPPED
echo ===============================================================================
echo.
echo [✅] تم إيقاف Cyber Sentinel Pro
echo [📧] للدعم: <EMAIL>
echo.
pause
