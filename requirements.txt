# Cyber Sentinel Pro - Python Dependencies
# Security and Network Analysis Tools

# Core networking and security
python-nmap==0.7.1
scapy==2.5.0
requests==2.31.0
urllib3==2.0.7

# Web application security testing
sqlmap==1.7.11
beautifulsoup4==4.12.2
selenium==4.15.2
paramiko==3.3.1

# Vulnerability scanning
python-libnmap==0.7.3
vulners==2.0.9
shodan==1.30.1
censys==2.2.8

# Cryptography and hashing
cryptography==41.0.7
hashlib-compat==1.0.1
bcrypt==4.1.2
pycryptodome==3.19.0

# Network analysis
netaddr==0.9.0
ipaddress==1.0.23
dnspython==2.4.2
pypcap==1.2.3

# Web scraping and analysis
scrapy==2.11.0
lxml==4.9.3
html5lib==1.1

# Database connectivity
psycopg2-binary==2.9.9
pymongo==4.6.0
redis==5.0.1
sqlalchemy==2.0.23

# API and web frameworks
flask==3.0.0
fastapi==0.104.1
uvicorn==0.24.0
aiohttp==3.9.1

# Data processing and analysis
pandas==2.1.4
numpy==1.25.2
matplotlib==3.8.2
seaborn==0.13.0

# Report generation
reportlab==4.0.7
jinja2==3.1.2
weasyprint==60.2
fpdf2==2.7.6

# Email and notifications
smtplib2==0.2.1
email-validator==2.1.0
twilio==8.11.0

# System monitoring
psutil==5.9.6
py-cpuinfo==9.0.0
GPUtil==1.4.0

# Logging and debugging
loguru==0.7.2
colorlog==6.8.0
rich==13.7.0

# Configuration and environment
python-dotenv==1.0.0
configparser==6.0.0
pyyaml==6.0.1

# Testing and quality assurance
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# Async and concurrency
asyncio==3.4.3
aiofiles==23.2.1
concurrent-futures==3.1.1

# Machine learning for threat detection
scikit-learn==1.3.2
tensorflow==2.15.0
torch==2.1.1

# Image and QR code processing
pillow==10.1.0
qrcode==7.4.2
opencv-python==********

# Wireless security testing
pywifi==1.1.12
wifi==0.3.8

# Mobile security (Android)
frida==16.1.4
androguard==4.1.0

# Reverse engineering
capstone==5.0.1
keystone-engine==0.9.2
unicorn==2.0.1.post1

# Social engineering toolkit
social-engineer-toolkit==8.0.3

# Metasploit integration
pymetasploit3==1.0.3

# Additional security tools
yara-python==4.3.1
pefile==2023.2.7
python-magic==0.4.27

# Performance optimization
cython==3.0.6
numba==0.58.1

# Development utilities
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6
