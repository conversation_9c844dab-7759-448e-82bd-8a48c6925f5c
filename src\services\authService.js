import axios from 'axios';
import CryptoJS from 'crypto-js';

// إعدادات الخدمة
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
const ENCRYPTION_KEY = 'CyberSentinel-Client-Encryption-2024';

// إنشاء instance من axios مع إعدادات الأمان
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
});

// Interceptor للطلبات - إضافة رمز المصادقة
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('cybersentinel_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // إضافة timestamp لمنع replay attacks
    config.headers['X-Timestamp'] = Date.now();
    
    // إضافة fingerprint للجهاز
    config.headers['X-Device-Fingerprint'] = generateDeviceFingerprint();
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor للاستجابات - معالجة الأخطاء
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // رمز المصادقة منتهي الصلاحية
      localStorage.removeItem('cybersentinel_token');
      localStorage.removeItem('cybersentinel_session');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// توليد بصمة الجهاز
const generateDeviceFingerprint = () => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillText('CyberSentinel Device Fingerprint', 2, 2);
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|');
  
  return CryptoJS.SHA256(fingerprint).toString();
};

// تشفير البيانات الحساسة
const encryptData = (data) => {
  return CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
};

// فك تشفير البيانات
const decryptData = (encryptedData) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  } catch (error) {
    console.error('خطأ في فك التشفير:', error);
    return null;
  }
};

// التحقق من قوة كلمة المرور
const validatePasswordStrength = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [
    password.length >= minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar
  ].filter(Boolean).length;
  
  return {
    isValid: score >= 4,
    score: score * 20,
    requirements: {
      minLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar
    }
  };
};

// خدمة المصادقة
export const authService = {
  // تسجيل الدخول
  async login(credentials) {
    try {
      // التحقق من صحة البيانات
      if (!credentials.username || !credentials.password) {
        return {
          success: false,
          error: 'اسم المستخدم وكلمة المرور مطلوبان'
        };
      }

      // تشفير كلمة المرور قبل الإرسال
      const encryptedCredentials = {
        username: credentials.username,
        password: encryptData(credentials.password),
        twoFactorCode: credentials.twoFactorCode,
        deviceFingerprint: generateDeviceFingerprint()
      };

      const response = await apiClient.post('/auth/login', encryptedCredentials);
      
      if (response.data.requiresTwoFactor) {
        return {
          success: false,
          requiresTwoFactor: true,
          message: response.data.message
        };
      }

      // حفظ بيانات الجلسة بشكل آمن
      if (response.data.token) {
        const encryptedToken = encryptData(response.data.token);
        localStorage.setItem('cybersentinel_token', response.data.token);
        localStorage.setItem('cybersentinel_session', response.data.sessionId);
        localStorage.setItem('cybersentinel_user', encryptData(response.data.user));
      }

      return {
        success: true,
        token: response.data.token,
        sessionId: response.data.sessionId,
        user: response.data.user,
        message: response.data.message
      };

    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      
      if (error.response?.data?.error) {
        return {
          success: false,
          error: error.response.data.error
        };
      }
      
      return {
        success: false,
        error: 'خطأ في الاتصال بالخادم'
      };
    }
  },

  // تسجيل حساب جديد
  async register(userData) {
    try {
      // التحقق من قوة كلمة المرور
      const passwordValidation = validatePasswordStrength(userData.password);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          error: 'كلمة المرور لا تلبي متطلبات الأمان'
        };
      }

      // تشفير البيانات الحساسة
      const encryptedUserData = {
        ...userData,
        password: encryptData(userData.password),
        email: encryptData(userData.email),
        phone: encryptData(userData.phone),
        deviceFingerprint: generateDeviceFingerprint()
      };

      const response = await apiClient.post('/auth/register', encryptedUserData);
      
      return {
        success: true,
        message: response.data.message,
        qrCode: response.data.qrCode,
        manualEntryKey: response.data.manualEntryKey,
        verificationRequired: response.data.verificationRequired
      };

    } catch (error) {
      console.error('خطأ في التسجيل:', error);
      
      if (error.response?.data?.error) {
        return {
          success: false,
          error: error.response.data.error
        };
      }
      
      return {
        success: false,
        error: 'خطأ في الاتصال بالخادم'
      };
    }
  },

  // تسجيل الخروج
  async logout(token, sessionId) {
    try {
      await apiClient.post('/auth/logout', { sessionId });
      
      // مسح البيانات المحلية
      localStorage.removeItem('cybersentinel_token');
      localStorage.removeItem('cybersentinel_session');
      localStorage.removeItem('cybersentinel_user');
      
      return { success: true };
      
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      return { success: false, error: error.message };
    }
  },

  // التحقق من صحة الرمز المميز
  async validateToken(token) {
    try {
      const response = await apiClient.get('/auth/validate', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return response.data.valid;
      
    } catch (error) {
      console.error('خطأ في التحقق من الرمز:', error);
      return false;
    }
  },

  // الحصول على ملف المستخدم
  async getUserProfile(token) {
    try {
      const response = await apiClient.get('/user/profile', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return response.data;
      
    } catch (error) {
      console.error('خطأ في الحصول على ملف المستخدم:', error);
      throw error;
    }
  },

  // تحديث ملف المستخدم
  async updateProfile(profileData) {
    try {
      // تشفير البيانات الحساسة
      const encryptedData = {
        ...profileData,
        email: profileData.email ? encryptData(profileData.email) : undefined,
        phone: profileData.phone ? encryptData(profileData.phone) : undefined
      };

      const response = await apiClient.put('/user/profile', encryptedData);
      
      return {
        success: true,
        user: response.data.user,
        message: response.data.message
      };
      
    } catch (error) {
      console.error('خطأ في تحديث الملف الشخصي:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'خطأ في الخادم'
      };
    }
  },

  // تغيير كلمة المرور
  async changePassword(currentPassword, newPassword) {
    try {
      // التحقق من قوة كلمة المرور الجديدة
      const passwordValidation = validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        return {
          success: false,
          error: 'كلمة المرور الجديدة لا تلبي متطلبات الأمان'
        };
      }

      const encryptedData = {
        currentPassword: encryptData(currentPassword),
        newPassword: encryptData(newPassword)
      };

      const response = await apiClient.put('/user/change-password', encryptedData);
      
      return {
        success: true,
        message: response.data.message
      };
      
    } catch (error) {
      console.error('خطأ في تغيير كلمة المرور:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'خطأ في الخادم'
      };
    }
  },

  // الحصول على المستخدم المحفوظ محلياً
  getStoredUser() {
    try {
      const encryptedUser = localStorage.getItem('cybersentinel_user');
      if (encryptedUser) {
        return decryptData(encryptedUser);
      }
      return null;
    } catch (error) {
      console.error('خطأ في قراءة بيانات المستخدم:', error);
      return null;
    }
  },

  // التحقق من حالة الجلسة
  async checkSession() {
    try {
      const token = localStorage.getItem('cybersentinel_token');
      const sessionId = localStorage.getItem('cybersentinel_session');
      
      if (!token || !sessionId) {
        return { valid: false };
      }

      const response = await apiClient.get('/auth/session-status', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      return {
        valid: true,
        user: response.data.user,
        expiresAt: response.data.expiresAt
      };
      
    } catch (error) {
      console.error('خطأ في التحقق من الجلسة:', error);
      return { valid: false };
    }
  }
};
