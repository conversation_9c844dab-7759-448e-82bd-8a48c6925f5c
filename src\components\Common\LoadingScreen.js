import React, { useState, useEffect } from 'react';
import { Box, Typography, LinearProgress, Fade } from '@mui/material';
import { Shield, Security, VpnLock, Verified } from '@mui/icons-material';

const LoadingScreen = () => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [loadingText, setLoadingText] = useState('');

  const loadingSteps = [
    {
      icon: <Shield sx={{ fontSize: 40, color: '#00ff41' }} />,
      text: 'تهيئة نظام الحماية...',
      duration: 800
    },
    {
      icon: <Security sx={{ fontSize: 40, color: '#00ccff' }} />,
      text: 'تحميل أدوات الأمان السيبراني...',
      duration: 1000
    },
    {
      icon: <VpnLock sx={{ fontSize: 40, color: '#ff6b35' }} />,
      text: 'تشفير قنوات الاتصال...',
      duration: 700
    },
    {
      icon: <Verified sx={{ fontSize: 40, color: '#00ff41' }} />,
      text: 'التحقق من سلامة النظام...',
      duration: 900
    }
  ];

  useEffect(() => {
    let progressTimer;
    let stepTimer;
    let currentProgress = 0;
    let stepIndex = 0;

    const updateProgress = () => {
      if (currentProgress < 100) {
        currentProgress += Math.random() * 3 + 1;
        if (currentProgress > 100) currentProgress = 100;
        setProgress(currentProgress);

        // تحديث الخطوة الحالية
        const newStepIndex = Math.floor((currentProgress / 100) * loadingSteps.length);
        if (newStepIndex !== stepIndex && newStepIndex < loadingSteps.length) {
          stepIndex = newStepIndex;
          setCurrentStep(stepIndex);
          setLoadingText(loadingSteps[stepIndex].text);
        }

        progressTimer = setTimeout(updateProgress, 100);
      }
    };

    // بدء التحميل
    updateProgress();
    setLoadingText(loadingSteps[0].text);

    return () => {
      if (progressTimer) clearTimeout(progressTimer);
      if (stepTimer) clearTimeout(stepTimer);
    };
  }, []);

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
        overflow: 'hidden'
      }}
    >
      {/* خلفية Matrix متحركة */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          opacity: 0.1,
          pointerEvents: 'none'
        }}
      >
        {[...Array(50)].map((_, i) => (
          <Box
            key={i}
            sx={{
              position: 'absolute',
              left: `${Math.random() * 100}%`,
              color: '#00ff41',
              fontFamily: 'Roboto Mono, monospace',
              fontSize: '12px',
              animation: `matrix-fall ${Math.random() * 3 + 2}s linear infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          >
            {Math.random() > 0.5 ? '1' : '0'}
          </Box>
        ))}
      </Box>

      {/* المحتوى الرئيسي */}
      <Box
        sx={{
          textAlign: 'center',
          zIndex: 1,
          maxWidth: 500,
          width: '90%'
        }}
      >
        {/* الشعار */}
        <Fade in timeout={1000}>
          <Box sx={{ mb: 4 }}>
            <Shield
              sx={{
                fontSize: 80,
                color: '#00ff41',
                mb: 2,
                filter: 'drop-shadow(0 0 20px #00ff41)',
                animation: 'cyber-pulse 2s ease-in-out infinite alternate'
              }}
            />
            <Typography
              variant="h3"
              component="h1"
              sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #00ff41, #00ccff)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 0 30px rgba(0, 255, 65, 0.5)',
                mb: 1
              }}
            >
              CYBER SENTINEL PRO
            </Typography>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{ opacity: 0.8 }}
            >
              SecOps Edition
            </Typography>
          </Box>
        </Fade>

        {/* أيقونة الخطوة الحالية */}
        <Fade in timeout={500} key={currentStep}>
          <Box sx={{ mb: 3 }}>
            {loadingSteps[currentStep]?.icon}
          </Box>
        </Fade>

        {/* نص التحميل */}
        <Typography
          variant="h6"
          sx={{
            mb: 4,
            color: '#00ccff',
            fontWeight: 500,
            minHeight: '32px'
          }}
        >
          {loadingText}
        </Typography>

        {/* شريط التقدم */}
        <Box sx={{ width: '100%', mb: 2 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'rgba(0, 255, 65, 0.1)',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #00ff41, #00ccff)',
                borderRadius: 4,
                boxShadow: '0 0 10px rgba(0, 255, 65, 0.5)'
              }
            }}
          />
        </Box>

        {/* نسبة التقدم */}
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ mb: 4 }}
        >
          {Math.round(progress)}%
        </Typography>

        {/* مؤشرات الخطوات */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            gap: 1,
            mb: 4
          }}
        >
          {loadingSteps.map((step, index) => (
            <Box
              key={index}
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                backgroundColor: index <= currentStep ? '#00ff41' : 'rgba(255, 255, 255, 0.2)',
                transition: 'all 0.3s ease',
                boxShadow: index <= currentStep ? '0 0 10px #00ff41' : 'none'
              }}
            />
          ))}
        </Box>

        {/* رسالة الأمان */}
        <Box
          sx={{
            p: 2,
            backgroundColor: 'rgba(0, 255, 65, 0.05)',
            border: '1px solid rgba(0, 255, 65, 0.2)',
            borderRadius: 2,
            backdropFilter: 'blur(10px)'
          }}
        >
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ display: 'block', textAlign: 'center' }}
          >
            🔒 تشفير AES-256 | 🛡️ حماية متقدمة | 🌐 اتصال آمن
          </Typography>
        </Box>
      </Box>

      {/* تحذير قانوني */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          textAlign: 'center',
          maxWidth: '80%'
        }}
      >
        <Typography
          variant="caption"
          color="error.main"
          sx={{
            backgroundColor: 'rgba(255, 0, 64, 0.1)',
            border: '1px solid rgba(255, 0, 64, 0.3)',
            padding: '8px 16px',
            borderRadius: 2,
            display: 'inline-block'
          }}
        >
          ⚠️ تحذير: هذا البرنامج مخصص لاختبار الأمان المصرح به فقط
        </Typography>
      </Box>

      {/* معلومات الإصدار */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          textAlign: 'right'
        }}
      >
        <Typography variant="caption" color="text.secondary">
          الإصدار 1.0.0
        </Typography>
        <br />
        <Typography variant="caption" color="text.secondary">
          © 2024 CyberSentinel Team
        </Typography>
      </Box>

      <style jsx>{`
        @keyframes matrix-fall {
          0% {
            transform: translateY(-100vh);
            opacity: 0;
          }
          10% {
            opacity: 1;
          }
          90% {
            opacity: 1;
          }
          100% {
            transform: translateY(100vh);
            opacity: 0;
          }
        }

        @keyframes cyber-pulse {
          from {
            filter: drop-shadow(0 0 20px #00ff41);
          }
          to {
            filter: drop-shadow(0 0 40px #00ff41) drop-shadow(0 0 60px #00ff41);
          }
        }
      `}</style>
    </Box>
  );
};

export default LoadingScreen;
