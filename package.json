{"name": "cyber-sentinel-pro", "version": "1.0.0", "description": "🛡️ Cyber Sentinel Pro - Professional Edition - أداة أمان سيبراني متقدمة", "main": "main.js", "author": "CyberSentinel Team", "license": "MIT", "homepage": "https://cybersentinel.pro", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "build": {"appId": "com.cybersentinel.pro", "productName": "Cyber Sentinel Pro", "directories": {"output": "dist"}, "files": ["main.js", "🛡️-CYBER-SENTINEL-PROFESSIONAL.html", "📖-PROFESSIONAL-EDITION-GUIDE.txt", "🚀-START-HERE.txt", "🔒-SECURITY-REPORT.txt", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Cyber Sentinel Pro"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-updater": "^6.1.4"}}