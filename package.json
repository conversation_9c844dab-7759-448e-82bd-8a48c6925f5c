{"name": "cyber-sentinel-pro", "version": "1.0.0", "description": "Cyber Sentinel Pro - SecOps Edition - Advanced Cybersecurity Testing Platform", "main": "src/main.js", "homepage": "./", "author": "CyberSentinel Team", "license": "Proprietary", "private": true, "scripts": {"start": "electron .", "dev": "concurrently \"npm run server\" \"npm run electron-dev\"", "electron-dev": "ELECTRON_IS_DEV=true electron .", "server": "nodemon server/index.js", "build": "npm run build-react && electron-builder", "build-react": "react-scripts build", "build-windows": "electron-builder --windows", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "test": "react-scripts test", "eject": "react-scripts eject"}, "build": {"appId": "com.cybersentinel.pro", "productName": "Cyber Sentinel Pro", "directories": {"output": "dist"}, "files": ["build/**/*", "src/main.js", "server/**/*", "public/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico", "requestedExecutionLevel": "requireAdministrator"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@tensorflow/tfjs": "^4.15.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "chart.js": "^4.4.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "electron": "^27.1.3", "electron-store": "^8.1.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "node-2fa": "^2.0.3", "nodemailer": "^6.9.7", "pg": "^8.11.3", "qrcode": "^1.5.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "speakeasy": "^2.0.0", "three": "^0.159.0", "uuid": "^9.0.1", "web-vitals": "^3.5.0"}, "devDependencies": {"concurrently": "^8.2.2", "electron-builder": "^24.8.1", "nodemon": "^3.0.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}