@echo off
title 🔧 تحويل Cyber Sentinel Pro إلى EXE
color 0A
chcp 65001 >nul

echo.
echo ████████████████████████████████████████████████████████████████████████████████
echo █                                                                              █
echo █  🔧  تحويل CYBER SENTINEL PRO إلى ملف EXE قابل للتشغيل  🔧                  █
echo █                                                                              █
echo █     ✅ تحويل التطبيق إلى برنامج مستقل                                       █
echo █     ✅ إنشاء ملف تثبيت احترافي                                              █
echo █     ✅ دعم جميع أنظمة التشغيل                                               █
echo █                                                                              █
echo ████████████████████████████████████████████████████████████████████████████████
echo.

echo [INFO] 🔧 بدء عملية تحويل التطبيق إلى EXE...
echo.

REM Check if Node.js is installed
echo [STEP 1] 🔍 فحص Node.js...
echo [DEBUG] جاري فحص وجود Node.js...

REM Test Node.js with detailed output
node --version 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] ⚠️ Node.js غير مثبت على النظام
    echo.
    echo [INFO] 📥 تحتاج إلى تثبيت Node.js أولاً:
    echo    1. اذهب إلى: https://nodejs.org
    echo    2. حمل النسخة LTS (الموصى بها)
    echo    3. ثبت Node.js
    echo    4. أعد تشغيل Command Prompt كمدير
    echo    5. أعد تشغيل هذا الملف
    echo.
    echo [ACTION] 🌐 فتح موقع Node.js...
    start https://nodejs.org
    echo.
    echo [INFO] بعد تثبيت Node.js، اضغط أي مفتاح للمتابعة...
    pause
    exit /b 1
) else (
    echo [✅] Node.js مثبت بنجاح
    echo [INFO] إصدار Node.js:
    node --version
    echo.
)

echo [STEP 2] 📦 فحص npm...
echo [DEBUG] جاري فحص وجود npm...

REM Test npm with detailed output
npm --version 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] ❌ npm غير متاح
    echo [INFO] npm يأتي عادة مع Node.js
    echo [INFO] جرب إعادة تثبيت Node.js
    echo.
    echo [DEBUG] معلومات إضافية للتشخيص:
    echo PATH=%PATH%
    echo.
    echo [INFO] اضغط أي مفتاح للخروج...
    pause
    exit /b 1
) else (
    echo [✅] npm متاح
    echo [INFO] إصدار npm:
    npm --version
    echo.
)

echo.
echo [STEP 3] 📋 فحص ملفات المشروع...

REM Check required files
set "missing_files="

if not exist "package.json" (
    echo [ERROR] ❌ ملف package.json مفقود
    set "missing_files=1"
)

if not exist "main.js" (
    echo [ERROR] ❌ ملف main.js مفقود
    set "missing_files=1"
)

if not exist "🛡️-CYBER-SENTINEL-PROFESSIONAL.html" (
    echo [ERROR] ❌ ملف التطبيق الرئيسي مفقود
    set "missing_files=1"
)

if defined missing_files (
    echo.
    echo [ERROR] ❌ ملفات مطلوبة مفقودة!
    echo [INFO] تأكد من وجود جميع الملفات في نفس المجلد
    pause
    exit /b 1
)

echo [✅] جميع الملفات المطلوبة موجودة

echo [STEP 4] 📥 تثبيت المتطلبات...
echo [INFO] جاري تثبيت Electron و Electron Builder...
echo [DEBUG] هذه العملية قد تستغرق عدة دقائق...
echo.

REM Initialize npm if package.json doesn't exist or is corrupted
if not exist "node_modules" (
    echo [INFO] إنشاء مجلد node_modules...
    mkdir node_modules 2>nul
)

echo [INFO] تثبيت المتطلبات... يرجى الانتظار...
npm install electron electron-builder electron-updater --save-dev --verbose
set npm_error=%errorlevel%

if %npm_error% neq 0 (
    echo.
    echo [ERROR] ❌ فشل في تثبيت المتطلبات
    echo [DEBUG] رمز الخطأ: %npm_error%
    echo.
    echo [INFO] 🔧 حلول مقترحة:
    echo    1. تأكد من اتصال الإنترنت
    echo    2. شغل Command Prompt كمدير
    echo    3. جرب الأمر يدوياً: npm install
    echo    4. امسح مجلد node_modules وأعد المحاولة
    echo    5. تحقق من إعدادات Firewall/Antivirus
    echo.
    echo [DEBUG] للتشخيص المتقدم، جرب:
    echo    npm config list
    echo    npm cache clean --force
    echo.
    echo [INFO] اضغط أي مفتاح للخروج...
    pause
    exit /b 1
)

echo.
echo [✅] تم تثبيت المتطلبات بنجاح

echo.
echo [STEP 5] 🔧 بناء التطبيق...
echo [INFO] جاري إنشاء ملف EXE...
echo [DEBUG] هذه العملية قد تستغرق 5-10 دقائق...
echo.

REM First try the npm script
echo [INFO] محاولة البناء باستخدام npm script...
npm run build-win 2>&1
set build_error=%errorlevel%

if %build_error% neq 0 (
    echo.
    echo [WARNING] ⚠️ فشل في البناء التلقائي، جاري المحاولة بطريقة أخرى...
    echo [DEBUG] رمز خطأ npm: %build_error%
    echo.

    REM Try alternative build with npx
    echo [INFO] محاولة البناء باستخدام npx...
    npx electron-builder --win --verbose 2>&1
    set npx_error=%errorlevel%

    if %npx_error% neq 0 (
        echo.
        echo [ERROR] ❌ فشل في بناء التطبيق
        echo [DEBUG] رمز خطأ npx: %npx_error%
        echo.
        echo [INFO] 🔧 حلول مقترحة:
        echo    1. تحقق من وجود جميع الملفات المطلوبة
        echo    2. تأكد من صحة ملف package.json
        echo    3. جرب حذف node_modules وإعادة التثبيت
        echo    4. تحقق من مساحة القرص المتاحة
        echo    5. أغلق برامج الحماية مؤقتاً
        echo.
        echo [DEBUG] للتشخيص المتقدم:
        echo    - تحقق من سجل الأخطاء أعلاه
        echo    - جرب: npm run build-win --verbose
        echo    - تأكد من وجود ملف main.js
        echo.
        echo [INFO] اضغط أي مفتاح للخروج...
        pause
        exit /b 1
    )
)

echo.
echo [✅] تم بناء التطبيق بنجاح!

echo.
echo [STEP 6] 📁 فحص الملفات المُنشأة...

if exist "dist" (
    echo [✅] مجلد dist موجود
    
    REM List contents of dist folder
    echo [INFO] محتويات مجلد dist:
    dir dist /b
    
    REM Check for exe file
    for /r dist %%i in (*.exe) do (
        echo [✅] تم العثور على ملف EXE: %%~nxi
        echo [PATH] المسار: %%i
    )
    
    REM Check for installer
    for /r dist %%i in (*.msi *.nsis) do (
        echo [✅] تم العثور على ملف التثبيت: %%~nxi
        echo [PATH] المسار: %%i
    )
    
) else (
    echo [WARNING] ⚠️ مجلد dist غير موجود
    echo [INFO] قد يكون التطبيق في مجلد آخر
)

echo.
echo ===============================================================================
echo                              ✅ اكتمل التحويل بنجاح!
echo ===============================================================================
echo.
echo 🎉 تم تحويل Cyber Sentinel Pro إلى ملف EXE بنجاح!
echo.
echo 📁 الملفات المُنشأة:
echo    📂 مجلد dist - يحتوي على جميع الملفات
echo    💻 ملف EXE - البرنامج القابل للتشغيل
echo    📦 ملف التثبيت - لتوزيع البرنامج
echo.
echo 🚀 طرق التشغيل:
echo    1️⃣ تشغيل مباشر: انقر على ملف EXE
echo    2️⃣ تثبيت: استخدم ملف التثبيت
echo    3️⃣ توزيع: شارك ملف التثبيت مع الآخرين
echo.
echo 📋 مميزات النسخة المحولة:
echo    ✅ برنامج مستقل - لا يحتاج متصفح
echo    ✅ أيقونة مخصصة - في شريط المهام
echo    ✅ قائمة تطبيق - مع خيارات متقدمة
echo    ✅ تحديثات تلقائية - عبر الإنترنت
echo    ✅ أمان محسن - حماية إضافية
echo    ✅ أداء أفضل - تحسينات خاصة
echo.
echo 🔧 الملفات الإضافية:
echo    📄 package.json - إعدادات المشروع
echo    📄 main.js - ملف Electron الرئيسي
echo    📁 assets - الأيقونات والموارد
echo.
echo 💡 نصائح للاستخدام:
echo    • احتفظ بنسخة من مجلد dist
echo    • اختبر البرنامج قبل التوزيع
echo    • تأكد من عمل جميع الوظائف
echo    • راجع تقرير الأمان
echo.
echo ⚠️ ملاحظات مهمة:
echo    • البرنامج يحتاج إنترنت لبعض الوظائف
echo    • تأكد من تشغيله كمدير عند الحاجة
echo    • احترم القوانين المحلية والدولية
echo.
echo 📞 الدعم الفني:
echo    📧 البريد: <EMAIL>
echo    🌐 الموقع: https://cybersentinel.pro
echo    📱 التليجرام: @CyberSentinelSupport
echo.
echo ===============================================================================
echo.
echo [INFO] يمكنك الآن إغلاق هذه النافذة
echo [INFO] ابحث عن ملف EXE في مجلد dist
echo.

REM Open dist folder if it exists
if exist "dist" (
    echo [ACTION] 📂 فتح مجلد الملفات المُنشأة...
    start dist
)

echo.
echo [INFO] يمكنك إغلاق هذه النافذة الآن
echo [INFO] ابحث عن ملف EXE في مجلد dist
echo.
echo [DEBUG] إذا واجهت مشاكل:
echo    📧 البريد: <EMAIL>
echo    📖 الدليل: 📖-EXE-CONVERSION-GUIDE.txt
echo    🧪 الاختبار: 🧪-TEST-ADMIN-PANEL.bat
echo.
echo [INFO] اضغط أي مفتاح لإنهاء البرنامج...
pause >nul
