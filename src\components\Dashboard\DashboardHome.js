import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  LinearProgress,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Security,
  NetworkCheck,
  BugReport,
  Assessment,
  TrendingUp,
  Warning,
  CheckCircle,
  Error,
  Info,
  PlayArrow,
  Refresh,
  Timeline,
  Shield,
  Computer,
  Storage
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSecurity } from '../../contexts/SecurityContext';

const DashboardHome = ({ user }) => {
  const navigate = useNavigate();
  const { 
    securityStatus, 
    threatLevel, 
    metrics, 
    alerts, 
    getThreatLevelColor,
    getThreatLevelText,
    performSecurityScan 
  } = useSecurity();

  const [systemStats, setSystemStats] = useState({
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    networkActivity: 0
  });
  const [recentScans, setRecentScans] = useState([]);
  const [isScanning, setIsScanning] = useState(false);

  // محاكاة إحصائيات النظام
  useEffect(() => {
    const updateSystemStats = () => {
      setSystemStats({
        cpuUsage: Math.floor(Math.random() * 30) + 10,
        memoryUsage: Math.floor(Math.random() * 40) + 30,
        diskUsage: Math.floor(Math.random() * 20) + 50,
        networkActivity: Math.floor(Math.random() * 50) + 20
      });
    };

    updateSystemStats();
    const interval = setInterval(updateSystemStats, 5000);
    return () => clearInterval(interval);
  }, []);

  // محاكاة الفحوصات الأخيرة
  useEffect(() => {
    setRecentScans([
      {
        id: 1,
        type: 'فحص الشبكة',
        target: '192.168.1.0/24',
        status: 'مكتمل',
        timestamp: new Date(Date.now() - 3600000),
        findings: 15
      },
      {
        id: 2,
        type: 'فحص الثغرات',
        target: 'example.com',
        status: 'مكتمل',
        timestamp: new Date(Date.now() - 7200000),
        findings: 3
      },
      {
        id: 3,
        type: 'اختبار الاختراق',
        target: '10.0.0.1',
        status: 'قيد التشغيل',
        timestamp: new Date(Date.now() - 1800000),
        findings: 0
      }
    ]);
  }, []);

  // بطاقات الإحصائيات السريعة
  const quickStats = [
    {
      title: 'حالة الأمان',
      value: securityStatus === 'secure' ? 'آمن' : 'تحذير',
      icon: <Shield />,
      color: securityStatus === 'secure' ? '#00ff41' : '#ff6600',
      action: () => performQuickScan()
    },
    {
      title: 'مستوى التهديد',
      value: getThreatLevelText(),
      icon: <Warning />,
      color: getThreatLevelColor(),
      action: () => navigate('/dashboard/vulnerability-scanner')
    },
    {
      title: 'الفحوصات اليوم',
      value: metrics.totalThreats || 0,
      icon: <NetworkCheck />,
      color: '#00ccff',
      action: () => navigate('/dashboard/network-scanner')
    },
    {
      title: 'التهديدات المحجوبة',
      value: metrics.blockedAttacks || 0,
      icon: <Security />,
      color: '#ff0040',
      action: () => navigate('/dashboard/reports')
    }
  ];

  // الأدوات السريعة
  const quickTools = [
    {
      title: 'فحص سريع للشبكة',
      description: 'فحص سريع للأجهزة المتصلة',
      icon: <NetworkCheck />,
      color: '#00ccff',
      action: () => navigate('/dashboard/network-scanner')
    },
    {
      title: 'فحص الثغرات',
      description: 'البحث عن الثغرات الأمنية',
      icon: <BugReport />,
      color: '#ff6b35',
      action: () => navigate('/dashboard/vulnerability-scanner')
    },
    {
      title: 'اختبار الاختراق',
      description: 'اختبار مقاومة النظام',
      icon: <Security />,
      color: '#ff0040',
      action: () => navigate('/dashboard/penetration-testing')
    },
    {
      title: 'إنشاء تقرير',
      description: 'تقرير شامل عن الأمان',
      icon: <Assessment />,
      color: '#ffaa00',
      action: () => navigate('/dashboard/reports')
    }
  ];

  // فحص سريع
  const performQuickScan = async () => {
    setIsScanning(true);
    try {
      await performSecurityScan();
    } catch (error) {
      console.error('خطأ في الفحص السريع:', error);
    } finally {
      setIsScanning(false);
    }
  };

  // لون شريط التقدم حسب القيمة
  const getProgressColor = (value) => {
    if (value < 50) return 'success';
    if (value < 80) return 'warning';
    return 'error';
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* ترحيب */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h4"
          component="h1"
          sx={{
            fontWeight: 'bold',
            background: 'linear-gradient(45deg, #00ff41, #00ccff)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 1
          }}
        >
          مرحباً، {user?.username}
        </Typography>
        <Typography variant="h6" color="text.secondary">
          لوحة تحكم Cyber Sentinel Pro - SecOps Edition
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* الإحصائيات السريعة */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {quickStats.map((stat, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card
                  sx={{
                    backgroundColor: 'rgba(26, 26, 46, 0.9)',
                    border: `1px solid ${stat.color}30`,
                    borderRadius: 2,
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: `0 8px 25px ${stat.color}40`
                    }
                  }}
                  onClick={stat.action}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          backgroundColor: `${stat.color}20`,
                          color: stat.color,
                          mr: 2
                        }}
                      >
                        {stat.icon}
                      </Avatar>
                      <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                        {stat.value}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* إحصائيات النظام */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Computer sx={{ color: '#00ccff', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  إحصائيات النظام
                </Typography>
                <Tooltip title="تحديث">
                  <IconButton
                    size="small"
                    sx={{ ml: 'auto', color: '#00ff41' }}
                    onClick={() => window.location.reload()}
                  >
                    <Refresh />
                  </IconButton>
                </Tooltip>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">المعالج</Typography>
                  <Typography variant="body2">{systemStats.cpuUsage}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemStats.cpuUsage}
                  color={getProgressColor(systemStats.cpuUsage)}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">الذاكرة</Typography>
                  <Typography variant="body2">{systemStats.memoryUsage}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemStats.memoryUsage}
                  color={getProgressColor(systemStats.memoryUsage)}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">التخزين</Typography>
                  <Typography variant="body2">{systemStats.diskUsage}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemStats.diskUsage}
                  color={getProgressColor(systemStats.diskUsage)}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">نشاط الشبكة</Typography>
                  <Typography variant="body2">{systemStats.networkActivity}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={systemStats.networkActivity}
                  color="info"
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* الإشعارات الأخيرة */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Warning sx={{ color: '#ffaa00', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  الإشعارات الأخيرة
                </Typography>
                <Chip
                  label={alerts.length}
                  size="small"
                  color="warning"
                  sx={{ ml: 'auto' }}
                />
              </Box>

              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {alerts.length === 0 ? (
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircle sx={{ color: '#00ff41' }} />
                    </ListItemIcon>
                    <ListItemText
                      primary="لا توجد تنبيهات"
                      secondary="النظام يعمل بشكل طبيعي"
                    />
                  </ListItem>
                ) : (
                  alerts.slice(0, 5).map((alert, index) => (
                    <ListItem key={alert.id || index}>
                      <ListItemIcon>
                        {alert.level === 'critical' ? (
                          <Error sx={{ color: '#ff0040' }} />
                        ) : alert.level === 'high' ? (
                          <Warning sx={{ color: '#ff6600' }} />
                        ) : alert.level === 'medium' ? (
                          <Warning sx={{ color: '#ffaa00' }} />
                        ) : (
                          <Info sx={{ color: '#00ccff' }} />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={alert.message}
                        secondary={new Date(alert.timestamp).toLocaleString('ar-SA')}
                      />
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* الأدوات السريعة */}
        <Grid item xs={12}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <PlayArrow sx={{ color: '#00ff41', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  الأدوات السريعة
                </Typography>
              </Box>

              <Grid container spacing={2}>
                {quickTools.map((tool, index) => (
                  <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card
                      sx={{
                        backgroundColor: 'rgba(0, 0, 0, 0.3)',
                        border: `1px solid ${tool.color}30`,
                        borderRadius: 2,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          border: `1px solid ${tool.color}60`,
                          boxShadow: `0 4px 15px ${tool.color}30`
                        }
                      }}
                      onClick={tool.action}
                    >
                      <CardContent sx={{ textAlign: 'center', py: 3 }}>
                        <Avatar
                          sx={{
                            backgroundColor: `${tool.color}20`,
                            color: tool.color,
                            width: 56,
                            height: 56,
                            margin: '0 auto 1rem'
                          }}
                        >
                          {tool.icon}
                        </Avatar>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {tool.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {tool.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* الفحوصات الأخيرة */}
        <Grid item xs={12}>
          <Card
            sx={{
              backgroundColor: 'rgba(26, 26, 46, 0.9)',
              border: '1px solid rgba(0, 255, 65, 0.3)',
              borderRadius: 2
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Timeline sx={{ color: '#00ccff', mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  الفحوصات الأخيرة
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  sx={{ ml: 'auto' }}
                  onClick={() => navigate('/dashboard/reports')}
                >
                  عرض الكل
                </Button>
              </Box>

              <List>
                {recentScans.map((scan, index) => (
                  <React.Fragment key={scan.id}>
                    <ListItem>
                      <ListItemIcon>
                        <Chip
                          label={scan.status}
                          size="small"
                          color={
                            scan.status === 'مكتمل' ? 'success' :
                            scan.status === 'قيد التشغيل' ? 'warning' : 'error'
                          }
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${scan.type} - ${scan.target}`}
                        secondary={`${scan.timestamp.toLocaleString('ar-SA')} | النتائج: ${scan.findings}`}
                      />
                    </ListItem>
                    {index < recentScans.length - 1 && (
                      <Divider sx={{ borderColor: 'rgba(0, 255, 65, 0.1)' }} />
                    )}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* زر الفحص السريع العائم */}
      <Tooltip title="فحص أمني سريع">
        <Button
          variant="contained"
          onClick={performQuickScan}
          disabled={isScanning}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            borderRadius: '50%',
            width: 64,
            height: 64,
            minWidth: 64,
            background: 'linear-gradient(45deg, #00ff41, #00ccff)',
            '&:hover': {
              background: 'linear-gradient(45deg, #00cc33, #0099cc)',
              transform: 'scale(1.1)'
            }
          }}
        >
          {isScanning ? <Refresh className="spin" /> : <Security />}
        </Button>
      </Tooltip>

      <style jsx>{`
        .spin {
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </Box>
  );
};

export default DashboardHome;
